"""
时序违例确认插件工具类

提供时间转换、格式化和验证等工具功能。
"""

import re
import os
from typing import Tuple, Optional

try:
    # 尝试相对导入（在插件环境中）
    from .exceptions import TimeConversionError
    from .models import CaseInfo, VALID_CORNERS, is_valid_corner
except ImportError:
    # 如果相对导入失败，尝试直接导入（在测试环境中）
    from exceptions import TimeConversionError
    from models import CaseInfo, VALID_CORNERS, is_valid_corner

# 尝试导入PyQt5组件（用于UI组件）
try:
    from PyQt5.QtWidgets import QComboBox, QWidget
    from PyQt5.QtCore import pyqtSignal
    PYQT5_AVAILABLE = True
except ImportError:
    PYQT5_AVAILABLE = False


class TimeConverter:
    """时间转换工具类
    
    提供时间单位识别、转换和格式化功能。
    支持的时间单位：FS (飞秒)、PS (皮秒)、NS (纳秒)
    """
    
    # 时间单位转换系数（转换为飞秒）
    TIME_UNITS = {
        'FS': 1,           # 飞秒 = 1 飞秒
        'PS': 1000,        # 皮秒 = 1000 飞秒
        'NS': 1000000,     # 纳秒 = 1000000 飞秒
    }
    
    # 时间字符串正则表达式
    TIME_PATTERN = re.compile(r'^(\d+(?:\.\d+)?)\s*(FS|PS|NS)$', re.IGNORECASE)
    
    @staticmethod
    def to_femtoseconds(time_str: str) -> int:
        """将时间字符串转换为飞秒
        
        Args:
            time_str: 时间字符串，格式如 "1000PS", "500NS", "1500FS"
            
        Returns:
            int: 转换后的飞秒值
            
        Raises:
            TimeConversionError: 当时间字符串格式不正确或转换失败时
        """
        if not time_str or not isinstance(time_str, str):
            raise TimeConversionError(str(time_str), "时间字符串不能为空")
        
        # 清理输入字符串
        time_str = time_str.strip().upper()
        
        # 使用正则表达式解析时间字符串
        match = TimeConverter.TIME_PATTERN.match(time_str)
        if not match:
            raise TimeConversionError(
                time_str, 
                "时间格式不正确，应为数字+单位格式，如：1000PS, 500NS, 1500FS"
            )
        
        try:
            # 提取数值和单位
            value_str, unit = match.groups()
            value = float(value_str)
            
            # 检查数值是否为非负数
            if value < 0:
                raise TimeConversionError(time_str, "时间值不能为负数")
            
            # 获取转换系数
            if unit not in TimeConverter.TIME_UNITS:
                raise TimeConversionError(
                    time_str, 
                    f"不支持的时间单位: {unit}，支持的单位: {', '.join(TimeConverter.TIME_UNITS.keys())}"
                )
            
            # 转换为飞秒
            conversion_factor = TimeConverter.TIME_UNITS[unit]
            femtoseconds = int(value * conversion_factor)
            
            return femtoseconds
            
        except ValueError as e:
            raise TimeConversionError(time_str, f"数值转换失败: {str(e)}")
    
    @staticmethod
    def format_time(fs_time: int, target_unit: str = None) -> str:
        """格式化时间显示
        
        Args:
            fs_time: 飞秒时间值
            target_unit: 目标显示单位，如果为None则自动选择最合适的单位
            
        Returns:
            str: 格式化后的时间字符串
            
        Raises:
            TimeConversionError: 当输入参数不正确时
        """
        if not isinstance(fs_time, int) or fs_time < 0:
            raise TimeConversionError(str(fs_time), "飞秒时间值必须为非负整数")
        
        # 如果指定了目标单位，直接转换
        if target_unit:
            target_unit = target_unit.upper()
            if target_unit not in TimeConverter.TIME_UNITS:
                raise TimeConversionError(
                    str(fs_time), 
                    f"不支持的目标单位: {target_unit}，支持的单位: {', '.join(TimeConverter.TIME_UNITS.keys())}"
                )
            
            conversion_factor = TimeConverter.TIME_UNITS[target_unit]
            value = fs_time / conversion_factor
            
            # 格式化数值显示
            if value == int(value):
                return f"{int(value)}{target_unit}"
            else:
                return f"{value:.3f}{target_unit}"
        
        # 自动选择最合适的单位
        if fs_time == 0:
            return "0FS"
        
        # 按照从大到小的顺序尝试单位
        units_ordered = [('NS', 1000000), ('PS', 1000), ('FS', 1)]
        
        for unit, factor in units_ordered:
            if fs_time >= factor:
                value = fs_time / factor
                if value == int(value):
                    return f"{int(value)}{unit}"
                else:
                    return f"{value:.3f}{unit}"
        
        # 如果都不满足，使用飞秒
        return f"{fs_time}FS"
    
    @staticmethod
    def parse_time_unit(time_str: str) -> Tuple[float, str]:
        """解析时间字符串，返回数值和单位
        
        Args:
            time_str: 时间字符串
            
        Returns:
            Tuple[float, str]: (数值, 单位)
            
        Raises:
            TimeConversionError: 当解析失败时
        """
        if not time_str or not isinstance(time_str, str):
            raise TimeConversionError(str(time_str), "时间字符串不能为空")
        
        # 清理输入字符串
        time_str = time_str.strip().upper()
        
        # 使用正则表达式解析
        match = TimeConverter.TIME_PATTERN.match(time_str)
        if not match:
            raise TimeConversionError(
                time_str, 
                "时间格式不正确，应为数字+单位格式，如：1000PS, 500NS, 1500FS"
            )
        
        try:
            value_str, unit = match.groups()
            value = float(value_str)
            
            if value < 0:
                raise TimeConversionError(time_str, "时间值不能为负数")
            
            return value, unit
            
        except ValueError as e:
            raise TimeConversionError(time_str, f"数值解析失败: {str(e)}")
    
    @staticmethod
    def compare_times(time1_fs: int, time2_fs: int) -> int:
        """比较两个时间值
        
        Args:
            time1_fs: 第一个时间值（飞秒）
            time2_fs: 第二个时间值（飞秒）
            
        Returns:
            int: -1 如果 time1 < time2, 0 如果相等, 1 如果 time1 > time2
            
        Raises:
            TimeConversionError: 当输入参数不正确时
        """
        if not isinstance(time1_fs, int) or not isinstance(time2_fs, int):
            raise TimeConversionError("比较参数", "时间值必须为整数")
        
        if time1_fs < 0 or time2_fs < 0:
            raise TimeConversionError("比较参数", "时间值不能为负数")
        
        if time1_fs < time2_fs:
            return -1
        elif time1_fs > time2_fs:
            return 1
        else:
            return 0
    
    @staticmethod
    def is_valid_time_format(time_str: str) -> bool:
        """验证时间字符串格式是否正确
        
        Args:
            time_str: 时间字符串
            
        Returns:
            bool: 格式是否正确
        """
        if not time_str or not isinstance(time_str, str):
            return False
        
        try:
            TimeConverter.parse_time_unit(time_str)
            return True
        except TimeConversionError:
            return False
    
    @staticmethod
    def get_supported_units() -> list:
        """获取支持的时间单位列表
        
        Returns:
            list: 支持的时间单位列表
        """
        return list(TimeConverter.TIME_UNITS.keys())
    
    @staticmethod
    def is_reset_period_violation(violation_time_fs: int, reset_time_fs: int) -> bool:
        """判断违例是否发生在复位期间
        
        Args:
            violation_time_fs: 违例发生时间（飞秒）
            reset_time_fs: 复位时间（飞秒）
            
        Returns:
            bool: 是否为复位期间违例
            
        Raises:
            TimeConversionError: 当输入参数不正确时
        """
        if not isinstance(violation_time_fs, int) or not isinstance(reset_time_fs, int):
            raise TimeConversionError("复位时间比较", "时间值必须为整数")
        
        if violation_time_fs < 0 or reset_time_fs < 0:
            raise TimeConversionError("复位时间比较", "时间值不能为负数")
        
        return violation_time_fs <= reset_time_fs
    
    @staticmethod
    def compare_time_strings(time1_str: str, time2_str: str) -> int:
        """比较两个时间字符串
        
        Args:
            time1_str: 第一个时间字符串
            time2_str: 第二个时间字符串
            
        Returns:
            int: -1 如果 time1 < time2, 0 如果相等, 1 如果 time1 > time2
            
        Raises:
            TimeConversionError: 当时间字符串格式不正确时
        """
        try:
            time1_fs = TimeConverter.to_femtoseconds(time1_str)
            time2_fs = TimeConverter.to_femtoseconds(time2_str)
            return TimeConverter.compare_times(time1_fs, time2_fs)
        except TimeConversionError as e:
            raise TimeConversionError(
                f"{time1_str} vs {time2_str}", 
                f"时间字符串比较失败: {e.message}"
            )
    
    @staticmethod
    def validate_reset_time(reset_time_str: str) -> Tuple[bool, str]:
        """验证复位时间格式和数值
        
        Args:
            reset_time_str: 复位时间字符串
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if not reset_time_str or not isinstance(reset_time_str, str):
            return False, "复位时间不能为空"
        
        try:
            # 验证格式
            if not TimeConverter.is_valid_time_format(reset_time_str):
                return False, "复位时间格式不正确，应为数字+单位格式，如：1000PS, 500NS, 1500FS"
            
            # 验证数值
            fs_value = TimeConverter.to_femtoseconds(reset_time_str)
            if fs_value <= 0:
                return False, "复位时间必须大于0"
            
            return True, ""
            
        except TimeConversionError as e:
            return False, e.get_user_message()
    
    @staticmethod
    def get_time_difference(time1_fs: int, time2_fs: int, result_unit: str = None) -> str:
        """计算两个时间的差值
        
        Args:
            time1_fs: 第一个时间值（飞秒）
            time2_fs: 第二个时间值（飞秒）
            result_unit: 结果显示单位，如果为None则自动选择
            
        Returns:
            str: 格式化的时间差值字符串
            
        Raises:
            TimeConversionError: 当输入参数不正确时
        """
        if not isinstance(time1_fs, int) or not isinstance(time2_fs, int):
            raise TimeConversionError("时间差值计算", "时间值必须为整数")
        
        if time1_fs < 0 or time2_fs < 0:
            raise TimeConversionError("时间差值计算", "时间值不能为负数")
        
        diff_fs = abs(time1_fs - time2_fs)
        return TimeConverter.format_time(diff_fs, result_unit)
    
    @staticmethod
    def batch_validate_times(time_strings: list) -> Tuple[list, list]:
        """批量验证时间字符串格式
        
        Args:
            time_strings: 时间字符串列表
            
        Returns:
            Tuple[list, list]: (有效时间列表, 无效时间及错误信息列表)
        """
        valid_times = []
        invalid_times = []
        
        for i, time_str in enumerate(time_strings):
            try:
                if TimeConverter.is_valid_time_format(time_str):
                    fs_value = TimeConverter.to_femtoseconds(time_str)
                    valid_times.append({
                        'index': i,
                        'original': time_str,
                        'fs_value': fs_value,
                        'formatted': TimeConverter.format_time(fs_value)
                    })
                else:
                    invalid_times.append({
                        'index': i,
                        'original': time_str,
                        'error': '时间格式不正确'
                    })
            except TimeConversionError as e:
                invalid_times.append({
                    'index': i,
                    'original': time_str,
                    'error': e.get_user_message()
                })
        
        return valid_times, invalid_times


class CaseInfoExtractor:
    """用例信息提取器
    
    从文件路径中提取用例名称和corner信息。
    支持两种格式：
    1. {case_name}_{corner}/log/vio_summary.log
    2. {case_name}/log/vio_summary.log (需要用户选择corner)
    """
    
    # 用例目录名称正则表达式
    # 匹配格式：case_name 或 case_name_corner
    # 使用贪婪匹配，然后在验证时处理corner
    CASE_DIR_PATTERN = re.compile(r'^([a-zA-Z0-9_]+?)(?:_([a-zA-Z0-9_]+))?$')
    
    @staticmethod
    def extract_from_path(file_path: str) -> CaseInfo:
        """从文件路径提取用例信息
        
        Args:
            file_path: vio_summary.log文件的完整路径
            
        Returns:
            CaseInfo: 提取的用例信息
            
        Raises:
            ValueError: 当路径格式不正确或提取失败时
        """
        if not file_path or not isinstance(file_path, str):
            raise ValueError("文件路径不能为空")
        
        # 标准化路径分隔符
        normalized_path = os.path.normpath(file_path)
        
        # 验证文件名是否为vio_summary.log
        filename = os.path.basename(normalized_path)
        if filename.lower() != 'vio_summary.log':
            raise ValueError(f"文件名必须为vio_summary.log，当前文件名: {filename}")
        
        # 获取目录路径
        dir_path = os.path.dirname(normalized_path)
        
        # 验证是否在log目录下
        parent_dir = os.path.basename(dir_path)
        if parent_dir.lower() != 'log':
            raise ValueError(f"文件必须位于log目录下，当前目录: {parent_dir}")
        
        # 获取用例目录（log目录的父目录）
        case_dir_path = os.path.dirname(dir_path)
        case_dir_name = os.path.basename(case_dir_path)
        
        if not case_dir_name:
            raise ValueError("无法确定用例目录名称")
        
        # 解析用例目录名称
        case_name, corner = CaseInfoExtractor._parse_case_directory_name(case_dir_name)
        
        return CaseInfo(
            case_name=case_name,
            corner=corner or "",  # 如果corner为None，设置为空字符串
            directory_path=case_dir_path
        )
    
    @staticmethod
    def _parse_case_directory_name(dir_name: str) -> Tuple[str, Optional[str]]:
        """解析用例目录名称
        
        Args:
            dir_name: 目录名称
            
        Returns:
            Tuple[str, Optional[str]]: (case_name, corner)
            
        Raises:
            ValueError: 当目录名称格式不正确时
        """
        if not dir_name or not isinstance(dir_name, str):
            raise ValueError("目录名称不能为空")
        
        dir_name = dir_name.strip()
        
        # 首先尝试匹配已知的合法corner
        # 从最长的corner开始匹配，避免部分匹配问题
        for corner in sorted(VALID_CORNERS, key=len, reverse=True):
            if dir_name.endswith('_' + corner):
                case_name = dir_name[:-len('_' + corner)]
                if case_name:  # 确保case_name不为空
                    return case_name, corner
        
        # 如果没有匹配到合法corner，检查是否包含下划线
        if '_' in dir_name:
            # 使用正则表达式解析，但不验证corner合法性
            match = CaseInfoExtractor.CASE_DIR_PATTERN.match(dir_name)
            if match:
                case_name, potential_corner = match.groups()
                if case_name and potential_corner:
                    # corner不合法，将整个字符串作为case_name
                    return dir_name, None
        
        # 没有下划线或解析失败，整个字符串就是case_name
        if re.match(r'^[a-zA-Z0-9_]+$', dir_name):
            return dir_name, None
        else:
            raise ValueError(f"目录名称格式不正确: {dir_name}，只能包含字母、数字和下划线")
    
    @staticmethod
    def validate_file_path(file_path: str) -> Tuple[bool, str]:
        """验证文件路径格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            CaseInfoExtractor.extract_from_path(file_path)
            return True, ""
        except ValueError as e:
            return False, str(e)
        except Exception as e:
            return False, f"路径验证失败: {str(e)}"
    
    @staticmethod
    def get_case_name_from_path(file_path: str) -> str:
        """从文件路径获取用例名称
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 用例名称
            
        Raises:
            ValueError: 当路径格式不正确时
        """
        case_info = CaseInfoExtractor.extract_from_path(file_path)
        return case_info.case_name
    
    @staticmethod
    def get_corner_from_path(file_path: str) -> Optional[str]:
        """从文件路径获取corner信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[str]: corner信息，如果没有则返回None
            
        Raises:
            ValueError: 当路径格式不正确时
        """
        case_info = CaseInfoExtractor.extract_from_path(file_path)
        return case_info.corner if case_info.corner else None
    
    @staticmethod
    def needs_corner_selection(file_path: str) -> bool:
        """检查是否需要用户选择corner
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否需要选择corner
            
        Raises:
            ValueError: 当路径格式不正确时
        """
        case_info = CaseInfoExtractor.extract_from_path(file_path)
        return not case_info.has_corner()
    
    @staticmethod
    def create_case_info_with_corner(file_path: str, corner: str) -> CaseInfo:
        """创建包含指定corner的用例信息
        
        Args:
            file_path: 文件路径
            corner: 用户选择的corner
            
        Returns:
            CaseInfo: 完整的用例信息
            
        Raises:
            ValueError: 当路径格式不正确或corner不合法时
        """
        if not corner or not isinstance(corner, str):
            raise ValueError("Corner不能为空")
        
        corner = corner.strip()
        if not is_valid_corner(corner):
            raise ValueError(f"不合法的corner: {corner}，合法选项: {', '.join(VALID_CORNERS)}")
        
        # 先提取基础信息
        case_info = CaseInfoExtractor.extract_from_path(file_path)
        
        # 更新corner信息
        case_info.corner = corner
        
        return case_info
    
    @staticmethod
    def get_valid_corners() -> list:
        """获取合法的corner列表
        
        Returns:
            list: 合法corner列表
        """
        return VALID_CORNERS.copy()
    
    @staticmethod
    def is_corner_valid(corner: str) -> bool:
        """检查corner是否合法
        
        Args:
            corner: corner字符串
            
        Returns:
            bool: 是否合法
        """
        return is_valid_corner(corner)
    
    @staticmethod
    def get_corner_display_options() -> list:
        """获取corner下拉选择选项
        
        Returns:
            list: corner选项列表，用于UI显示
        """
        return VALID_CORNERS.copy()
    
    @staticmethod
    def get_corner_selection_list() -> list:
        """获取corner选择列表（包含提示选项）
        
        Returns:
            list: corner选择列表，第一项为提示文本
        """
        options = ["请选择Corner..."] + VALID_CORNERS.copy()
        return options
    
    @staticmethod
    def validate_corner_selection(corner: str) -> Tuple[bool, str]:
        """验证用户选择的corner
        
        Args:
            corner: 用户选择的corner
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if not corner or not isinstance(corner, str):
            return False, "请选择一个有效的Corner"
        
        corner = corner.strip()
        
        # 检查是否为提示文本
        if corner == "请选择Corner...":
            return False, "请选择一个有效的Corner"
        
        # 检查是否为空
        if not corner:
            return False, "Corner不能为空"
        
        # 检查是否在合法列表中
        if not is_valid_corner(corner):
            return False, f"不合法的Corner: {corner}，合法选项: {', '.join(VALID_CORNERS)}"
        
        return True, ""
    
    @staticmethod
    def get_corner_index_in_list(corner: str) -> int:
        """获取corner在选择列表中的索引
        
        Args:
            corner: corner字符串
            
        Returns:
            int: 索引位置，如果不存在返回0（提示选项）
        """
        if not corner or not is_valid_corner(corner):
            return 0  # 返回提示选项的索引
        
        try:
            # +1 是因为列表第一项是提示文本
            return VALID_CORNERS.index(corner) + 1
        except ValueError:
            return 0
    
    @staticmethod
    def get_corner_from_selection_index(index: int) -> Optional[str]:
        """从选择列表索引获取corner
        
        Args:
            index: 选择列表中的索引
            
        Returns:
            Optional[str]: corner字符串，如果索引无效返回None
        """
        if index <= 0 or index > len(VALID_CORNERS):
            return None
        
        # -1 是因为列表第一项是提示文本
        return VALID_CORNERS[index - 1]
    
    @staticmethod
    def extract_multiple_paths(file_paths: list) -> list:
        """批量提取多个文件路径的用例信息
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            list: 提取结果列表，每个元素包含 {'path': str, 'case_info': CaseInfo, 'error': str}
        """
        results = []
        
        for i, file_path in enumerate(file_paths):
            result = {
                'index': i,
                'path': file_path,
                'case_info': None,
                'error': ''
            }
            
            try:
                case_info = CaseInfoExtractor.extract_from_path(file_path)
                result['case_info'] = case_info
            except Exception as e:
                result['error'] = str(e)
            
            results.append(result)
        
        return results
    
    @staticmethod
    def validate_case_directory_structure(directory_path: str) -> Tuple[bool, str]:
        """验证用例目录结构
        
        Args:
            directory_path: 用例目录路径
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if not directory_path or not isinstance(directory_path, str):
            return False, "目录路径不能为空"
        
        if not os.path.exists(directory_path):
            return False, f"目录不存在: {directory_path}"
        
        if not os.path.isdir(directory_path):
            return False, f"路径不是目录: {directory_path}"
        
        # 检查log子目录是否存在
        log_dir = os.path.join(directory_path, 'log')
        if not os.path.exists(log_dir):
            return False, f"log子目录不存在: {log_dir}"
        
        if not os.path.isdir(log_dir):
            return False, f"log路径不是目录: {log_dir}"
        
        # 检查vio_summary.log文件是否存在
        vio_file = os.path.join(log_dir, 'vio_summary.log')
        if not os.path.exists(vio_file):
            return False, f"vio_summary.log文件不存在: {vio_file}"
        
        if not os.path.isfile(vio_file):
            return False, f"vio_summary.log不是文件: {vio_file}"
        
        return True, ""
    
    @staticmethod
    def suggest_case_name_from_path(file_path: str) -> str:
        """从路径推测用例名称（容错处理）
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 推测的用例名称
        """
        try:
            return CaseInfoExtractor.get_case_name_from_path(file_path)
        except ValueError:
            # 如果标准解析失败，尝试从路径中提取可能的用例名称
            normalized_path = os.path.normpath(file_path)
            path_parts = normalized_path.split(os.sep)
            
            # 寻找log目录，其父目录可能是用例目录
            for i, part in enumerate(path_parts):
                if part.lower() == 'log' and i > 0:
                    potential_case_dir = path_parts[i-1]
                    # 简单清理目录名称
                    case_name = re.sub(r'[^a-zA-Z0-9_]', '_', potential_case_dir)
                    return case_name if case_name else "unknown_case"
            
            # 如果找不到log目录，使用文件名前缀
            filename = os.path.basename(file_path)
            name_without_ext = os.path.splitext(filename)[0]
            return name_without_ext if name_without_ext else "unknown_case"


# UI组件类（仅在PyQt5可用时定义）
if PYQT5_AVAILABLE:
    class CornerSelectionWidget(QComboBox):
        """Corner选择下拉框组件
        
        提供corner选择功能，包含验证和信号发送。
        """
        
        # 自定义信号
        corner_selected = pyqtSignal(str)  # 当选择有效corner时发送
        selection_cleared = pyqtSignal()   # 当清除选择时发送
        
        def __init__(self, parent=None):
            """初始化corner选择组件
            
            Args:
                parent: 父组件
            """
            super().__init__(parent)
            
            # 设置组件属性
            self.setMinimumWidth(150)
            self.setMaximumWidth(200)
            
            # 初始化选项
            self._setup_options()
            
            # 连接信号
            self.currentTextChanged.connect(self._on_selection_changed)
        
        def _setup_options(self):
            """设置下拉选项"""
            # 清空现有选项
            self.clear()
            
            # 添加选项
            options = CaseInfoExtractor.get_corner_selection_list()
            self.addItems(options)
            
            # 设置默认选择为提示文本
            self.setCurrentIndex(0)
        
        def _on_selection_changed(self, text: str):
            """处理选择变化事件
            
            Args:
                text: 选择的文本
            """
            # 验证选择
            is_valid, error_msg = CaseInfoExtractor.validate_corner_selection(text)
            
            if is_valid:
                # 发送有效选择信号
                self.corner_selected.emit(text)
            else:
                # 如果选择的是提示文本，发送清除信号
                if text == "请选择Corner...":
                    self.selection_cleared.emit()
        
        def set_corner(self, corner: str):
            """设置当前选择的corner
            
            Args:
                corner: corner字符串
            """
            if not corner:
                self.setCurrentIndex(0)  # 设置为提示文本
                return
            
            # 获取corner在列表中的索引
            index = CaseInfoExtractor.get_corner_index_in_list(corner)
            self.setCurrentIndex(index)
        
        def get_selected_corner(self) -> Optional[str]:
            """获取当前选择的corner
            
            Returns:
                Optional[str]: 选择的corner，如果无效返回None
            """
            current_text = self.currentText()
            is_valid, _ = CaseInfoExtractor.validate_corner_selection(current_text)
            
            if is_valid:
                return current_text
            return None
        
        def clear_selection(self):
            """清除选择"""
            self.setCurrentIndex(0)
        
        def is_selection_valid(self) -> bool:
            """检查当前选择是否有效
            
            Returns:
                bool: 选择是否有效
            """
            current_text = self.currentText()
            is_valid, _ = CaseInfoExtractor.validate_corner_selection(current_text)
            return is_valid
        
        def get_validation_message(self) -> str:
            """获取当前选择的验证消息
            
            Returns:
                str: 验证消息，如果有效返回空字符串
            """
            current_text = self.currentText()
            is_valid, error_msg = CaseInfoExtractor.validate_corner_selection(current_text)
            return "" if is_valid else error_msg
        
        def refresh_options(self):
            """刷新选项列表"""
            current_corner = self.get_selected_corner()
            self._setup_options()
            
            # 恢复之前的选择
            if current_corner:
                self.set_corner(current_corner)
        
        def get_all_valid_corners(self) -> list:
            """获取所有有效的corner选项
            
            Returns:
                list: 有效corner列表
            """
            return CaseInfoExtractor.get_valid_corners()
        
        def set_enabled_corners(self, enabled_corners: list):
            """设置启用的corner选项
            
            Args:
                enabled_corners: 启用的corner列表
            """
            # 清空现有选项
            self.clear()
            
            # 添加提示文本
            self.addItem("请选择Corner...")
            
            # 添加启用的corner选项
            for corner in enabled_corners:
                if CaseInfoExtractor.is_corner_valid(corner):
                    self.addItem(corner)
            
            # 设置默认选择
            self.setCurrentIndex(0)
        
        def get_corner_display_text(self, corner: str) -> str:
            """获取corner的显示文本
            
            Args:
                corner: corner字符串
                
            Returns:
                str: 显示文本
            """
            if not corner or not CaseInfoExtractor.is_corner_valid(corner):
                return "无效Corner"
            
            return corner
        
        def set_placeholder_text(self, text: str):
            """设置占位符文本
            
            Args:
                text: 占位符文本
            """
            if self.count() > 0:
                self.setItemText(0, text)
        
        def has_valid_selection(self) -> bool:
            """检查是否有有效选择
            
            Returns:
                bool: 是否有有效选择
            """
            return self.is_selection_valid()
        
        def get_selection_info(self) -> dict:
            """获取选择信息
            
            Returns:
                dict: 包含选择信息的字典
            """
            current_text = self.currentText()
            is_valid, error_msg = CaseInfoExtractor.validate_corner_selection(current_text)
            
            return {
                'text': current_text,
                'corner': current_text if is_valid else None,
                'is_valid': is_valid,
                'error_message': error_msg,
                'index': self.currentIndex()
            }