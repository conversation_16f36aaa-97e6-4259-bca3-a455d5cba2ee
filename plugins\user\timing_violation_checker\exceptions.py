"""
时序违例确认插件异常类定义

定义插件中使用的各种异常类型，提供详细的错误信息和处理机制。
"""


class TimingViolationError(Exception):
    """时序违例插件基础异常类
    
    所有插件相关异常的基类，提供统一的异常处理接口。
    """
    
    def __init__(self, message: str, details: str = None):
        """初始化异常
        
        Args:
            message: 异常消息
            details: 详细信息（可选）
        """
        super().__init__(message)
        self.message = message
        self.details = details or ""
    
    def __str__(self):
        """返回异常字符串表示"""
        if self.details:
            return f"{self.message}\n详细信息: {self.details}"
        return self.message
    
    def get_user_message(self) -> str:
        """获取用户友好的错误消息"""
        return self.message


class FileParseError(TimingViolationError):
    """文件解析错误异常
    
    当解析vio_summary.log文件时发生错误时抛出。
    """
    
    def __init__(self, file_path: str, message: str, line_number: int = None):
        """初始化文件解析异常
        
        Args:
            file_path: 文件路径
            message: 错误消息
            line_number: 出错行号（可选）
        """
        self.file_path = file_path
        self.line_number = line_number
        
        if line_number:
            full_message = f"解析文件 '{file_path}' 第 {line_number} 行时发生错误: {message}"
        else:
            full_message = f"解析文件 '{file_path}' 时发生错误: {message}"
        
        super().__init__(full_message, f"文件路径: {file_path}")
    
    def get_user_message(self) -> str:
        """获取用户友好的错误消息"""
        return f"文件解析失败，请检查文件格式是否正确。\n文件: {self.file_path}"


class TimeConversionError(TimingViolationError):
    """时间转换错误异常
    
    当时间单位转换失败时抛出。
    """
    
    def __init__(self, time_string: str, message: str = None):
        """初始化时间转换异常
        
        Args:
            time_string: 原始时间字符串
            message: 错误消息（可选）
        """
        self.time_string = time_string
        
        if message:
            full_message = f"时间转换失败: {message}"
        else:
            full_message = f"无法转换时间字符串 '{time_string}'"
        
        super().__init__(full_message, f"原始时间字符串: {time_string}")
    
    def get_user_message(self) -> str:
        """获取用户友好的错误消息"""
        return f"时间格式转换失败，请检查时间格式是否正确。\n时间字符串: {self.time_string}"


class ExportError(TimingViolationError):
    """导出错误异常
    
    当导出操作失败时抛出。
    """
    
    def __init__(self, message: str, file_path: str = None, format_type: str = None):
        """初始化导出异常
        
        Args:
            message: 错误消息
            file_path: 导出文件路径（可选）
            format_type: 导出格式类型（可选）
        """
        self.file_path = file_path
        self.format_type = format_type
        
        if format_type and file_path:
            full_message = f"导出 {format_type.upper()} 文件失败: {message}"
            details = f"文件路径: {file_path}"
        else:
            full_message = f"导出失败: {message}"
            details = f"文件路径: {file_path}" if file_path else None
        
        super().__init__(full_message, details)
    
    def get_user_message(self) -> str:
        """获取用户友好的错误消息"""
        if self.file_path:
            return f"导出文件失败，请检查文件路径和权限。\n文件: {self.file_path}"
        return f"导出失败: {self.message}"


class ValidationError(TimingViolationError):
    """数据验证错误异常
    
    当数据验证失败时抛出。
    """
    
    def __init__(self, field_name: str, value: str, message: str):
        """初始化验证异常
        
        Args:
            field_name: 字段名称
            value: 字段值
            message: 错误消息
        """
        self.field_name = field_name
        self.value = value
        
        full_message = f"字段 '{field_name}' 验证失败: {message}"
        super().__init__(full_message, f"字段值: {value}")
    
    def get_user_message(self) -> str:
        """获取用户友好的错误消息"""
        return f"输入验证失败: {self.message}"


class CaseInfoExtractionError(TimingViolationError):
    """用例信息提取错误异常
    
    当从文件路径提取用例信息失败时抛出。
    """
    
    def __init__(self, file_path: str, message: str):
        """初始化用例信息提取异常
        
        Args:
            file_path: 文件路径
            message: 错误消息
        """
        self.file_path = file_path
        
        full_message = f"提取用例信息失败: {message}"
        super().__init__(full_message, f"文件路径: {file_path}")
    
    def get_user_message(self) -> str:
        """获取用户友好的错误消息"""
        return f"无法从文件路径提取用例信息，请检查路径格式。\n路径: {self.file_path}"


class PluginInitializationError(TimingViolationError):
    """插件初始化错误异常
    
    当插件初始化失败时抛出。
    """
    
    def __init__(self, plugin_name: str, message: str):
        """初始化插件初始化异常
        
        Args:
            plugin_name: 插件名称
            message: 错误消息
        """
        self.plugin_name = plugin_name
        
        full_message = f"插件 '{plugin_name}' 初始化失败: {message}"
        super().__init__(full_message)
    
    def get_user_message(self) -> str:
        """获取用户友好的错误消息"""
        return f"插件初始化失败，请重启应用程序或联系管理员。\n插件: {self.plugin_name}"


class ConfigurationError(TimingViolationError):
    """配置错误异常
    
    当配置参数错误时抛出。
    """
    
    def __init__(self, config_name: str, config_value: str, message: str):
        """初始化配置异常
        
        Args:
            config_name: 配置名称
            config_value: 配置值
            message: 错误消息
        """
        self.config_name = config_name
        self.config_value = config_value
        
        full_message = f"配置 '{config_name}' 错误: {message}"
        super().__init__(full_message, f"配置值: {config_value}")
    
    def get_user_message(self) -> str:
        """获取用户友好的错误消息"""
        return f"配置参数错误: {self.message}"