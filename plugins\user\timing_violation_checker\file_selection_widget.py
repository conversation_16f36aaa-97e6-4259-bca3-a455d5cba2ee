"""
文件选择组件

提供时序违例日志文件选择功能，包括文件路径验证、
用例信息提取和错误处理。
"""

import os
from typing import Optional, Tuple
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QLineEdit, QFileDialog, QMessageBox,
                             QGroupBox, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from .utils import CaseInfoExtractor, CornerSelectionWidget
    from .models import CaseInfo
    from .exceptions import TimingViolationError
except ImportError:
    from utils import CaseInfoExtractor, CornerSelectionWidget
    from models import CaseInfo
    from exceptions import TimingViolationError


class FileSelectionWidget(QWidget):
    """文件选择组件
    
    提供vio_summary.log文件选择、路径验证和用例信息显示功能。
    """
    
    # 自定义信号
    file_selected = pyqtSignal(str)  # 文件选择成功时发送文件路径
    case_info_extracted = pyqtSignal(CaseInfo)  # 用例信息提取成功时发送
    corner_selection_required = pyqtSignal(str)  # 需要用户选择corner时发送case_name
    validation_error = pyqtSignal(str)  # 验证错误时发送错误信息
    
    def __init__(self, parent=None):
        """初始化文件选择组件
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        # 初始化数据
        self.current_file_path = ""
        self.current_case_info = None
        self.corner_selection_widget = None
        
        # 设置界面
        self.setup_ui()
        
        # 连接信号
        self.connect_signals()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        
        # 创建文件选择组
        file_group = self.create_file_selection_group()
        main_layout.addWidget(file_group)
        
        # 创建用例信息组
        case_info_group = self.create_case_info_group()
        main_layout.addWidget(case_info_group)
        
        # 设置主布局
        self.setLayout(main_layout)
    
    def create_file_selection_group(self) -> QGroupBox:
        """创建文件选择组
        
        Returns:
            QGroupBox: 文件选择组件
        """
        group = QGroupBox("文件选择")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QVBoxLayout()
        
        # 文件路径输入行
        path_layout = QHBoxLayout()
        
        # 文件路径标签
        path_label = QLabel("文件路径:")
        path_label.setMinimumWidth(80)
        path_layout.addWidget(path_label)
        
        # 文件路径输入框
        self.path_input = QLineEdit()
        self.path_input.setPlaceholderText("请选择vio_summary.log文件...")
        self.path_input.setReadOnly(True)
        self.path_input.setStyleSheet("""
            QLineEdit {
                padding: 5px;
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: #f9f9f9;
            }
        """)
        path_layout.addWidget(self.path_input)
        
        # 浏览按钮
        self.browse_button = QPushButton("浏览...")
        self.browse_button.setMinimumWidth(80)
        self.browse_button.setStyleSheet("""
            QPushButton {
                padding: 5px 15px;
                background-color: #4a9eff;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3a8eef;
            }
            QPushButton:pressed {
                background-color: #2a7edf;
            }
        """)
        path_layout.addWidget(self.browse_button)
        
        layout.addLayout(path_layout)
        
        # 状态信息标签
        self.status_label = QLabel("")
        self.status_label.setWordWrap(True)
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 5px;
                border-radius: 3px;
                margin-top: 5px;
            }
        """)
        layout.addWidget(self.status_label)
        
        group.setLayout(layout)
        return group
    
    def create_case_info_group(self) -> QGroupBox:
        """创建用例信息组
        
        Returns:
            QGroupBox: 用例信息组件
        """
        group = QGroupBox("用例信息")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QVBoxLayout()
        
        # 用例名称行
        case_name_layout = QHBoxLayout()
        
        case_name_label = QLabel("用例名称:")
        case_name_label.setMinimumWidth(80)
        case_name_layout.addWidget(case_name_label)
        
        self.case_name_display = QLabel("未选择文件")
        self.case_name_display.setStyleSheet("""
            QLabel {
                padding: 5px;
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: #f9f9f9;
                color: #666666;
            }
        """)
        case_name_layout.addWidget(self.case_name_display)
        
        layout.addLayout(case_name_layout)
        
        # Corner选择行
        corner_layout = QHBoxLayout()
        
        corner_label = QLabel("Corner:")
        corner_label.setMinimumWidth(80)
        corner_layout.addWidget(corner_label)
        
        # Corner显示/选择组件容器
        self.corner_container = QWidget()
        self.corner_container_layout = QHBoxLayout()
        self.corner_container_layout.setContentsMargins(0, 0, 0, 0)
        self.corner_container.setLayout(self.corner_container_layout)
        
        # 初始显示标签
        self.corner_display = QLabel("未选择文件")
        self.corner_display.setStyleSheet("""
            QLabel {
                padding: 5px;
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: #f9f9f9;
                color: #666666;
            }
        """)
        self.corner_container_layout.addWidget(self.corner_display)
        
        corner_layout.addWidget(self.corner_container)
        
        layout.addLayout(corner_layout)
        
        # 验证状态行
        self.validation_status = QLabel("")
        self.validation_status.setWordWrap(True)
        self.validation_status.setStyleSheet("""
            QLabel {
                padding: 5px;
                border-radius: 3px;
                margin-top: 5px;
            }
        """)
        layout.addWidget(self.validation_status)
        
        group.setLayout(layout)
        return group
    
    def connect_signals(self):
        """连接信号和槽"""
        # 浏览按钮点击事件
        self.browse_button.clicked.connect(self.browse_file)
        
        # 路径输入框变化事件（虽然是只读，但可能通过代码设置）
        self.path_input.textChanged.connect(self.on_path_changed)
    
    def browse_file(self):
        """浏览文件对话框"""
        try:
            # 设置文件对话框
            file_dialog = QFileDialog(self)
            file_dialog.setWindowTitle("选择时序违例日志文件")
            file_dialog.setNameFilter("日志文件 (vio_summary.log);;所有文件 (*.*)")
            file_dialog.setFileMode(QFileDialog.ExistingFile)
            file_dialog.setAcceptMode(QFileDialog.AcceptOpen)
            
            # 设置默认目录（如果有当前路径）
            if self.current_file_path and os.path.exists(self.current_file_path):
                file_dialog.setDirectory(os.path.dirname(self.current_file_path))
            
            # 显示对话框
            if file_dialog.exec_() == QFileDialog.Accepted:
                selected_files = file_dialog.selectedFiles()
                if selected_files:
                    file_path = selected_files[0]
                    self.set_file_path(file_path)
                    
        except Exception as e:
            self.show_error_message("文件选择错误", f"选择文件时发生错误:\n{str(e)}")
    
    def set_file_path(self, file_path: str):
        """设置文件路径
        
        Args:
            file_path: 文件路径
        """
        if not file_path:
            self.clear_file_selection()
            return
        
        # 如果路径没有变化，不需要重新处理
        if file_path == self.current_file_path:
            return
        
        # 更新路径输入框（这会触发on_path_changed，但我们已经设置了current_file_path，所以不会重复处理）
        self.current_file_path = file_path
        self.path_input.setText(file_path)
        
        # 验证文件路径
        self.validate_and_extract_info(file_path)
    
    def on_path_changed(self, path: str):
        """路径变化事件处理
        
        Args:
            path: 新的路径
        """
        if path != self.current_file_path:
            self.current_file_path = path
            if path:
                self.validate_and_extract_info(path)
            else:
                self.clear_file_selection()
    
    def validate_and_extract_info(self, file_path: str):
        """验证文件路径并提取用例信息
        
        Args:
            file_path: 文件路径
        """
        try:
            # 清除之前的状态
            self.clear_status_messages()
            
            # 验证文件是否存在
            if not os.path.exists(file_path):
                self.show_validation_error("文件不存在")
                return
            
            if not os.path.isfile(file_path):
                self.show_validation_error("路径不是文件")
                return
            
            # 验证文件路径格式并提取用例信息
            case_info = CaseInfoExtractor.extract_from_path(file_path)
            
            # 更新用例信息显示
            self.update_case_info_display(case_info)
            
            # 存储用例信息
            self.current_case_info = case_info
            
            # 检查是否需要用户选择corner
            if not case_info.has_corner():
                self.show_corner_selection(case_info.case_name)
                self.corner_selection_required.emit(case_info.case_name)
            else:
                # 有完整信息，发送信号
                self.show_validation_success("文件验证成功")
                self.file_selected.emit(file_path)
                self.case_info_extracted.emit(case_info)
            
        except ValueError as e:
            self.show_validation_error(f"文件路径格式错误: {str(e)}")
            self.validation_error.emit(str(e))
            
        except Exception as e:
            error_msg = f"文件验证失败: {str(e)}"
            self.show_validation_error(error_msg)
            self.validation_error.emit(error_msg)
    
    def update_case_info_display(self, case_info: CaseInfo):
        """更新用例信息显示
        
        Args:
            case_info: 用例信息
        """
        # 更新用例名称
        self.case_name_display.setText(case_info.case_name)
        self.case_name_display.setStyleSheet("""
            QLabel {
                padding: 5px;
                border: 1px solid #28a745;
                border-radius: 3px;
                background-color: #d4edda;
                color: #155724;
            }
        """)
        
        # 更新corner信息
        if case_info.has_corner():
            self.show_corner_display(case_info.corner)
        else:
            # 需要用户选择corner，显示选择组件
            self.show_corner_selection(case_info.case_name)
    
    def show_corner_display(self, corner: str):
        """显示corner信息
        
        Args:
            corner: corner字符串
        """
        # 清除容器中的组件
        self.clear_corner_container()
        
        # 创建corner显示标签
        self.corner_display = QLabel(corner)
        self.corner_display.setStyleSheet("""
            QLabel {
                padding: 5px;
                border: 1px solid #28a745;
                border-radius: 3px;
                background-color: #d4edda;
                color: #155724;
            }
        """)
        
        self.corner_container_layout.addWidget(self.corner_display)
        self.corner_selection_widget = None
    
    def show_corner_selection(self, case_name: str):
        """显示corner选择组件
        
        Args:
            case_name: 用例名称
        """
        # 清除容器中的组件
        self.clear_corner_container()
        
        # 创建corner选择组件
        self.corner_selection_widget = CornerSelectionWidget()
        self.corner_selection_widget.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #ffc107;
                border-radius: 3px;
                background-color: #fff3cd;
                color: #856404;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #856404;
                margin-right: 5px;
            }
        """)
        
        # 连接corner选择信号
        self.corner_selection_widget.corner_selected.connect(self.on_corner_selected)
        self.corner_selection_widget.selection_cleared.connect(self.on_corner_selection_cleared)
        
        self.corner_container_layout.addWidget(self.corner_selection_widget)
        
        # 显示提示信息
        self.show_validation_warning("请选择Corner以完成用例信息")
    
    def clear_corner_container(self):
        """清除corner容器中的组件"""
        # 移除所有子组件
        while self.corner_container_layout.count():
            child = self.corner_container_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def on_corner_selected(self, corner: str):
        """corner选择事件处理
        
        Args:
            corner: 选择的corner
        """
        try:
            if self.current_file_path and self.current_case_info:
                # 创建包含corner的完整用例信息
                complete_case_info = CaseInfoExtractor.create_case_info_with_corner(
                    self.current_file_path, corner
                )
                
                # 更新当前用例信息
                self.current_case_info = complete_case_info
                
                # 显示成功状态
                self.show_validation_success("用例信息完整，可以开始处理")
                
                # 发送信号
                self.file_selected.emit(self.current_file_path)
                self.case_info_extracted.emit(complete_case_info)
                
        except ValueError as e:
            self.show_validation_error(f"Corner选择错误: {str(e)}")
            self.validation_error.emit(str(e))
        except Exception as e:
            error_msg = f"处理Corner选择时发生错误: {str(e)}"
            self.show_validation_error(error_msg)
            self.validation_error.emit(error_msg)
    
    def on_corner_selection_cleared(self):
        """corner选择清除事件处理"""
        self.show_validation_warning("请选择Corner以完成用例信息")
        
        # 重置当前用例信息的corner
        if self.current_case_info:
            self.current_case_info.corner = ""
    
    def show_validation_success(self, message: str):
        """显示验证成功信息
        
        Args:
            message: 成功信息
        """
        self.status_label.setText(f"✓ {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 5px;
                border-radius: 3px;
                margin-top: 5px;
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
        """)
        
        self.validation_status.setText("")
    
    def show_validation_warning(self, message: str):
        """显示验证警告信息
        
        Args:
            message: 警告信息
        """
        self.validation_status.setText(f"⚠ {message}")
        self.validation_status.setStyleSheet("""
            QLabel {
                padding: 5px;
                border-radius: 3px;
                margin-top: 5px;
                background-color: #fff3cd;
                color: #856404;
                border: 1px solid #ffeaa7;
            }
        """)
    
    def show_validation_error(self, message: str):
        """显示验证错误信息
        
        Args:
            message: 错误信息
        """
        self.status_label.setText(f"✗ {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 5px;
                border-radius: 3px;
                margin-top: 5px;
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
        """)
        
        # 清除用例信息显示
        self.clear_case_info_display()
    
    def clear_status_messages(self):
        """清除状态信息"""
        self.status_label.setText("")
        self.status_label.setStyleSheet("")
        self.validation_status.setText("")
        self.validation_status.setStyleSheet("")
    
    def clear_case_info_display(self):
        """清除用例信息显示"""
        self.case_name_display.setText("未选择文件")
        self.case_name_display.setStyleSheet("""
            QLabel {
                padding: 5px;
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: #f9f9f9;
                color: #666666;
            }
        """)
        
        # 清除corner容器并显示默认标签
        self.clear_corner_container()
        self.corner_display = QLabel("未选择文件")
        self.corner_display.setStyleSheet("""
            QLabel {
                padding: 5px;
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: #f9f9f9;
                color: #666666;
            }
        """)
        self.corner_container_layout.addWidget(self.corner_display)
        
        self.corner_selection_widget = None
    
    def clear_file_selection(self):
        """清除文件选择"""
        self.current_file_path = ""
        self.current_case_info = None
        self.path_input.setText("")
        self.clear_status_messages()
        self.clear_case_info_display()
    
    def show_error_message(self, title: str, message: str):
        """显示错误消息对话框
        
        Args:
            title: 对话框标题
            message: 错误消息
        """
        QMessageBox.critical(self, title, message)
    
    def get_current_file_path(self) -> str:
        """获取当前文件路径
        
        Returns:
            str: 当前文件路径
        """
        return self.current_file_path
    
    def get_current_case_info(self) -> Optional[CaseInfo]:
        """获取当前用例信息
        
        Returns:
            Optional[CaseInfo]: 当前用例信息，如果没有返回None
        """
        return self.current_case_info
    
    def is_file_selected(self) -> bool:
        """检查是否已选择文件
        
        Returns:
            bool: 是否已选择文件
        """
        return bool(self.current_file_path)
    
    def is_case_info_complete(self) -> bool:
        """检查用例信息是否完整
        
        Returns:
            bool: 用例信息是否完整
        """
        return (self.current_case_info is not None and 
                self.current_case_info.has_corner())
    
    def get_validation_status(self) -> dict:
        """获取验证状态信息
        
        Returns:
            dict: 验证状态信息
        """
        return {
            'file_selected': self.is_file_selected(),
            'case_info_complete': self.is_case_info_complete(),
            'file_path': self.current_file_path,
            'case_info': self.current_case_info,
            'needs_corner_selection': (self.current_case_info is not None and 
                                     not self.current_case_info.has_corner())
        }
    
    def set_enabled(self, enabled: bool):
        """设置组件启用状态
        
        Args:
            enabled: 是否启用
        """
        self.browse_button.setEnabled(enabled)
        if self.corner_selection_widget:
            self.corner_selection_widget.setEnabled(enabled)
    
    def reset(self):
        """重置组件状态"""
        self.clear_file_selection()
    
    def validate_current_selection(self) -> Tuple[bool, str]:
        """验证当前选择
        
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if not self.is_file_selected():
            return False, "请选择vio_summary.log文件"
        
        if not self.is_case_info_complete():
            return False, "请完成用例信息设置（包括Corner选择）"
        
        # 验证文件是否仍然存在
        if not os.path.exists(self.current_file_path):
            return False, "选择的文件不存在"
        
        return True, ""
    
    def get_file_info(self) -> dict:
        """获取文件信息
        
        Returns:
            dict: 文件信息字典
        """
        if not self.is_file_selected():
            return {}
        
        try:
            file_stat = os.stat(self.current_file_path)
            return {
                'path': self.current_file_path,
                'size': file_stat.st_size,
                'modified_time': file_stat.st_mtime,
                'exists': os.path.exists(self.current_file_path),
                'is_readable': os.access(self.current_file_path, os.R_OK)
            }
        except Exception as e:
            return {
                'path': self.current_file_path,
                'error': str(e)
            }