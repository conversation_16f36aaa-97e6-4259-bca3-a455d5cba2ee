"""
时序违例确认插件工具类单元测试

测试TimeConverter类的各种功能和边界情况。
"""

import unittest
from .utils import TimeConverter
from .exceptions import TimeConversionError


class TestTimeConverter(unittest.TestCase):
    """TimeConverter类单元测试"""
    
    def test_to_femtoseconds_valid_inputs(self):
        """测试有效输入的时间转换"""
        # 测试飞秒转换
        self.assertEqual(TimeConverter.to_femtoseconds("1000FS"), 1000)
        self.assertEqual(TimeConverter.to_femtoseconds("1000fs"), 1000)
        self.assertEqual(TimeConverter.to_femtoseconds("0FS"), 0)
        
        # 测试皮秒转换
        self.assertEqual(TimeConverter.to_femtoseconds("1PS"), 1000)
        self.assertEqual(TimeConverter.to_femtoseconds("1ps"), 1000)
        self.assertEqual(TimeConverter.to_femtoseconds("2PS"), 2000)
        self.assertEqual(TimeConverter.to_femtoseconds("0.5PS"), 500)
        
        # 测试纳秒转换
        self.assertEqual(TimeConverter.to_femtoseconds("1NS"), 1000000)
        self.assertEqual(TimeConverter.to_femtoseconds("1ns"), 1000000)
        self.assertEqual(TimeConverter.to_femtoseconds("2NS"), 2000000)
        self.assertEqual(TimeConverter.to_femtoseconds("0.001NS"), 1000)
        
        # 测试带空格的输入
        self.assertEqual(TimeConverter.to_femtoseconds(" 1000 FS "), 1000)
        self.assertEqual(TimeConverter.to_femtoseconds("  2 PS  "), 2000)
    
    def test_to_femtoseconds_invalid_inputs(self):
        """测试无效输入的异常处理"""
        # 测试空输入
        with self.assertRaises(TimeConversionError):
            TimeConverter.to_femtoseconds("")
        
        with self.assertRaises(TimeConversionError):
            TimeConverter.to_femtoseconds(None)
        
        # 测试格式错误
        with self.assertRaises(TimeConversionError):
            TimeConverter.to_femtoseconds("1000")  # 缺少单位
        
        with self.assertRaises(TimeConversionError):
            TimeConverter.to_femtoseconds("FS")  # 缺少数值
        
        with self.assertRaises(TimeConversionError):
            TimeConverter.to_femtoseconds("abc FS")  # 无效数值
        
        with self.assertRaises(TimeConversionError):
            TimeConverter.to_femtoseconds("1000 MS")  # 不支持的单位
        
        # 测试负数
        with self.assertRaises(TimeConversionError):
            TimeConverter.to_femtoseconds("-1000FS")
    
    def test_format_time_auto_unit(self):
        """测试自动单位选择的时间格式化"""
        # 测试飞秒显示
        self.assertEqual(TimeConverter.format_time(0), "0FS")
        self.assertEqual(TimeConverter.format_time(500), "500FS")
        self.assertEqual(TimeConverter.format_time(999), "999FS")
        
        # 测试皮秒显示
        self.assertEqual(TimeConverter.format_time(1000), "1PS")
        self.assertEqual(TimeConverter.format_time(1500), "1.500PS")
        self.assertEqual(TimeConverter.format_time(999000), "999PS")
        
        # 测试纳秒显示
        self.assertEqual(TimeConverter.format_time(1000000), "1NS")
        self.assertEqual(TimeConverter.format_time(1500000), "1.500NS")
        self.assertEqual(TimeConverter.format_time(2000000), "2NS")
    
    def test_format_time_specific_unit(self):
        """测试指定单位的时间格式化"""
        # 测试转换为飞秒
        self.assertEqual(TimeConverter.format_time(1000, "FS"), "1000FS")
        self.assertEqual(TimeConverter.format_time(1500, "FS"), "1500FS")
        
        # 测试转换为皮秒
        self.assertEqual(TimeConverter.format_time(1000, "PS"), "1PS")
        self.assertEqual(TimeConverter.format_time(1500, "PS"), "1.500PS")
        
        # 测试转换为纳秒
        self.assertEqual(TimeConverter.format_time(1000000, "NS"), "1NS")
        self.assertEqual(TimeConverter.format_time(1500000, "NS"), "1.500NS")
        
        # 测试大小写不敏感
        self.assertEqual(TimeConverter.format_time(1000, "ps"), "1PS")
        self.assertEqual(TimeConverter.format_time(1000000, "ns"), "1NS")
    
    def test_format_time_invalid_inputs(self):
        """测试格式化时间的无效输入"""
        # 测试负数
        with self.assertRaises(TimeConversionError):
            TimeConverter.format_time(-1000)
        
        # 测试非整数
        with self.assertRaises(TimeConversionError):
            TimeConverter.format_time(1000.5)
        
        # 测试不支持的单位
        with self.assertRaises(TimeConversionError):
            TimeConverter.format_time(1000, "MS")
    
    def test_parse_time_unit(self):
        """测试时间字符串解析"""
        # 测试正常解析
        value, unit = TimeConverter.parse_time_unit("1000FS")
        self.assertEqual(value, 1000.0)
        self.assertEqual(unit, "FS")
        
        value, unit = TimeConverter.parse_time_unit("1.5PS")
        self.assertEqual(value, 1.5)
        self.assertEqual(unit, "PS")
        
        value, unit = TimeConverter.parse_time_unit("2NS")
        self.assertEqual(value, 2.0)
        self.assertEqual(unit, "NS")
        
        # 测试大小写不敏感
        value, unit = TimeConverter.parse_time_unit("1000fs")
        self.assertEqual(value, 1000.0)
        self.assertEqual(unit, "FS")
    
    def test_parse_time_unit_invalid_inputs(self):
        """测试时间解析的无效输入"""
        with self.assertRaises(TimeConversionError):
            TimeConverter.parse_time_unit("")
        
        with self.assertRaises(TimeConversionError):
            TimeConverter.parse_time_unit("1000")
        
        with self.assertRaises(TimeConversionError):
            TimeConverter.parse_time_unit("FS")
        
        with self.assertRaises(TimeConversionError):
            TimeConverter.parse_time_unit("-1000FS")
    
    def test_compare_times(self):
        """测试时间比较功能"""
        # 测试相等
        self.assertEqual(TimeConverter.compare_times(1000, 1000), 0)
        self.assertEqual(TimeConverter.compare_times(0, 0), 0)
        
        # 测试小于
        self.assertEqual(TimeConverter.compare_times(1000, 2000), -1)
        self.assertEqual(TimeConverter.compare_times(0, 1000), -1)
        
        # 测试大于
        self.assertEqual(TimeConverter.compare_times(2000, 1000), 1)
        self.assertEqual(TimeConverter.compare_times(1000, 0), 1)
    
    def test_compare_times_invalid_inputs(self):
        """测试时间比较的无效输入"""
        # 测试非整数
        with self.assertRaises(TimeConversionError):
            TimeConverter.compare_times(1000.5, 2000)
        
        with self.assertRaises(TimeConversionError):
            TimeConverter.compare_times(1000, 2000.5)
        
        # 测试负数
        with self.assertRaises(TimeConversionError):
            TimeConverter.compare_times(-1000, 2000)
        
        with self.assertRaises(TimeConversionError):
            TimeConverter.compare_times(1000, -2000)
    
    def test_is_valid_time_format(self):
        """测试时间格式验证"""
        # 测试有效格式
        self.assertTrue(TimeConverter.is_valid_time_format("1000FS"))
        self.assertTrue(TimeConverter.is_valid_time_format("1.5PS"))
        self.assertTrue(TimeConverter.is_valid_time_format("2NS"))
        self.assertTrue(TimeConverter.is_valid_time_format(" 1000 FS "))
        
        # 测试无效格式
        self.assertFalse(TimeConverter.is_valid_time_format(""))
        self.assertFalse(TimeConverter.is_valid_time_format(None))
        self.assertFalse(TimeConverter.is_valid_time_format("1000"))
        self.assertFalse(TimeConverter.is_valid_time_format("FS"))
        self.assertFalse(TimeConverter.is_valid_time_format("abc FS"))
        self.assertFalse(TimeConverter.is_valid_time_format("-1000FS"))
        self.assertFalse(TimeConverter.is_valid_time_format("1000MS"))
    
    def test_get_supported_units(self):
        """测试获取支持的时间单位"""
        units = TimeConverter.get_supported_units()
        self.assertIn("FS", units)
        self.assertIn("PS", units)
        self.assertIn("NS", units)
        self.assertEqual(len(units), 3)
    
    def test_is_reset_period_violation(self):
        """测试复位期间违例判断"""
        # 测试复位期间违例
        self.assertTrue(TimeConverter.is_reset_period_violation(1000, 2000))
        self.assertTrue(TimeConverter.is_reset_period_violation(1000, 1000))  # 边界情况
        self.assertTrue(TimeConverter.is_reset_period_violation(0, 1000))
        
        # 测试非复位期间违例
        self.assertFalse(TimeConverter.is_reset_period_violation(2000, 1000))
        self.assertFalse(TimeConverter.is_reset_period_violation(1001, 1000))
    
    def test_is_reset_period_violation_invalid_inputs(self):
        """测试复位期间违例判断的无效输入"""
        # 测试非整数
        with self.assertRaises(TimeConversionError):
            TimeConverter.is_reset_period_violation(1000.5, 2000)
        
        with self.assertRaises(TimeConversionError):
            TimeConverter.is_reset_period_violation(1000, 2000.5)
        
        # 测试负数
        with self.assertRaises(TimeConversionError):
            TimeConverter.is_reset_period_violation(-1000, 2000)
        
        with self.assertRaises(TimeConversionError):
            TimeConverter.is_reset_period_violation(1000, -2000)
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 测试零值
        self.assertEqual(TimeConverter.to_femtoseconds("0FS"), 0)
        self.assertEqual(TimeConverter.to_femtoseconds("0PS"), 0)
        self.assertEqual(TimeConverter.to_femtoseconds("0NS"), 0)
        
        # 测试小数
        self.assertEqual(TimeConverter.to_femtoseconds("0.1PS"), 100)
        self.assertEqual(TimeConverter.to_femtoseconds("0.001NS"), 1000)
        
        # 测试大数值
        large_value = 999999999
        self.assertEqual(TimeConverter.to_femtoseconds(f"{large_value}FS"), large_value)
        
        # 测试格式化大数值
        formatted = TimeConverter.format_time(999999999000000)
        self.assertTrue(formatted.endswith("NS"))


    def test_compare_time_strings(self):
        """测试时间字符串比较功能"""
        # 测试相等
        self.assertEqual(TimeConverter.compare_time_strings("1000FS", "1PS"), 0)
        self.assertEqual(TimeConverter.compare_time_strings("1000000FS", "1NS"), 0)
        
        # 测试小于
        self.assertEqual(TimeConverter.compare_time_strings("500FS", "1PS"), -1)
        self.assertEqual(TimeConverter.compare_time_strings("1PS", "1NS"), -1)
        
        # 测试大于
        self.assertEqual(TimeConverter.compare_time_strings("2PS", "1000FS"), 1)
        self.assertEqual(TimeConverter.compare_time_strings("2NS", "1PS"), 1)
        
        # 测试无效输入
        with self.assertRaises(TimeConversionError):
            TimeConverter.compare_time_strings("invalid", "1PS")
        
        with self.assertRaises(TimeConversionError):
            TimeConverter.compare_time_strings("1PS", "invalid")
    
    def test_validate_reset_time(self):
        """测试复位时间验证功能"""
        # 测试有效输入
        valid, error = TimeConverter.validate_reset_time("1000FS")
        self.assertTrue(valid)
        self.assertEqual(error, "")
        
        valid, error = TimeConverter.validate_reset_time("1.5PS")
        self.assertTrue(valid)
        self.assertEqual(error, "")
        
        valid, error = TimeConverter.validate_reset_time("2NS")
        self.assertTrue(valid)
        self.assertEqual(error, "")
        
        # 测试无效输入
        valid, error = TimeConverter.validate_reset_time("")
        self.assertFalse(valid)
        self.assertIn("不能为空", error)
        
        valid, error = TimeConverter.validate_reset_time(None)
        self.assertFalse(valid)
        self.assertIn("不能为空", error)
        
        valid, error = TimeConverter.validate_reset_time("0FS")
        self.assertFalse(valid)
        self.assertIn("必须大于0", error)
        
        valid, error = TimeConverter.validate_reset_time("invalid")
        self.assertFalse(valid)
        self.assertIn("格式不正确", error)
    
    def test_get_time_difference(self):
        """测试时间差值计算功能"""
        # 测试自动单位选择
        diff = TimeConverter.get_time_difference(2000, 1000)
        self.assertEqual(diff, "1PS")
        
        diff = TimeConverter.get_time_difference(1000000, 500000)
        self.assertEqual(diff, "500PS")
        
        # 测试指定单位
        diff = TimeConverter.get_time_difference(2000, 1000, "FS")
        self.assertEqual(diff, "1000FS")
        
        diff = TimeConverter.get_time_difference(2000000, 1000000, "NS")
        self.assertEqual(diff, "1NS")
        
        # 测试顺序不影响结果（取绝对值）
        diff1 = TimeConverter.get_time_difference(2000, 1000)
        diff2 = TimeConverter.get_time_difference(1000, 2000)
        self.assertEqual(diff1, diff2)
        
        # 测试无效输入
        with self.assertRaises(TimeConversionError):
            TimeConverter.get_time_difference(-1000, 2000)
        
        with self.assertRaises(TimeConversionError):
            TimeConverter.get_time_difference(1000.5, 2000)
    
    def test_batch_validate_times(self):
        """测试批量时间验证功能"""
        time_strings = [
            "1000FS",    # 有效
            "1.5PS",     # 有效
            "2NS",       # 有效
            "invalid",   # 无效
            "",          # 无效
            "-1000FS",   # 无效
            "1000MS"     # 无效
        ]
        
        valid_times, invalid_times = TimeConverter.batch_validate_times(time_strings)
        
        # 验证有效时间
        self.assertEqual(len(valid_times), 3)
        self.assertEqual(valid_times[0]['original'], "1000FS")
        self.assertEqual(valid_times[0]['fs_value'], 1000)
        self.assertEqual(valid_times[1]['original'], "1.5PS")
        self.assertEqual(valid_times[1]['fs_value'], 1500)
        self.assertEqual(valid_times[2]['original'], "2NS")
        self.assertEqual(valid_times[2]['fs_value'], 2000000)
        
        # 验证无效时间
        self.assertEqual(len(invalid_times), 4)
        invalid_originals = [item['original'] for item in invalid_times]
        self.assertIn("invalid", invalid_originals)
        self.assertIn("", invalid_originals)
        self.assertIn("-1000FS", invalid_originals)
        self.assertIn("1000MS", invalid_originals)
        
        # 验证错误信息不为空
        for invalid_time in invalid_times:
            self.assertTrue(invalid_time['error'])


class TestTimeConverterIntegration(unittest.TestCase):
    """TimeConverter集成测试"""
    
    def test_round_trip_conversion(self):
        """测试往返转换的准确性"""
        test_cases = [
            "1000FS",
            "1.5PS", 
            "2NS",
            "0FS",
            "999FS",
            "1000PS",
            "0.001NS"
        ]
        
        for original in test_cases:
            # 转换为飞秒
            fs_value = TimeConverter.to_femtoseconds(original)
            
            # 解析原始字符串
            original_value, original_unit = TimeConverter.parse_time_unit(original)
            
            # 格式化回原始单位
            formatted = TimeConverter.format_time(fs_value, original_unit)
            
            # 再次转换为飞秒进行比较
            fs_value_2 = TimeConverter.to_femtoseconds(formatted)
            
            # 验证往返转换的准确性
            self.assertEqual(fs_value, fs_value_2, 
                           f"往返转换失败: {original} -> {fs_value} -> {formatted} -> {fs_value_2}")
    
    def test_time_comparison_with_different_units(self):
        """测试不同单位时间的比较"""
        # 1NS = 1000PS = 1000000FS
        time_1ns = TimeConverter.to_femtoseconds("1NS")
        time_1000ps = TimeConverter.to_femtoseconds("1000PS")
        time_1000000fs = TimeConverter.to_femtoseconds("1000000FS")
        
        # 验证相等性
        self.assertEqual(TimeConverter.compare_times(time_1ns, time_1000ps), 0)
        self.assertEqual(TimeConverter.compare_times(time_1000ps, time_1000000fs), 0)
        self.assertEqual(TimeConverter.compare_times(time_1ns, time_1000000fs), 0)
        
        # 测试不同时间的比较
        time_2ns = TimeConverter.to_femtoseconds("2NS")
        self.assertEqual(TimeConverter.compare_times(time_1ns, time_2ns), -1)
        self.assertEqual(TimeConverter.compare_times(time_2ns, time_1ns), 1)


if __name__ == '__main__':
    unittest.main()