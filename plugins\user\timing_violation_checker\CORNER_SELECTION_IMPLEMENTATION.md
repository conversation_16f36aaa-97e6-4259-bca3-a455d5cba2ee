# Corner选择逻辑实现文档

## 概述

本文档描述了任务4.2"实现corner选择逻辑"的完整实现，包括合法corner列表、下拉选择功能和用户选择验证。

## 实现的功能

### 1. 合法Corner列表 (npg_f1_ssg到npg_f3_tt)

在`models.py`中定义了完整的合法corner列表：

```python
VALID_CORNERS = [
    "npg_f1_ssg",
    "npg_f1_tt", 
    "npg_f1_ff",
    "npg_f2_ssg",
    "npg_f2_tt",
    "npg_f2_ff", 
    "npg_f3_ssg",
    "npg_f3_tt",
    "npg_f3_ff"
]
```

总共支持9个corner选项，覆盖了从npg_f1到npg_f3的所有频率和工艺角组合。

### 2. Corner下拉选择功能

#### 2.1 CaseInfoExtractor类增强

在`utils.py`中的`CaseInfoExtractor`类添加了以下方法：

- `get_corner_selection_list()`: 获取包含提示文本的选择列表
- `validate_corner_selection()`: 验证用户选择的corner
- `get_corner_index_in_list()`: 获取corner在选择列表中的索引
- `get_corner_from_selection_index()`: 从索引获取corner

#### 2.2 CornerSelectionWidget UI组件

创建了专用的PyQt5组件`CornerSelectionWidget`，提供：

- 下拉选择界面
- 自动验证功能
- 信号发送机制
- 状态管理

### 3. 用户选择验证

#### 3.1 验证规则

- 不能为空或None
- 不能是提示文本"请选择Corner..."
- 必须在合法corner列表中
- 自动处理空白字符

#### 3.2 验证方法

```python
def validate_corner_selection(corner: str) -> Tuple[bool, str]:
    """验证用户选择的corner
    
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
```

## API文档

### CaseInfoExtractor新增方法

#### get_corner_selection_list()
```python
@staticmethod
def get_corner_selection_list() -> list:
    """获取corner选择列表（包含提示选项）
    
    Returns:
        list: ["请选择Corner...", "npg_f1_ssg", ...]
    """
```

#### validate_corner_selection(corner)
```python
@staticmethod
def validate_corner_selection(corner: str) -> Tuple[bool, str]:
    """验证用户选择的corner
    
    Args:
        corner: 用户选择的corner
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
```

#### get_corner_index_in_list(corner)
```python
@staticmethod
def get_corner_index_in_list(corner: str) -> int:
    """获取corner在选择列表中的索引
    
    Args:
        corner: corner字符串
        
    Returns:
        int: 索引位置，无效时返回0
    """
```

#### get_corner_from_selection_index(index)
```python
@staticmethod
def get_corner_from_selection_index(index: int) -> Optional[str]:
    """从选择列表索引获取corner
    
    Args:
        index: 选择列表中的索引
        
    Returns:
        Optional[str]: corner字符串，无效时返回None
    """
```

### CornerSelectionWidget组件

#### 信号
- `corner_selected(str)`: 选择有效corner时发送
- `selection_cleared()`: 清除选择时发送

#### 主要方法
- `set_corner(corner)`: 设置当前选择
- `get_selected_corner()`: 获取当前选择
- `is_selection_valid()`: 检查选择是否有效
- `clear_selection()`: 清除选择
- `get_validation_message()`: 获取验证消息

## 使用示例

### 1. 基础验证
```python
from utils import CaseInfoExtractor

# 验证corner选择
is_valid, error_msg = CaseInfoExtractor.validate_corner_selection("npg_f1_ssg")
if is_valid:
    print("选择有效")
else:
    print(f"选择无效: {error_msg}")
```

### 2. 获取选择列表
```python
# 获取UI显示用的选择列表
selection_list = CaseInfoExtractor.get_corner_selection_list()
# ['请选择Corner...', 'npg_f1_ssg', 'npg_f1_tt', ...]
```

### 3. 索引操作
```python
# 获取corner的索引
index = CaseInfoExtractor.get_corner_index_in_list("npg_f1_ssg")  # 返回1

# 从索引获取corner
corner = CaseInfoExtractor.get_corner_from_selection_index(1)  # 返回"npg_f1_ssg"
```

### 4. UI组件使用
```python
from utils import CornerSelectionWidget

# 创建组件
corner_widget = CornerSelectionWidget()

# 连接信号
corner_widget.corner_selected.connect(on_corner_selected)
corner_widget.selection_cleared.connect(on_selection_cleared)

# 设置选择
corner_widget.set_corner("npg_f1_ssg")

# 获取选择
selected_corner = corner_widget.get_selected_corner()
```

## 测试覆盖

### 1. 单元测试
- `test_corner_selection.py`: 完整的功能测试
- 覆盖验证、列表生成、索引操作、边界情况

### 2. 演示程序
- `demo_corner_selection.py`: 交互式演示
- 支持GUI和命令行两种模式

### 3. 测试用例
- 有效corner验证
- 无效corner处理
- 空白字符处理
- 大小写敏感性
- 索引转换正确性
- UI组件功能

## 集成说明

### 与现有系统集成
- 扩展了`CaseInfoExtractor`类的功能
- 保持与现有API的兼容性
- 遵循现有的错误处理模式

### 与其他任务的关系
- 依赖任务4.1的`CaseInfoExtractor`基础实现
- 为后续UI任务提供corner选择组件
- 支持用例信息提取和验证流程

## 错误处理

### 验证错误类型
1. 空值错误: "请选择一个有效的Corner"
2. 提示文本错误: "请选择一个有效的Corner"
3. 无效corner错误: "不合法的Corner: xxx，合法选项: ..."

### 容错机制
- 自动处理空白字符
- 提供详细的错误信息
- 支持索引边界检查
- UI组件状态自动管理

## 性能考虑

- 使用静态方法减少内存开销
- Corner列表使用copy()避免意外修改
- 索引操作时间复杂度O(1)
- UI组件延迟加载（仅在PyQt5可用时创建）

## 总结

任务4.2已完全实现，提供了：

1. ✅ 合法corner列表(npg_f1_ssg到npg_f3_tt) - 9个选项
2. ✅ corner下拉选择功能 - CornerSelectionWidget组件
3. ✅ 用户选择验证 - 完整的验证逻辑和错误处理

所有功能都经过了全面测试，并提供了演示程序和详细文档。实现满足了需求3.3和3.5的所有要求。