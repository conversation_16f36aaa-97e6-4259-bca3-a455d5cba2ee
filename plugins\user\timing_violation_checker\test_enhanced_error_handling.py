"""
增强错误处理功能测试

测试ViolationParser的增强错误处理功能，包括错误恢复建议、详细错误报告等。
"""

import os
import sys
import tempfile
from typing import List

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from violation_parser import ViolationParser
from models import ViolationData
from exceptions import FileParseError


def create_test_log_file(content: str, filename: str = "vio_summary.log") -> str:
    """创建测试用的日志文件
    
    Args:
        content: 文件内容
        filename: 文件名
        
    Returns:
        str: 临时文件路径
    """
    # 创建临时目录结构
    temp_dir = tempfile.mkdtemp()
    case_dir = os.path.join(temp_dir, "test_case")
    log_dir = os.path.join(case_dir, "log")
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建日志文件
    file_path = os.path.join(log_dir, filename)
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return file_path


def test_enhanced_file_accessibility_errors():
    """测试增强的文件可访问性错误处理"""
    print("测试增强的文件可访问性错误处理...")
    
    parser = ViolationParser()
    
    # 测试不存在的目录
    try:
        parser.parse_file("/nonexistent/directory/log/vio_summary.log")
        assert False, "应该抛出FileParseError异常"
    except FileParseError as e:
        print(f"✓ 不存在目录错误处理: {e.get_user_message()}")
        assert "目录不存在" in str(e)
    
    # 测试空文件
    empty_file = create_test_log_file("")
    try:
        parser.parse_file(empty_file)
        assert False, "应该抛出FileParseError异常"
    except FileParseError as e:
        print(f"✓ 空文件错误处理: {e.get_user_message()}")
        assert "空文件" in str(e)
    finally:
        if os.path.exists(empty_file):
            os.remove(empty_file)
            os.rmdir(os.path.dirname(empty_file))
            os.rmdir(os.path.dirname(os.path.dirname(empty_file)))
    
    # 测试目录而不是文件（创建一个名为vio_summary.log的目录）
    temp_dir = tempfile.mkdtemp()
    case_dir = os.path.join(temp_dir, "test_case")
    log_dir = os.path.join(case_dir, "log")
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建一个名为vio_summary.log的目录而不是文件
    fake_file_dir = os.path.join(log_dir, "vio_summary.log")
    os.makedirs(fake_file_dir, exist_ok=True)
    
    try:
        parser.parse_file(fake_file_dir)  # 传入目录而不是文件
        assert False, "应该抛出FileParseError异常"
    except FileParseError as e:
        print(f"✓ 目录错误处理: {e.get_user_message()}")
        assert "目录" in str(e) or "文件" in str(e)
    finally:
        os.rmdir(fake_file_dir)
        os.rmdir(log_dir)
        os.rmdir(case_dir)
        os.rmdir(temp_dir)
    
    print("✓ 增强的文件可访问性错误处理测试通过")


def test_error_recovery_suggestions():
    """测试错误恢复建议功能"""
    print("测试错误恢复建议功能...")
    
    # 创建包含各种错误的测试数据
    test_content = """
NUM: 1
Hier: tb_top.cpu.core
Time: 1000PS
Check: setup(posedge clk, data)
----
NUM: invalid_number
Hier: tb_top.memory.ctrl
Time: 500NS
Check: hold(posedge clk, addr)
----
NUM: 3
Hier: 
Time: 1500FS
Check: setup(negedge rst, enable)
----
NUM: 4
Hier: tb_top.valid.module
Time: invalid_time_format
Check: valid check
----
Hier: tb_top.missing.num
Time: 2000PS
Check: missing num field
----
NUM: 6
Hier: tb_top.complete.module
Time: 3000FS
Check: 
"""
    
    file_path = create_test_log_file(test_content)
    
    try:
        parser = ViolationParser()
        violations = parser.parse_file(file_path)
        
        print(f"成功解析 {len(violations)} 个违例")
        
        # 获取错误恢复建议
        suggestions = parser.get_error_recovery_suggestions()
        print(f"错误恢复建议数量: {len(suggestions)}")
        
        for i, suggestion in enumerate(suggestions, 1):
            print(f"  {i}. {suggestion}")
        
        # 验证建议内容
        suggestion_text = " ".join(suggestions)
        assert "缺少必需字段" in suggestion_text or "NUM字段格式错误" in suggestion_text, "应该包含字段相关建议"
        
        print("✓ 错误恢复建议功能测试通过")
        
    except Exception as e:
        print(f"✗ 错误恢复建议功能测试失败: {str(e)}")
        raise
    finally:
        if os.path.exists(file_path):
            os.remove(file_path)
            os.rmdir(os.path.dirname(file_path))
            os.rmdir(os.path.dirname(os.path.dirname(file_path)))


def test_detailed_error_report():
    """测试详细错误报告功能"""
    print("测试详细错误报告功能...")
    
    # 创建包含错误的测试数据
    test_content = """
NUM: 1
Hier: tb_top.cpu.core
Time: 1000PS
Check: setup(posedge clk, data)
----
NUM: invalid
Hier: tb_top.error.module
Time: 500NS
Check: error case
----
NUM: 3
Hier: tb_top.warning.module
Time: unknown_unit
Check: warning case
"""
    
    file_path = create_test_log_file(test_content)
    
    try:
        parser = ViolationParser()
        violations = parser.parse_file(file_path)
        
        # 创建错误报告
        error_report = parser.create_error_report(file_path)
        print("错误报告内容:")
        print(error_report)
        
        # 验证报告内容
        assert "时序违例解析错误报告" in error_report, "报告应包含标题"
        assert file_path in error_report, "报告应包含文件路径"
        assert "错误统计" in error_report, "报告应包含错误统计"
        
        if parser.has_parse_errors():
            assert "详细错误信息" in error_report, "报告应包含详细错误信息"
        
        if parser.has_warnings():
            assert "警告信息" in error_report, "报告应包含警告信息"
        
        suggestions = parser.get_error_recovery_suggestions()
        if suggestions:
            assert "恢复建议" in error_report, "报告应包含恢复建议"
        
        print("✓ 详细错误报告功能测试通过")
        
    except Exception as e:
        print(f"✗ 详细错误报告功能测试失败: {str(e)}")
        raise
    finally:
        if os.path.exists(file_path):
            os.remove(file_path)
            os.rmdir(os.path.dirname(file_path))
            os.rmdir(os.path.dirname(os.path.dirname(file_path)))


def test_parse_statistics():
    """测试解析统计信息功能"""
    print("测试解析统计信息功能...")
    
    test_content = """
NUM: 1
Hier: tb_top.cpu.core
Time: 1000PS
Check: setup(posedge clk, data)
----
NUM: error
Hier: tb_top.error.module
Time: 500NS
Check: error case
"""
    
    file_path = create_test_log_file(test_content)
    
    try:
        parser = ViolationParser()
        violations = parser.parse_file(file_path)
        
        # 获取解析统计信息
        stats = parser.get_parse_statistics()
        
        print("解析统计信息:")
        print(f"  总错误数: {stats['total_errors']}")
        print(f"  总警告数: {stats['total_warnings']}")
        print(f"  有严重错误: {stats['has_critical_errors']}")
        print(f"  有警告: {stats['has_warnings']}")
        print(f"  恢复建议数: {len(stats['recovery_suggestions'])}")
        
        # 验证统计信息结构
        required_keys = ['total_errors', 'total_warnings', 'error_details', 
                        'warning_list', 'recovery_suggestions', 'has_critical_errors', 'has_warnings']
        for key in required_keys:
            assert key in stats, f"统计信息应包含 {key} 字段"
        
        print("✓ 解析统计信息功能测试通过")
        
    except Exception as e:
        print(f"✗ 解析统计信息功能测试失败: {str(e)}")
        raise
    finally:
        if os.path.exists(file_path):
            os.remove(file_path)
            os.rmdir(os.path.dirname(file_path))
            os.rmdir(os.path.dirname(os.path.dirname(file_path)))


def test_enhanced_validation():
    """测试增强的验证功能"""
    print("测试增强的验证功能...")
    
    # 测试低成功率警告
    test_content = """
NUM: 1
Hier: tb_top.cpu.core
Time: 1000PS
Check: setup(posedge clk, data)
----
invalid block 1
----
invalid block 2
----
invalid block 3
----
invalid block 4
"""
    
    file_path = create_test_log_file(test_content)
    
    try:
        parser = ViolationParser()
        violations = parser.parse_file(file_path)
        
        # 检查是否有成功率警告
        warnings = parser.get_warnings()
        success_rate_warnings = [w for w in warnings if "成功率" in w]
        
        print(f"成功解析 {len(violations)} 个违例")
        print(f"成功率警告: {len(success_rate_warnings)} 个")
        
        if success_rate_warnings:
            for warning in success_rate_warnings:
                print(f"  - {warning}")
        
        # 应该有成功率相关的警告
        assert len(success_rate_warnings) > 0, "应该有成功率警告"
        
        print("✓ 增强的验证功能测试通过")
        
    except Exception as e:
        print(f"✗ 增强的验证功能测试失败: {str(e)}")
        raise
    finally:
        if os.path.exists(file_path):
            os.remove(file_path)
            os.rmdir(os.path.dirname(file_path))
            os.rmdir(os.path.dirname(os.path.dirname(file_path)))


def run_all_enhanced_tests():
    """运行所有增强错误处理测试"""
    print("开始运行增强错误处理测试...")
    print("=" * 60)
    
    try:
        test_enhanced_file_accessibility_errors()
        print()
        
        test_error_recovery_suggestions()
        print()
        
        test_detailed_error_report()
        print()
        
        test_parse_statistics()
        print()
        
        test_enhanced_validation()
        print()
        
        print("=" * 60)
        print("✓ 所有增强错误处理测试通过！")
        
    except Exception as e:
        print("=" * 60)
        print(f"✗ 增强错误处理测试失败: {str(e)}")
        raise


if __name__ == "__main__":
    run_all_enhanced_tests()