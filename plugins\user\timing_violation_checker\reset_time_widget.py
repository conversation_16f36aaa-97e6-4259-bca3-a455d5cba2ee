"""
复位时间输入组件

提供复位时间输入、验证和应用功能，支持自动确认复位期间的时序违例。
"""

from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QGroupBox, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    # 尝试相对导入（在插件环境中）
    from .utils import TimeConverter
    from .exceptions import TimeConversionError
    from .models import ViolationData
except ImportError:
    # 如果相对导入失败，尝试直接导入（在测试环境中）
    from utils import TimeConverter
    from exceptions import TimeConversionError
    from models import ViolationData


class ResetTimeWidget(QWidget):
    """复位时间输入组件
    
    提供复位时间输入界面，包含输入验证、格式提示和应用功能。
    当复位时间设置后，会自动标记复位期间的时序违例。
    """
    
    # 自定义信号
    reset_time_applied = pyqtSignal(int)  # 复位时间应用成功，参数为飞秒值
    reset_time_cleared = pyqtSignal()     # 复位时间清除
    validation_error = pyqtSignal(str)    # 验证错误，参数为错误信息
    
    def __init__(self, parent=None):
        """初始化复位时间输入组件
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        # 初始化数据
        self.current_reset_time_fs = 0
        self.violations = []  # 当前违例列表
        
        # 设置界面
        self.setup_ui()
        
        # 连接信号
        self.connect_signals()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(5)
        
        # 创建分组框
        group_box = QGroupBox("复位时间设置")
        group_box.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        # 创建分组框内部布局
        group_layout = QVBoxLayout()
        group_layout.setSpacing(10)
        
        # 添加说明文本
        info_label = QLabel("设置复位时间后，系统将自动标记复位期间的时序违例为已确认状态")
        info_label.setStyleSheet("color: #666666; font-size: 12px;")
        info_label.setWordWrap(True)
        group_layout.addWidget(info_label)
        
        # 创建输入行布局
        input_layout = QHBoxLayout()
        input_layout.setSpacing(10)
        
        # 复位时间标签
        time_label = QLabel("复位时间:")
        time_label.setMinimumWidth(80)
        time_label.setStyleSheet("font-weight: bold;")
        input_layout.addWidget(time_label)
        
        # 复位时间输入框
        self.time_input = QLineEdit()
        self.time_input.setPlaceholderText("例如: 1000PS, 500NS, 1500FS")
        self.time_input.setMinimumWidth(200)
        self.time_input.setMaximumWidth(300)
        self.time_input.setStyleSheet("""
            QLineEdit {
                padding: 5px;
                border: 1px solid #cccccc;
                border-radius: 3px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #4a9eff;
            }
            QLineEdit.error {
                border-color: #dc3545;
                background-color: #fff5f5;
            }
            QLineEdit.valid {
                border-color: #28a745;
                background-color: #f8fff8;
            }
        """)
        input_layout.addWidget(self.time_input)
        
        # 应用按钮
        self.apply_button = QPushButton("应用")
        self.apply_button.setMinimumWidth(80)
        self.apply_button.setEnabled(False)
        self.apply_button.setStyleSheet("""
            QPushButton {
                padding: 5px 15px;
                background-color: #4a9eff;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover:enabled {
                background-color: #3a8eef;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        input_layout.addWidget(self.apply_button)
        
        # 清除按钮
        self.clear_button = QPushButton("清除")
        self.clear_button.setMinimumWidth(80)
        self.clear_button.setEnabled(False)
        self.clear_button.setStyleSheet("""
            QPushButton {
                padding: 5px 15px;
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover:enabled {
                background-color: #5a6268;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        input_layout.addWidget(self.clear_button)
        
        # 添加弹性空间
        input_layout.addStretch()
        
        group_layout.addLayout(input_layout)
        
        # 格式提示标签
        self.format_hint_label = QLabel("支持的时间格式: 数字+单位 (FS=飞秒, PS=皮秒, NS=纳秒)")
        self.format_hint_label.setStyleSheet("color: #888888; font-size: 11px;")
        group_layout.addWidget(self.format_hint_label)
        
        # 状态显示标签
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("font-size: 12px; margin-top: 5px;")
        self.status_label.setWordWrap(True)
        group_layout.addWidget(self.status_label)
        
        # 设置分组框布局
        group_box.setLayout(group_layout)
        main_layout.addWidget(group_box)
        
        # 设置主布局
        self.setLayout(main_layout)
    
    def connect_signals(self):
        """连接信号和槽"""
        # 输入框文本变化
        self.time_input.textChanged.connect(self.on_input_changed)
        
        # 输入框回车键
        self.time_input.returnPressed.connect(self.on_apply_clicked)
        
        # 按钮点击
        self.apply_button.clicked.connect(self.on_apply_clicked)
        self.clear_button.clicked.connect(self.on_clear_clicked)
    
    def on_input_changed(self, text: str):
        """处理输入文本变化
        
        Args:
            text: 输入的文本
        """
        # 清除之前的状态样式
        self.time_input.setProperty("class", "")
        self.time_input.style().unpolish(self.time_input)
        self.time_input.style().polish(self.time_input)
        
        # 清除状态显示
        self.status_label.setText("")
        
        if not text.strip():
            # 输入为空
            self.apply_button.setEnabled(False)
            self.format_hint_label.setStyleSheet("color: #888888; font-size: 11px;")
            return
        
        # 验证输入格式
        is_valid, error_msg = TimeConverter.validate_reset_time(text)
        
        if is_valid:
            # 输入有效
            self.time_input.setProperty("class", "valid")
            self.time_input.style().unpolish(self.time_input)
            self.time_input.style().polish(self.time_input)
            
            self.apply_button.setEnabled(True)
            self.format_hint_label.setStyleSheet("color: #28a745; font-size: 11px;")
            
            # 显示转换后的值
            try:
                fs_value = TimeConverter.to_femtoseconds(text)
                formatted_time = TimeConverter.format_time(fs_value)
                self.status_label.setText(f"转换结果: {formatted_time} ({fs_value:,} FS)")
                self.status_label.setStyleSheet("color: #28a745; font-size: 12px; margin-top: 5px;")
            except TimeConversionError:
                pass
        else:
            # 输入无效
            self.time_input.setProperty("class", "error")
            self.time_input.style().unpolish(self.time_input)
            self.time_input.style().polish(self.time_input)
            
            self.apply_button.setEnabled(False)
            self.format_hint_label.setStyleSheet("color: #dc3545; font-size: 11px;")
            
            # 显示错误信息
            self.status_label.setText(f"错误: {error_msg}")
            self.status_label.setStyleSheet("color: #dc3545; font-size: 12px; margin-top: 5px;")
    
    def on_apply_clicked(self):
        """处理应用按钮点击"""
        input_text = self.time_input.text().strip()
        
        if not input_text:
            self.show_error_message("请输入复位时间")
            return
        
        try:
            # 验证并转换时间
            is_valid, error_msg = TimeConverter.validate_reset_time(input_text)
            if not is_valid:
                self.show_error_message(error_msg)
                return
            
            # 转换为飞秒
            fs_value = TimeConverter.to_femtoseconds(input_text)
            
            # 更新当前复位时间
            self.current_reset_time_fs = fs_value
            
            # 启用清除按钮
            self.clear_button.setEnabled(True)
            
            # 更新状态显示
            formatted_time = TimeConverter.format_time(fs_value)
            self.status_label.setText(f"复位时间已设置: {formatted_time} ({fs_value:,} FS)")
            self.status_label.setStyleSheet("color: #28a745; font-size: 12px; margin-top: 5px;")
            
            # 发送信号
            self.reset_time_applied.emit(fs_value)
            
            # 如果有违例数据，自动处理复位期间的违例
            if self.violations:
                auto_confirmed_count = self.apply_auto_confirmation()
                if auto_confirmed_count > 0:
                    self.status_label.setText(
                        f"复位时间已设置: {formatted_time}\n"
                        f"自动确认了 {auto_confirmed_count} 个复位期间的违例"
                    )
            
            print(f"复位时间应用成功: {input_text} -> {fs_value} FS")
            
        except TimeConversionError as e:
            self.show_error_message(e.get_user_message())
        except Exception as e:
            self.show_error_message(f"应用复位时间时发生错误: {str(e)}")
    
    def on_clear_clicked(self):
        """处理清除按钮点击"""
        try:
            # 清除输入
            self.time_input.clear()
            
            # 重置状态
            self.current_reset_time_fs = 0
            self.clear_button.setEnabled(False)
            self.apply_button.setEnabled(False)
            
            # 清除状态显示
            self.status_label.setText("复位时间已清除")
            self.status_label.setStyleSheet("color: #6c757d; font-size: 12px; margin-top: 5px;")
            
            # 重置格式提示样式
            self.format_hint_label.setStyleSheet("color: #888888; font-size: 11px;")
            
            # 清除输入框样式
            self.time_input.setProperty("class", "")
            self.time_input.style().unpolish(self.time_input)
            self.time_input.style().polish(self.time_input)
            
            # 发送信号
            self.reset_time_cleared.emit()
            
            print("复位时间已清除")
            
        except Exception as e:
            self.show_error_message(f"清除复位时间时发生错误: {str(e)}")
    
    def set_reset_time(self, time_str: str):
        """设置复位时间
        
        Args:
            time_str: 时间字符串
        """
        try:
            if not time_str:
                self.on_clear_clicked()
                return
            
            # 验证时间格式
            is_valid, error_msg = TimeConverter.validate_reset_time(time_str)
            if not is_valid:
                self.show_error_message(error_msg)
                return
            
            # 设置输入框文本
            self.time_input.setText(time_str)
            
            # 触发应用
            self.on_apply_clicked()
            
        except Exception as e:
            self.show_error_message(f"设置复位时间时发生错误: {str(e)}")
    
    def get_reset_time_fs(self) -> int:
        """获取当前复位时间（飞秒）
        
        Returns:
            int: 复位时间飞秒值
        """
        return self.current_reset_time_fs
    
    def get_reset_time_string(self) -> str:
        """获取当前复位时间字符串
        
        Returns:
            str: 复位时间字符串
        """
        return self.time_input.text().strip()
    
    def has_reset_time(self) -> bool:
        """检查是否已设置复位时间
        
        Returns:
            bool: 是否已设置复位时间
        """
        return self.current_reset_time_fs > 0
    
    def set_violations(self, violations: list):
        """设置违例数据列表
        
        Args:
            violations: 违例数据列表
        """
        self.violations = violations or []
        
        # 如果已设置复位时间，重新应用自动确认
        if self.has_reset_time():
            self.apply_auto_confirmation()
    
    def apply_auto_confirmation(self) -> int:
        """应用自动确认逻辑
        
        Returns:
            int: 自动确认的违例数量
        """
        if not self.has_reset_time() or not self.violations:
            return 0
        
        auto_confirmed_count = 0
        
        for violation in self.violations:
            # 检查是否为复位期间违例且未手动确认
            if (not violation.is_confirmed() and 
                TimeConverter.is_reset_period_violation(violation.time_fs, self.current_reset_time_fs)):
                
                # 设置自动确认
                violation.set_auto_confirmation("复位期间时序违例，可以忽略")
                auto_confirmed_count += 1
        
        return auto_confirmed_count
    
    def update_violation_status(self, violations: list):
        """更新违例状态（当复位时间变化时重新计算）
        
        Args:
            violations: 违例数据列表
        """
        self.violations = violations or []
        
        if not self.has_reset_time():
            return
        
        # 重新计算自动确认状态
        from .models import ViolationStatus
        
        for violation in self.violations:
            # 如果是自动确认的违例，检查是否仍在复位期间
            if violation.is_auto_confirmed():
                if not TimeConverter.is_reset_period_violation(violation.time_fs, self.current_reset_time_fs):
                    # 不再是复位期间违例，恢复为待确认状态
                    violation.status = ViolationStatus.PENDING
                    violation.auto_confirmed = False
                    violation.confirmer = ""
                    violation.reason = ""
                    violation.confirmation_result = None
            else:
                # 如果是待确认的违例，检查是否应该自动确认
                if (not violation.is_confirmed() and 
                    TimeConverter.is_reset_period_violation(violation.time_fs, self.current_reset_time_fs)):
                    violation.set_auto_confirmation("复位期间时序违例，可以忽略")
    
    def get_auto_confirmation_count(self) -> int:
        """获取自动确认的违例数量
        
        Returns:
            int: 自动确认的违例数量
        """
        if not self.violations:
            return 0
        
        return sum(1 for v in self.violations if v.is_auto_confirmed())
    
    def get_reset_period_violations(self) -> list:
        """获取复位期间的违例列表
        
        Returns:
            list: 复位期间的违例列表
        """
        if not self.has_reset_time() or not self.violations:
            return []
        
        return [v for v in self.violations 
                if TimeConverter.is_reset_period_violation(v.time_fs, self.current_reset_time_fs)]
    
    def show_error_message(self, message: str):
        """显示错误消息
        
        Args:
            message: 错误消息
        """
        # 发送验证错误信号
        self.validation_error.emit(message)
        
        # 也可以显示工具提示
        self.time_input.setToolTip(message)
        
        print(f"复位时间组件错误: {message}")
    
    def clear_error_message(self):
        """清除错误消息"""
        self.time_input.setToolTip("")
    
    def set_enabled(self, enabled: bool):
        """设置组件启用状态
        
        Args:
            enabled: 是否启用
        """
        self.time_input.setEnabled(enabled)
        
        # 只有在输入有效时才启用应用按钮
        if enabled and self.time_input.text().strip():
            is_valid, _ = TimeConverter.validate_reset_time(self.time_input.text())
            self.apply_button.setEnabled(is_valid)
        else:
            self.apply_button.setEnabled(False)
        
        # 只有在已设置复位时间时才启用清除按钮
        self.clear_button.setEnabled(enabled and self.has_reset_time())
    
    def get_status_info(self) -> dict:
        """获取状态信息
        
        Returns:
            dict: 状态信息字典
        """
        return {
            'has_reset_time': self.has_reset_time(),
            'reset_time_fs': self.current_reset_time_fs,
            'reset_time_string': self.get_reset_time_string(),
            'formatted_time': TimeConverter.format_time(self.current_reset_time_fs) if self.has_reset_time() else "",
            'auto_confirmed_count': self.get_auto_confirmation_count(),
            'reset_period_violations': len(self.get_reset_period_violations()),
            'is_input_valid': TimeConverter.validate_reset_time(self.time_input.text())[0] if self.time_input.text().strip() else False
        }
    
    def reset_to_default(self):
        """重置到默认状态"""
        self.on_clear_clicked()
        self.violations.clear()
        self.clear_error_message()
    
    def validate_current_input(self) -> tuple:
        """验证当前输入
        
        Returns:
            tuple: (是否有效, 错误信息)
        """
        input_text = self.time_input.text().strip()
        if not input_text:
            return True, ""  # 空输入被认为是有效的（表示未设置）
        
        return TimeConverter.validate_reset_time(input_text)
    
    def get_formatted_reset_time(self) -> str:
        """获取格式化的复位时间显示
        
        Returns:
            str: 格式化的复位时间字符串
        """
        if not self.has_reset_time():
            return "未设置"
        
        return TimeConverter.format_time(self.current_reset_time_fs)
    
    def is_violation_in_reset_period(self, violation: ViolationData) -> bool:
        """检查违例是否在复位期间
        
        Args:
            violation: 违例数据
            
        Returns:
            bool: 是否在复位期间
        """
        if not self.has_reset_time():
            return False
        
        return TimeConverter.is_reset_period_violation(violation.time_fs, self.current_reset_time_fs)