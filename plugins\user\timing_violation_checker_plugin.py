"""
时序违例确认插件

用于解析仿真用例的时序违例日志文件(vio_summary.log)，生成可交互的检查清单，
支持用户逐一确认时序违例，并提供导出功能。
"""

import os
import sys
from PyQt5.QtWidgets import QAction, QMessageBox
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QIcon

# 添加插件路径到系统路径
plugin_dir = os.path.dirname(os.path.abspath(__file__))
if plugin_dir not in sys.path:
    sys.path.insert(0, plugin_dir)

from plugins.base import PluginBase


class TimingViolationCheckerPlugin(PluginBase):
    """时序违例确认插件主类"""
    
    def __init__(self):
        """初始化插件"""
        super().__init__()
        self.dialog_window = None
        self.main_window = None
        self.menu_action = None
        self.plugin_config = {}
        
        # 设置插件配置
        self.admin_controlled = False  # 不需要管理员权限
        self.default_enabled = True    # 默认启用
        
        # 加载插件配置
        self._load_plugin_config()
        
    @property
    def name(self) -> str:
        """插件名称"""
        return "时序违例确认工具"
    
    @property
    def version(self) -> str:
        """插件版本"""
        return "1.0.0"
    
    @property
    def description(self) -> str:
        """插件描述"""
        return "解析和确认后仿真时序违例日志文件，支持自动化处理和导出功能"
    
    def initialize(self, main_window):
        """初始化插件
        
        Args:
            main_window: 主窗口实例
        """
        try:
            self.main_window = main_window
            
            # 创建菜单项（遵循现有GUI设计风格）
            self.menu_action = QAction("时序违例确认工具", main_window)
            self.menu_action.setStatusTip("打开时序违例确认工具")
            self.menu_action.triggered.connect(self.show_dialog)
            
            # 设置菜单项图标（如果有的话）
            # 可以在后续版本中添加图标支持
            
            # 添加到工具菜单（遵循现有GUI设计风格）
            if hasattr(main_window, 'tools_menu'):
                main_window.tools_menu.addAction(self.menu_action)
            elif hasattr(main_window, 'plugin_menu'):
                # 备用方案：如果没有tools_menu，使用plugin_menu
                main_window.plugin_menu.addAction(self.menu_action)
            
            print(f"[插件] {self.name} v{self.version} 初始化成功")
            
        except Exception as e:
            print(f"[插件] {self.name} 初始化失败: {str(e)}")
            QMessageBox.critical(main_window, "插件初始化失败", 
                               f"{self.name} 初始化失败:\n{str(e)}")
    
    def cleanup(self):
        """清理插件资源"""
        try:
            # 保存配置
            self._save_plugin_config()
            
            # 关闭对话框窗口
            if self.dialog_window:
                self.dialog_window.close()
                self.dialog_window = None
            
            # 移除菜单项
            if self.menu_action and self.main_window:
                if hasattr(self.main_window, 'tools_menu'):
                    self.main_window.tools_menu.removeAction(self.menu_action)
                elif hasattr(self.main_window, 'plugin_menu'):
                    # 备用方案：如果没有tools_menu，使用plugin_menu
                    self.main_window.plugin_menu.removeAction(self.menu_action)
            
            # 清理引用
            self.main_window = None
            self.menu_action = None
            self.plugin_config = {}
            
            print(f"[插件] {self.name} 清理完成")
            
        except Exception as e:
            print(f"[插件] {self.name} 清理失败: {str(e)}")
    
    def show_dialog(self):
        """显示时序违例确认对话框"""
        try:
            # 延迟导入对话框类，避免初始化时的依赖问题
            from timing_violation_checker.timing_violation_dialog import TimingViolationDialog
            
            # 如果对话框已存在且可见，则激活它
            if self.dialog_window is not None:
                if self.dialog_window.isVisible():
                    self.dialog_window.raise_()
                    self.dialog_window.activateWindow()
                    return
                else:
                    # 如果对话框存在但不可见，重新创建
                    self.dialog_window = None
            
            # 创建新的对话框实例，传递插件配置
            self.dialog_window = TimingViolationDialog(self.main_window)
            
            # 应用保存的窗口几何配置
            self._apply_window_geometry()
            
            # 连接对话框关闭信号，清理引用
            self.dialog_window.finished.connect(self._on_dialog_closed)
            
            # 显示非模态对话框
            self.dialog_window.show()
            self.dialog_window.raise_()
            self.dialog_window.activateWindow()
            
        except ImportError as e:
            error_msg = f"无法导入时序违例确认对话框:\n{str(e)}\n\n请确保插件文件完整且依赖项已正确安装。"
            QMessageBox.critical(self.main_window, "导入错误", error_msg)
            print(f"[插件] {self.name} 导入错误: {str(e)}")
        except Exception as e:
            error_msg = f"显示时序违例确认对话框时发生错误:\n{str(e)}"
            QMessageBox.critical(self.main_window, "错误", error_msg)
            print(f"[插件] {self.name} 显示对话框错误: {str(e)}")
    
    def _on_dialog_closed(self):
        """对话框关闭时的回调"""
        # 保存窗口几何配置
        self._save_window_geometry()
        self.dialog_window = None
    
    def _apply_window_geometry(self):
        """应用窗口几何配置"""
        if not self.dialog_window:
            return
            
        try:
            geometry = self.get_config('window_geometry', {})
            if geometry:
                width = geometry.get('width', 1000)
                height = geometry.get('height', 700)
                x = geometry.get('x', 100)
                y = geometry.get('y', 100)
                
                self.dialog_window.setGeometry(x, y, width, height)
        except Exception as e:
            print(f"[插件] {self.name} 应用窗口几何配置失败: {str(e)}")
    
    def _save_window_geometry(self):
        """保存窗口几何配置"""
        if not self.dialog_window:
            return
            
        try:
            geometry = self.dialog_window.geometry()
            self.set_config('window_geometry', {
                'width': geometry.width(),
                'height': geometry.height(),
                'x': geometry.x(),
                'y': geometry.y()
            })
        except Exception as e:
            print(f"[插件] {self.name} 保存窗口几何配置失败: {str(e)}")
    
    def _load_plugin_config(self):
        """加载插件配置"""
        try:
            import json
            config_file = "timing_violation_checker_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.plugin_config = json.load(f)
        except Exception as e:
            print(f"[插件] {self.name} 加载配置失败: {str(e)}")
            self.plugin_config = {}
    
    def _save_plugin_config(self):
        """保存插件配置"""
        try:
            import json
            config_file = "timing_violation_checker_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.plugin_config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"[插件] {self.name} 保存配置失败: {str(e)}")
    
    def get_config(self, key, default=None):
        """获取配置项
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return self.plugin_config.get(key, default)
    
    def set_config(self, key, value):
        """设置配置项
        
        Args:
            key: 配置键
            value: 配置值
        """
        self.plugin_config[key] = value
        self._save_plugin_config()


# 插件实例
plugin_instance = TimingViolationCheckerPlugin()