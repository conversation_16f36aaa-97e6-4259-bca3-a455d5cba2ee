#!/usr/bin/env python3
"""
测试导出界面和错误处理功能

验证任务10.2的实现：
- 添加导出按钮和格式选择
- 实现文件保存对话框
- 显示导出成功提示和文件位置
- 处理导出失败的错误提示
"""

import sys
import os
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from models import ViolationData, ViolationStatus, CaseInfo, ExportConfig, ConfirmationResult
    from export_manager import ExportManager
    from exceptions import ExportError
    
    # 尝试导入PyQt5相关模块
    try:
        from PyQt5.QtWidgets import QApplication, QWidget
        from PyQt5.QtCore import Qt
        from PyQt5.QtTest import QTest
        from timing_violation_dialog import TimingViolationDialog
        PYQT_AVAILABLE = True
    except ImportError:
        PYQT_AVAILABLE = False
        
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Required modules not available: {e}")
    MODULES_AVAILABLE = False
    PYQT_AVAILABLE = False


def create_test_violations():
    """创建测试违例数据"""
    if not MODULES_AVAILABLE:
        return []
        
    violations = []
    
    # 创建不同状态的违例
    violations.append(ViolationData(
        num=1,
        hier="tb_top.cpu.core",
        time_original="1000000FS",
        time_fs=1000000,
        check="setup(posedge clk, data)"
    ))
    
    violations.append(ViolationData(
        num=2,
        hier="tb_top.mem.ctrl",
        time_original="2000000FS", 
        time_fs=2000000,
        check="hold(posedge clk, addr)",
        status=ViolationStatus.CONFIRMED_OK,
        confirmer="张三",
        reason="已确认无问题",
        confirmation_result=ConfirmationResult.NO_ISSUE
    ))
    
    violations.append(ViolationData(
        num=3,
        hier="tb_top.bus.arbiter",
        time_original="500000FS",
        time_fs=500000,
        check="setup(posedge clk, req)",
        status=ViolationStatus.AUTO_CONFIRMED,
        confirmer="系统自动",
        reason="复位期间时序违例，可以忽略",
        auto_confirmed=True,
        confirmation_result=ConfirmationResult.NO_ISSUE
    ))
    
    violations.append(ViolationData(
        num=4,
        hier="tb_top.io.controller",
        time_original="3000000FS",
        time_fs=3000000,
        check="hold(posedge clk, enable)",
        status=ViolationStatus.CONFIRMED_ISSUE,
        confirmer="李四",
        reason="需要调整时序约束",
        confirmation_result=ConfirmationResult.HAS_ISSUE
    ))
    
    return violations


def create_test_case_info():
    """创建测试用例信息"""
    if not MODULES_AVAILABLE:
        return None
        
    return CaseInfo(
        case_name="test_case_001",
        corner="npg_f1_ssg",
        directory_path="/path/to/test_case_001_npg_f1_ssg"
    )


def test_export_config_validation():
    """测试导出配置验证"""
    print("测试导出配置验证...")
    
    if not MODULES_AVAILABLE:
        print("⚠ 必需模块不可用，跳过配置验证测试")
        return True
    
    # 测试有效配置
    try:
        config = ExportConfig(
            format_type="excel",
            include_auto_confirmed=True,
            file_path="/tmp/test.xlsx"
        )
        print("✓ 有效Excel配置创建成功")
    except Exception as e:
        print(f"✗ 有效Excel配置创建失败: {e}")
        return False
    
    try:
        config = ExportConfig(
            format_type="csv",
            include_auto_confirmed=False,
            only_confirmed=True,
            file_path="/tmp/test.csv"
        )
        print("✓ 有效CSV配置创建成功")
    except Exception as e:
        print(f"✗ 有效CSV配置创建失败: {e}")
        return False
    
    # 测试无效格式
    try:
        config = ExportConfig(format_type="invalid")
        print("✗ 无效格式配置应该失败但没有")
        return False
    except ValueError:
        print("✓ 无效格式配置正确拒绝")
    except Exception as e:
        print(f"✗ 无效格式配置错误类型: {e}")
        return False
    
    return True


def test_export_manager_functionality():
    """测试导出管理器功能"""
    print("\n测试导出管理器功能...")
    
    if not MODULES_AVAILABLE:
        print("⚠ 必需模块不可用，跳过导出管理器测试")
        return True
    
    violations = create_test_violations()
    case_info = {
        'case_name': 'test_case_001',
        'corner': 'npg_f1_ssg'
    }
    
    export_manager = ExportManager()
    
    # 测试默认文件名生成
    try:
        filename = export_manager.get_default_filename(case_info, "excel")
        if "test_case_001" in filename and "npg_f1_ssg" in filename and filename.endswith(".xlsx"):
            print("✓ Excel默认文件名生成正确")
        else:
            print(f"✗ Excel默认文件名格式错误: {filename}")
            return False
    except Exception as e:
        print(f"✗ Excel默认文件名生成失败: {e}")
        return False
    
    try:
        filename = export_manager.get_default_filename(case_info, "csv")
        if "test_case_001" in filename and "npg_f1_ssg" in filename and filename.endswith(".csv"):
            print("✓ CSV默认文件名生成正确")
        else:
            print(f"✗ CSV默认文件名格式错误: {filename}")
            return False
    except Exception as e:
        print(f"✗ CSV默认文件名生成失败: {e}")
        return False
    
    # 测试导出摘要
    try:
        config = ExportConfig(
            format_type="excel",
            include_auto_confirmed=True,
            file_path="/tmp/test.xlsx"
        )
        summary = export_manager.get_export_summary(violations, config)
        
        expected_keys = ['total_violations', 'exported_violations', 'format_type', 
                        'include_auto_confirmed', 'status_counts', 'confirmed_count', 'pending_count']
        
        for key in expected_keys:
            if key not in summary:
                print(f"✗ 导出摘要缺少键: {key}")
                return False
        
        if summary['total_violations'] != 4:
            print(f"✗ 总违例数错误: {summary['total_violations']}")
            return False
        
        print("✓ 导出摘要生成正确")
    except Exception as e:
        print(f"✗ 导出摘要生成失败: {e}")
        return False
    
    # 测试路径验证
    try:
        # 测试有效路径
        temp_dir = tempfile.mkdtemp()
        test_path = os.path.join(temp_dir, "test.xlsx")
        
        if export_manager.validate_export_path(test_path):
            print("✓ 有效路径验证通过")
        else:
            print("✗ 有效路径验证失败")
            return False
        
        # 清理
        shutil.rmtree(temp_dir)
        
        # 测试无效路径（使用系统保留路径或权限受限路径）
        import platform
        if platform.system() == "Windows":
            # Windows系统保留路径
            invalid_path = "C:\\Windows\\System32\\test.xlsx"
        else:
            # Unix系统根目录（通常权限受限）
            invalid_path = "/root/test.xlsx"
            
        if not export_manager.validate_export_path(invalid_path):
            print("✓ 无效路径验证正确拒绝")
        else:
            print("⚠ 无效路径验证通过（可能有写入权限）")
            # 不算作失败，因为权限情况可能不同
            
    except Exception as e:
        print(f"✗ 路径验证测试失败: {e}")
        return False
    
    return True


def test_violation_data_exportable():
    """测试违例数据可导出性"""
    print("\n测试违例数据可导出性...")
    
    if not MODULES_AVAILABLE:
        print("⚠ 必需模块不可用，跳过违例数据测试")
        return True
    
    violations = create_test_violations()
    
    for violation in violations:
        if not violation.is_exportable():
            print(f"✗ 违例 {violation.num} 应该可导出但返回False")
            return False
    
    print("✓ 所有违例数据都可导出")
    return True


def test_export_interface_methods():
    """测试导出界面方法（不需要GUI）"""
    print("\n测试导出界面方法...")
    
    if not PYQT_AVAILABLE:
        print("⚠ PyQt5不可用，跳过GUI测试")
        return True
    
    # 创建应用程序（测试需要）
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    try:
        # 创建对话框实例
        dialog = TimingViolationDialog()
        
        # 设置测试数据
        dialog.violations = create_test_violations()
        dialog.case_info = create_test_case_info()
        
        # 测试导出按钮状态更新
        try:
            dialog.update_export_button_status()
            print("✓ 导出按钮状态更新成功")
        except Exception as e:
            print(f"✗ 导出按钮状态更新失败: {e}")
            return False
        
        # 测试导出要求验证
        try:
            is_valid, error_msg = dialog.validate_export_requirements()
            if is_valid:
                print("✓ 导出要求验证通过")
            else:
                print(f"✗ 导出要求验证失败: {error_msg}")
                return False
        except Exception as e:
            print(f"✗ 导出要求验证异常: {e}")
            return False
        
        # 测试导出预览信息
        try:
            preview_info = dialog.get_export_preview_info()
            
            expected_keys = ['case_name', 'corner', 'total_violations', 'pending_violations',
                           'confirmed_violations', 'auto_confirmed_violations', 'exportable_violations',
                           'has_pending', 'completion_rate']
            
            for key in expected_keys:
                if key not in preview_info:
                    print(f"✗ 导出预览信息缺少键: {key}")
                    return False
            
            if preview_info['case_name'] != "test_case_001":
                print(f"✗ 用例名称错误: {preview_info['case_name']}")
                return False
            
            if preview_info['total_violations'] != 4:
                print(f"✗ 总违例数错误: {preview_info['total_violations']}")
                return False
            
            print("✓ 导出预览信息生成正确")
        except Exception as e:
            print(f"✗ 导出预览信息生成失败: {e}")
            return False
        
        # 测试确认统计
        try:
            stats = dialog.get_confirmation_statistics()
            
            if stats['total'] != 4:
                print(f"✗ 统计总数错误: {stats['total']}")
                return False
            
            if stats['pending'] != 1:  # 只有第一个违例是待确认
                print(f"✗ 待确认数量错误: {stats['pending']}")
                return False
            
            if stats['auto_confirmed'] != 1:  # 第三个违例是自动确认
                print(f"✗ 自动确认数量错误: {stats['auto_confirmed']}")
                return False
            
            print("✓ 确认统计信息正确")
        except Exception as e:
            print(f"✗ 确认统计信息失败: {e}")
            return False
        
        # 清理
        dialog.close()
        
    except Exception as e:
        print(f"✗ 导出界面方法测试失败: {e}")
        return False
    
    return True


def test_error_handling():
    """测试错误处理"""
    print("\n测试错误处理...")
    
    if not MODULES_AVAILABLE:
        print("⚠ 必需模块不可用，跳过错误处理测试")
        return True
    
    # 测试ExportError异常
    try:
        raise ExportError("测试导出错误")
    except ExportError as e:
        if "导出失败: 测试导出错误" in str(e):
            print("✓ ExportError异常处理正确")
        else:
            print(f"✗ ExportError异常信息错误: {e}")
            return False
    except Exception as e:
        print(f"✗ ExportError异常类型错误: {e}")
        return False
    
    # 测试导出管理器错误处理
    export_manager = ExportManager()
    violations = create_test_violations()
    
    # 测试无效配置
    try:
        invalid_config = ExportConfig(
            format_type="invalid_format",
            file_path="/tmp/test.txt"
        )
        print("✗ 无效配置应该失败但没有")
        return False
    except ValueError:
        print("✓ 无效配置正确拒绝")
    except Exception as e:
        print(f"✗ 无效配置错误类型: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("=" * 60)
    print("时序违例确认插件 - 导出界面和错误处理测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("导出配置验证", test_export_config_validation()))
    test_results.append(("导出管理器功能", test_export_manager_functionality()))
    test_results.append(("违例数据可导出性", test_violation_data_exportable()))
    test_results.append(("导出界面方法", test_export_interface_methods()))
    test_results.append(("错误处理", test_error_handling()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！导出界面和错误处理功能实现正确。")
        return True
    else:
        print(f"\n❌ {total - passed} 个测试失败，请检查实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)