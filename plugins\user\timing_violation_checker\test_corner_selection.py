#!/usr/bin/env python3
"""
测试corner选择逻辑

验证corner选择功能的正确性，包括验证、选项生成和UI组件功能。
"""

import sys
import os

# 添加插件目录到Python路径
plugin_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, plugin_dir)

from utils import CaseInfoExtractor
from models import VALID_CORNERS, is_valid_corner


def test_corner_validation():
    """测试corner验证功能"""
    print("=== 测试Corner验证功能 ===")
    
    # 测试有效corner
    valid_corners = ["npg_f1_ssg", "npg_f2_tt", "npg_f3_ff"]
    for corner in valid_corners:
        is_valid, error_msg = CaseInfoExtractor.validate_corner_selection(corner)
        print(f"Corner '{corner}': 有效={is_valid}, 错误信息='{error_msg}'")
        assert is_valid, f"有效corner验证失败: {corner}"
    
    # 测试无效corner
    invalid_corners = ["", "请选择Corner...", "invalid_corner", "npg_f4_ssg", None]
    for corner in invalid_corners:
        is_valid, error_msg = CaseInfoExtractor.validate_corner_selection(corner)
        print(f"Corner '{corner}': 有效={is_valid}, 错误信息='{error_msg}'")
        assert not is_valid, f"无效corner验证失败: {corner}"
    
    print("Corner验证功能测试通过!\n")


def test_corner_selection_list():
    """测试corner选择列表生成"""
    print("=== 测试Corner选择列表生成 ===")
    
    # 测试基础选项列表
    options = CaseInfoExtractor.get_corner_display_options()
    print(f"基础选项列表: {options}")
    assert len(options) == len(VALID_CORNERS), "基础选项列表长度不正确"
    assert options == VALID_CORNERS, "基础选项列表内容不正确"
    
    # 测试带提示的选择列表
    selection_list = CaseInfoExtractor.get_corner_selection_list()
    print(f"选择列表: {selection_list}")
    assert len(selection_list) == len(VALID_CORNERS) + 1, "选择列表长度不正确"
    assert selection_list[0] == "请选择Corner...", "选择列表第一项不是提示文本"
    assert selection_list[1:] == VALID_CORNERS, "选择列表corner部分不正确"
    
    print("Corner选择列表生成测试通过!\n")


def test_corner_index_operations():
    """测试corner索引操作"""
    print("=== 测试Corner索引操作 ===")
    
    # 测试获取corner在列表中的索引
    test_cases = [
        ("npg_f1_ssg", 1),  # 第一个有效corner
        ("npg_f2_tt", 5),   # 中间的corner
        ("npg_f3_ff", 9),   # 最后一个corner
        ("invalid", 0),     # 无效corner
        ("", 0),            # 空字符串
        (None, 0)           # None值
    ]
    
    for corner, expected_index in test_cases:
        actual_index = CaseInfoExtractor.get_corner_index_in_list(corner)
        print(f"Corner '{corner}' -> 索引: {actual_index} (期望: {expected_index})")
        assert actual_index == expected_index, f"索引获取失败: {corner}"
    
    # 测试从索引获取corner
    for i in range(len(VALID_CORNERS) + 2):  # 测试超出范围的索引
        corner = CaseInfoExtractor.get_corner_from_selection_index(i)
        print(f"索引 {i} -> Corner: {corner}")
        
        if i == 0 or i > len(VALID_CORNERS):
            assert corner is None, f"无效索引应返回None: {i}"
        else:
            expected_corner = VALID_CORNERS[i - 1]
            assert corner == expected_corner, f"索引转换失败: {i}"
    
    print("Corner索引操作测试通过!\n")


def test_corner_validation_edge_cases():
    """测试corner验证边界情况"""
    print("=== 测试Corner验证边界情况 ===")
    
    # 测试大小写敏感性
    mixed_case_corners = ["NPG_F1_SSG", "npg_F1_ssg", "Npg_f1_Ssg"]
    for corner in mixed_case_corners:
        is_valid, error_msg = CaseInfoExtractor.validate_corner_selection(corner)
        print(f"大小写测试 '{corner}': 有效={is_valid}")
        # 当前实现是大小写敏感的，所以这些应该无效
        assert not is_valid, f"大小写敏感性测试失败: {corner}"
    
    # 测试空白字符处理
    whitespace_corners = [" npg_f1_ssg ", "\tnpg_f1_ssg\n", "  npg_f1_ssg  "]
    for corner in whitespace_corners:
        is_valid, error_msg = CaseInfoExtractor.validate_corner_selection(corner)
        print(f"空白字符测试 '{repr(corner)}': 有效={is_valid}")
        # 应该能正确处理空白字符
        assert is_valid, f"空白字符处理失败: {repr(corner)}"
    
    print("Corner验证边界情况测试通过!\n")


def test_corner_integration_with_case_info():
    """测试corner选择与用例信息的集成"""
    print("=== 测试Corner选择与用例信息集成 ===")
    
    # 测试创建带corner的用例信息
    test_path = "test_case/log/vio_summary.log"
    test_corner = "npg_f1_ssg"
    
    try:
        case_info = CaseInfoExtractor.create_case_info_with_corner(test_path, test_corner)
        print(f"创建用例信息成功: {case_info}")
        assert case_info.corner == test_corner, "Corner设置失败"
        assert case_info.case_name == "test_case", "用例名称提取失败"
    except Exception as e:
        print(f"创建用例信息失败: {e}")
    
    # 测试无效corner的处理
    invalid_corner = "invalid_corner"
    try:
        case_info = CaseInfoExtractor.create_case_info_with_corner(test_path, invalid_corner)
        assert False, "应该抛出异常"
    except ValueError as e:
        print(f"无效corner正确抛出异常: {e}")
    
    print("Corner选择与用例信息集成测试通过!\n")


def test_ui_component_availability():
    """测试UI组件可用性"""
    print("=== 测试UI组件可用性 ===")
    
    try:
        from utils import PYQT5_AVAILABLE, CornerSelectionWidget
        print(f"PyQt5可用性: {PYQT5_AVAILABLE}")
        
        if PYQT5_AVAILABLE:
            print("CornerSelectionWidget类已定义")
            # 这里不创建实际的widget实例，因为需要QApplication
            print("UI组件定义正确")
        else:
            print("PyQt5不可用，跳过UI组件测试")
    
    except ImportError as e:
        print(f"UI组件导入失败: {e}")
    
    print("UI组件可用性测试完成!\n")


def main():
    """运行所有测试"""
    print("开始测试Corner选择逻辑...\n")
    
    try:
        test_corner_validation()
        test_corner_selection_list()
        test_corner_index_operations()
        test_corner_validation_edge_cases()
        test_corner_integration_with_case_info()
        test_ui_component_availability()
        
        print("=== 所有测试通过! ===")
        print(f"支持的Corner列表: {VALID_CORNERS}")
        print(f"总共支持 {len(VALID_CORNERS)} 个Corner选项")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)