# 时序违例确认插件

## 概述

时序违例确认插件是一个集成到runsim GUI系统中的工具，用于解析和管理后仿真时序违例日志文件。该插件提供直观的用户界面，支持自动化处理和手动确认流程，最终生成可导出的确认报告。

## 文件结构

```
timing_violation_checker/
├── __init__.py                    # 包初始化文件
├── models.py                      # 数据模型和枚举定义
├── exceptions.py                  # 异常类定义
├── timing_violation_dialog.py     # 主对话框界面
└── README.md                      # 说明文档
```

## 核心组件

### 数据模型 (models.py)

- **ViolationData**: 时序违例数据模型，存储违例信息和确认状态
- **ViolationStatus**: 违例状态枚举（待确认、已确认-无问题、已确认-有问题、自动确认）
- **ConfirmationResult**: 确认结果枚举（无问题、有问题）
- **CaseInfo**: 用例信息数据模型，存储用例名称和corner信息
- **ExportConfig**: 导出配置数据模型
- **VALID_CORNERS**: 合法corner选项列表

### 异常处理 (exceptions.py)

- **TimingViolationError**: 基础异常类
- **FileParseError**: 文件解析错误
- **TimeConversionError**: 时间转换错误
- **ExportError**: 导出错误
- **ValidationError**: 数据验证错误
- **CaseInfoExtractionError**: 用例信息提取错误
- **PluginInitializationError**: 插件初始化错误
- **ConfigurationError**: 配置错误

### 主对话框 (timing_violation_dialog.py)

- **TimingViolationDialog**: 主对话框类，继承自NonModalDialog
- 提供时序违例确认的完整用户界面
- 集成文件选择、违例列表显示、确认操作和导出功能

## 使用方法

1. 通过插件菜单启动"时序违例确认工具"
2. 选择vio_summary.log文件
3. 设置复位时间（可选）
4. 逐一确认时序违例
5. 导出确认结果

## 开发状态

当前已完成：
- ✅ 基础项目结构
- ✅ 数据模型定义
- ✅ 异常类定义
- ✅ 主对话框框架

待实现功能：
- ⏳ 时间转换工具
- ⏳ 日志文件解析器
- ⏳ 用例信息提取器
- ⏳ 违例列表显示组件
- ⏳ 确认对话框
- ⏳ 导出功能
- ⏳ 完整的用户界面

## 技术要求

- Python 3.6+
- PyQt5
- 集成到现有runsim GUI系统
- 遵循现有插件架构模式