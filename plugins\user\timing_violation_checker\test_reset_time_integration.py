"""
测试复位时间功能集成

验证复位时间组件与对话框的集成是否正常工作。
"""

import sys
import os

# 添加插件路径到系统路径
plugin_dir = os.path.dirname(os.path.abspath(__file__))
if plugin_dir not in sys.path:
    sys.path.insert(0, plugin_dir)

from models import ViolationData, ViolationStatus
from utils import TimeConverter


class MockViolationListWidget:
    """模拟违例列表组件"""
    
    def __init__(self):
        self.violations = []
        self.refresh_called = False
    
    def load_violations(self, violations):
        self.violations = violations or []
    
    def refresh_display(self):
        self.refresh_called = True
        print("违例列表显示已刷新")


class MockResetTimeWidget:
    """模拟复位时间组件"""
    
    def __init__(self):
        self.reset_time_fs = 0
        self.violations = []
    
    def has_reset_time(self):
        return self.reset_time_fs > 0
    
    def get_reset_time_fs(self):
        return self.reset_time_fs
    
    def set_violations(self, violations):
        self.violations = violations or []
    
    def update_violation_status(self, violations):
        self.violations = violations or []
        print(f"复位时间组件违例状态已更新，违例数量: {len(self.violations)}")


class MockDialog:
    """模拟对话框类，测试复位时间事件处理"""
    
    def __init__(self):
        self.violations = []
        self.reset_time_fs = 0
        self.violation_list_widget = MockViolationListWidget()
        self.reset_time_widget = MockResetTimeWidget()
    
    def apply_auto_confirmation_logic(self, reset_time_fs: int) -> int:
        """应用自动确认逻辑"""
        if not self.violations or reset_time_fs <= 0:
            return 0
        
        auto_confirmed_count = 0
        
        for violation in self.violations:
            # 检查是否为复位期间违例且未手动确认
            if (not violation.is_confirmed() and 
                TimeConverter.is_reset_period_violation(violation.time_fs, reset_time_fs)):
                
                # 设置自动确认状态
                violation.status = ViolationStatus.AUTO_CONFIRMED
                violation.auto_confirmed = True
                violation.confirmer = "系统自动"
                violation.reason = "复位期间时序违例，可以忽略"
                
                auto_confirmed_count += 1
        
        return auto_confirmed_count
    
    def clear_auto_confirmation_logic(self) -> int:
        """清除自动确认逻辑"""
        if not self.violations:
            return 0
        
        cleared_count = 0
        
        for violation in self.violations:
            # 只清除自动确认的违例，保留手动确认的违例
            if violation.is_auto_confirmed():
                violation.status = ViolationStatus.PENDING
                violation.auto_confirmed = False
                violation.confirmer = ""
                violation.reason = ""
                violation.confirmation_result = None
                
                cleared_count += 1
        
        return cleared_count
    
    def recalculate_auto_confirmation(self, new_reset_time_fs: int):
        """重新计算自动确认状态"""
        if not self.violations:
            return
        
        # 首先清除所有自动确认状态
        for violation in self.violations:
            if violation.is_auto_confirmed():
                violation.status = ViolationStatus.PENDING
                violation.auto_confirmed = False
                violation.confirmer = ""
                violation.reason = ""
                violation.confirmation_result = None
        
        # 如果新的复位时间有效，重新应用自动确认
        if new_reset_time_fs > 0:
            self.apply_auto_confirmation_logic(new_reset_time_fs)
        
        # 更新复位时间组件的违例数据
        self.reset_time_widget.update_violation_status(self.violations)
    
    def on_reset_time_applied(self, reset_time_fs: int):
        """复位时间应用事件处理"""
        self.reset_time_fs = reset_time_fs
        
        # 如果有违例数据，应用自动确认逻辑
        if self.violations:
            auto_confirmed_count = self.apply_auto_confirmation_logic(reset_time_fs)
            
            # 更新违例列表显示
            self.violation_list_widget.refresh_display()
            
            # 显示结果
            formatted_time = TimeConverter.format_time(reset_time_fs)
            print(f"复位时间已设置为: {formatted_time}")
            print(f"自动确认了 {auto_confirmed_count} 个复位期间的时序违例")
        
        print(f"复位时间应用成功: {reset_time_fs} FS")
    
    def on_reset_time_cleared(self):
        """复位时间清除事件处理"""
        self.reset_time_fs = 0
        
        # 如果有违例数据，清除自动确认状态
        if self.violations:
            cleared_count = self.clear_auto_confirmation_logic()
            
            # 更新违例列表显示
            self.violation_list_widget.refresh_display()
            
            print(f"复位时间已清除，恢复了 {cleared_count} 个违例的待确认状态")
        
        print("复位时间清除成功")
    
    def load_violations_to_list(self, violations: list):
        """加载违例数据到列表"""
        # 更新内部违例数据
        self.violations = violations or []
        
        # 同步到违例列表组件
        self.violation_list_widget.load_violations(violations)
        
        # 同步到复位时间组件
        self.reset_time_widget.set_violations(violations)
        
        # 如果已设置复位时间，重新应用自动确认逻辑
        if self.reset_time_widget.has_reset_time():
            self.recalculate_auto_confirmation(self.reset_time_widget.get_reset_time_fs())


def test_dialog_reset_time_integration():
    """测试对话框复位时间集成"""
    print("测试对话框复位时间集成...")
    
    # 创建模拟对话框
    dialog = MockDialog()
    
    # 创建测试违例数据
    violations = [
        ViolationData(
            num=1,
            hier="tb_top.cpu.core1",
            time_original="500FS",
            time_fs=500,
            check="setup(posedge clk, data)"
        ),
        ViolationData(
            num=2,
            hier="tb_top.cpu.core2",
            time_original="1500FS",
            time_fs=1500,
            check="hold(posedge clk, data)"
        ),
        ViolationData(
            num=3,
            hier="tb_top.memory.ctrl",
            time_original="2500FS",
            time_fs=2500,
            check="setup(posedge clk, addr)"
        )
    ]
    
    # 加载违例数据
    print("\n1. 加载违例数据...")
    dialog.load_violations_to_list(violations)
    
    print("初始违例状态:")
    for v in violations:
        print(f"  NUM {v.num}: {v.time_original} - {v.status.value}")
    
    # 应用复位时间
    print("\n2. 应用复位时间 1000FS...")
    dialog.on_reset_time_applied(1000)
    
    print("应用复位时间后的违例状态:")
    for v in violations:
        status_text = v.status.value
        if v.is_auto_confirmed():
            status_text += f" (自动确认)"
        print(f"  NUM {v.num}: {v.time_original} - {status_text}")
    
    # 验证结果
    assert violations[0].is_auto_confirmed(), "NUM 1 应该被自动确认"
    assert not violations[1].is_auto_confirmed(), "NUM 2 不应该被自动确认"
    assert not violations[2].is_auto_confirmed(), "NUM 3 不应该被自动确认"
    assert dialog.violation_list_widget.refresh_called, "违例列表应该被刷新"
    
    # 更改复位时间
    print("\n3. 更改复位时间为 2000FS...")
    dialog.reset_time_widget.reset_time_fs = 2000  # 模拟复位时间组件状态
    dialog.on_reset_time_applied(2000)
    
    print("更改复位时间后的违例状态:")
    for v in violations:
        status_text = v.status.value
        if v.is_auto_confirmed():
            status_text += f" (自动确认)"
        print(f"  NUM {v.num}: {v.time_original} - {status_text}")
    
    # 验证结果
    assert violations[0].is_auto_confirmed(), "NUM 1 应该被自动确认"
    assert violations[1].is_auto_confirmed(), "NUM 2 现在也应该被自动确认"
    assert not violations[2].is_auto_confirmed(), "NUM 3 仍然不应该被自动确认"
    
    # 清除复位时间
    print("\n4. 清除复位时间...")
    dialog.on_reset_time_cleared()
    
    print("清除复位时间后的违例状态:")
    for v in violations:
        print(f"  NUM {v.num}: {v.time_original} - {v.status.value}")
    
    # 验证结果
    assert not violations[0].is_auto_confirmed(), "NUM 1 不应该再被自动确认"
    assert not violations[1].is_auto_confirmed(), "NUM 2 不应该再被自动确认"
    assert not violations[2].is_auto_confirmed(), "NUM 3 不应该再被自动确认"
    
    print("\n✓ 对话框复位时间集成测试通过")


def test_violation_loading_with_reset_time():
    """测试在已设置复位时间的情况下加载违例数据"""
    print("\n测试在已设置复位时间的情况下加载违例数据...")
    
    # 创建模拟对话框
    dialog = MockDialog()
    
    # 先设置复位时间
    print("1. 先设置复位时间为 1500FS...")
    dialog.reset_time_widget.reset_time_fs = 1500
    dialog.on_reset_time_applied(1500)
    
    # 创建测试违例数据
    violations = [
        ViolationData(
            num=1,
            hier="tb_top.cpu.core1",
            time_original="800FS",
            time_fs=800,
            check="setup(posedge clk, data)"
        ),
        ViolationData(
            num=2,
            hier="tb_top.cpu.core2",
            time_original="2000FS",
            time_fs=2000,
            check="hold(posedge clk, data)"
        )
    ]
    
    # 加载违例数据（应该自动应用复位时间逻辑）
    print("\n2. 加载违例数据...")
    dialog.load_violations_to_list(violations)
    
    print("加载后的违例状态:")
    for v in violations:
        status_text = v.status.value
        if v.is_auto_confirmed():
            status_text += f" (自动确认)"
        print(f"  NUM {v.num}: {v.time_original} - {status_text}")
    
    # 验证结果
    assert violations[0].is_auto_confirmed(), "NUM 1 应该被自动确认 (800FS <= 1500FS)"
    assert not violations[1].is_auto_confirmed(), "NUM 2 不应该被自动确认 (2000FS > 1500FS)"
    
    print("\n✓ 违例数据加载时自动确认测试通过")


if __name__ == "__main__":
    try:
        test_dialog_reset_time_integration()
        test_violation_loading_with_reset_time()
        print("\n🎉 所有集成测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()