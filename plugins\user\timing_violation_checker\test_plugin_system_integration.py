#!/usr/bin/env python3
"""
测试时序违例确认插件与系统的集成

验证插件能够正确与现有插件管理系统集成，包括配置管理、菜单集成等。
"""

import sys
import os
import json
import tempfile

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_plugin_configuration():
    """测试插件配置功能"""
    try:
        from plugins.user.timing_violation_checker_plugin import TimingViolationCheckerPlugin
        
        # 创建插件实例
        plugin = TimingViolationCheckerPlugin()
        
        # 测试配置设置和获取
        plugin.set_config('test_key', 'test_value')
        assert plugin.get_config('test_key') == 'test_value'
        
        # 测试默认值
        assert plugin.get_config('non_existent_key', 'default') == 'default'
        
        # 测试配置持久化
        plugin._save_plugin_config()
        
        # 创建新实例验证配置持久化
        plugin2 = TimingViolationCheckerPlugin()
        assert plugin2.get_config('test_key') == 'test_value'
        
        print("✓ 插件配置功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 插件配置功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_plugin_properties():
    """测试插件属性和兼容性"""
    try:
        from plugins.user.timing_violation_checker_plugin import TimingViolationCheckerPlugin
        
        plugin = TimingViolationCheckerPlugin()
        
        # 测试基本属性
        assert plugin.name == "时序违例确认工具"
        assert plugin.version == "1.0.0"
        assert isinstance(plugin.description, str)
        assert len(plugin.description) > 0
        
        # 测试插件管理系统兼容性
        assert plugin.admin_controlled == False
        assert plugin.default_enabled == True
        assert plugin.enabled == True  # 默认应该启用
        
        print("✓ 插件属性和兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 插件属性和兼容性测试失败: {e}")
        return False

def test_menu_integration():
    """测试菜单集成功能"""
    try:
        from plugins.user.timing_violation_checker_plugin import TimingViolationCheckerPlugin
        
        # 模拟主窗口和菜单
        class MockMenu:
            def __init__(self):
                self.actions = []
                
            def addAction(self, action):
                self.actions.append(action)
                
            def removeAction(self, action):
                if action in self.actions:
                    self.actions.remove(action)
        
        class MockMainWindow:
            def __init__(self):
                self.tools_menu = MockMenu()
        
        plugin = TimingViolationCheckerPlugin()
        main_window = MockMainWindow()
        
        # 测试初始化
        plugin.initialize(main_window)
        
        # 验证菜单项已添加
        assert len(main_window.tools_menu.actions) == 1
        assert plugin.menu_action is not None
        assert plugin.main_window == main_window
        
        # 测试清理
        plugin.cleanup()
        
        # 验证菜单项已移除
        assert len(main_window.tools_menu.actions) == 0
        assert plugin.main_window is None
        assert plugin.menu_action is None
        
        print("✓ 菜单集成功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 菜单集成功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_plugin_manager_compatibility():
    """测试与插件管理器的兼容性"""
    try:
        from plugins.user.timing_violation_checker_plugin import plugin_instance
        from plugins.base import PluginBase
        
        # 验证插件实例存在且正确
        assert plugin_instance is not None
        assert isinstance(plugin_instance, PluginBase)
        assert plugin_instance.name == "时序违例确认工具"
        
        # 验证插件实例是单例
        from plugins.user.timing_violation_checker_plugin import plugin_instance as plugin_instance2
        assert plugin_instance is plugin_instance2
        
        print("✓ 插件管理器兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 插件管理器兼容性测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    try:
        # 清理测试配置文件
        config_file = "timing_violation_checker_config.json"
        if os.path.exists(config_file):
            # 读取原始配置
            with open(config_file, 'r', encoding='utf-8') as f:
                original_config = json.load(f)
            
            # 移除测试键
            if 'test_key' in original_config:
                del original_config['test_key']
                
            # 写回配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(original_config, f, indent=4, ensure_ascii=False)
                
        print("✓ 测试文件清理完成")
        
    except Exception as e:
        print(f"⚠ 测试文件清理失败: {e}")

def main():
    """主测试函数"""
    print("开始测试时序违例确认插件系统集成...")
    print("=" * 60)
    
    tests = [
        ("插件属性和兼容性", test_plugin_properties),
        ("插件配置功能", test_plugin_configuration),
        ("菜单集成功能", test_menu_integration),
        ("插件管理器兼容性", test_plugin_manager_compatibility),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{len(tests) - passed - failed}. 测试{test_name}...")
        if test_func():
            passed += 1
        else:
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"测试完成: {passed} 通过, {failed} 失败")
    
    # 清理测试文件
    cleanup_test_files()
    
    if failed == 0:
        print("✓ 所有系统集成测试通过！插件已成功集成到现有系统")
        return True
    else:
        print("✗ 部分系统集成测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)