"""
用例信息集成测试

测试文件选择组件中用例信息显示和corner选择的集成功能。
"""

import sys
import os
import tempfile
import unittest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtTest import QTest
    from PyQt5.QtCore import Qt
    PYQT5_AVAILABLE = True
except ImportError:
    PYQT5_AVAILABLE = False

if PYQT5_AVAILABLE:
    from plugins.user.timing_violation_checker.file_selection_widget import FileSelectionWidget
    from plugins.user.timing_violation_checker.models import CaseInfo
    from plugins.user.timing_violation_checker.utils import CaseInfoExtractor


@unittest.skipUnless(PYQT5_AVAILABLE, "PyQt5 not available")
class TestCaseInfoIntegration(unittest.TestCase):
    """用例信息集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.widget = FileSelectionWidget()
        
        # 创建临时测试目录
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """清理测试"""
        # 清理临时文件
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        
        # 清理组件
        if hasattr(self, 'widget'):
            self.widget.close()
            self.widget.deleteLater()
    
    def create_test_file(self, case_dir_name: str) -> str:
        """创建测试文件
        
        Args:
            case_dir_name: 用例目录名称
            
        Returns:
            str: 测试文件路径
        """
        case_dir = os.path.join(self.temp_dir, case_dir_name)
        log_dir = os.path.join(case_dir, "log")
        file_path = os.path.join(log_dir, "vio_summary.log")
        
        os.makedirs(log_dir, exist_ok=True)
        with open(file_path, 'w') as f:
            f.write("test violation summary content")
        
        return file_path
    
    def test_case_name_display_with_corner(self):
        """测试包含corner的用例名称显示"""
        # 创建包含corner的测试文件
        test_file = self.create_test_file("test_case_npg_f1_ssg")
        
        # 设置文件路径
        self.widget.set_file_path(test_file)
        
        # 验证用例名称显示
        self.assertEqual(self.widget.case_name_display.text(), "test_case")
        
        # 验证corner显示（应该显示为标签，不是下拉框）
        self.assertIsNone(self.widget.corner_selection_widget)
        
        # 验证用例信息
        case_info = self.widget.get_current_case_info()
        self.assertIsNotNone(case_info)
        self.assertEqual(case_info.case_name, "test_case")
        self.assertEqual(case_info.corner, "npg_f1_ssg")
        self.assertTrue(case_info.has_corner())
    
    def test_case_name_display_without_corner(self):
        """测试不包含corner的用例名称显示"""
        # 创建不包含corner的测试文件
        test_file = self.create_test_file("test_case_only")
        
        # 设置文件路径
        self.widget.set_file_path(test_file)
        
        # 验证用例名称显示
        self.assertEqual(self.widget.case_name_display.text(), "test_case_only")
        
        # 验证corner选择组件显示
        self.assertIsNotNone(self.widget.corner_selection_widget)
        
        # 验证用例信息
        case_info = self.widget.get_current_case_info()
        self.assertIsNotNone(case_info)
        self.assertEqual(case_info.case_name, "test_case_only")
        self.assertEqual(case_info.corner, "")
        self.assertFalse(case_info.has_corner())
    
    def test_corner_selection_validation(self):
        """测试corner选择验证"""
        # 创建不包含corner的测试文件
        test_file = self.create_test_file("validation_test")
        
        # 设置文件路径
        self.widget.set_file_path(test_file)
        
        # 验证初始状态
        self.assertFalse(self.widget.is_case_info_complete())
        
        # 验证corner选择组件存在
        self.assertIsNotNone(self.widget.corner_selection_widget)
        
        # 测试有效corner选择
        valid_corner = "npg_f1_ssg"
        self.widget.on_corner_selected(valid_corner)
        
        # 验证选择后的状态
        self.assertTrue(self.widget.is_case_info_complete())
        case_info = self.widget.get_current_case_info()
        self.assertEqual(case_info.corner, valid_corner)
    
    def test_corner_selection_widget_functionality(self):
        """测试corner选择组件功能"""
        # 创建不包含corner的测试文件
        test_file = self.create_test_file("widget_test")
        
        # 设置文件路径
        self.widget.set_file_path(test_file)
        
        # 获取corner选择组件
        corner_widget = self.widget.corner_selection_widget
        self.assertIsNotNone(corner_widget)
        
        # 验证初始状态
        self.assertFalse(corner_widget.is_selection_valid())
        self.assertIsNone(corner_widget.get_selected_corner())
        
        # 设置有效corner
        test_corner = "npg_f2_tt"
        corner_widget.set_corner(test_corner)
        
        # 验证设置结果
        self.assertTrue(corner_widget.is_selection_valid())
        self.assertEqual(corner_widget.get_selected_corner(), test_corner)
    
    def test_case_info_update_flow(self):
        """测试用例信息更新流程"""
        # 信号接收器
        case_info_updates = []
        corner_selections = []
        
        def on_case_info_extracted(case_info):
            case_info_updates.append(case_info)
        
        def on_corner_selection_required(case_name):
            corner_selections.append(case_name)
        
        # 连接信号
        self.widget.case_info_extracted.connect(on_case_info_extracted)
        self.widget.corner_selection_required.connect(on_corner_selection_required)
        
        # 测试包含corner的文件
        test_file_with_corner = self.create_test_file("complete_case_npg_f3_ffg")
        self.widget.set_file_path(test_file_with_corner)
        
        # 验证信号发送
        self.assertEqual(len(case_info_updates), 1)
        self.assertEqual(len(corner_selections), 0)  # 不需要corner选择
        
        # 验证用例信息
        case_info = case_info_updates[0]
        self.assertEqual(case_info.case_name, "complete_case")
        self.assertEqual(case_info.corner, "npg_f3_ffg")
        
        # 清除信号记录
        case_info_updates.clear()
        corner_selections.clear()
        
        # 测试不包含corner的文件
        test_file_without_corner = self.create_test_file("incomplete_case")
        self.widget.set_file_path(test_file_without_corner)
        
        # 验证信号发送
        self.assertEqual(len(corner_selections), 1)
        self.assertEqual(corner_selections[0], "incomplete_case")
        
        # 模拟corner选择
        self.widget.on_corner_selected("npg_f1_tt")
        
        # 验证完整信息信号
        self.assertEqual(len(case_info_updates), 1)
        final_case_info = case_info_updates[0]
        self.assertEqual(final_case_info.case_name, "incomplete_case")
        self.assertEqual(final_case_info.corner, "npg_f1_tt")
    
    def test_validation_status_integration(self):
        """测试验证状态集成"""
        # 测试初始状态
        status = self.widget.get_validation_status()
        self.assertFalse(status['file_selected'])
        self.assertFalse(status['case_info_complete'])
        self.assertFalse(status['needs_corner_selection'])
        
        # 设置包含corner的文件
        test_file_complete = self.create_test_file("status_test_npg_f2_ssg")
        self.widget.set_file_path(test_file_complete)
        
        status = self.widget.get_validation_status()
        self.assertTrue(status['file_selected'])
        self.assertTrue(status['case_info_complete'])
        self.assertFalse(status['needs_corner_selection'])
        
        # 设置不包含corner的文件
        test_file_incomplete = self.create_test_file("status_test_incomplete")
        self.widget.set_file_path(test_file_incomplete)
        
        status = self.widget.get_validation_status()
        self.assertTrue(status['file_selected'])
        self.assertFalse(status['case_info_complete'])
        self.assertTrue(status['needs_corner_selection'])
        
        # 完成corner选择
        self.widget.on_corner_selected("npg_f1_ssg")
        
        status = self.widget.get_validation_status()
        self.assertTrue(status['file_selected'])
        self.assertTrue(status['case_info_complete'])
        self.assertFalse(status['needs_corner_selection'])
    
    def test_ui_state_consistency(self):
        """测试UI状态一致性"""
        # 设置不包含corner的文件
        test_file = self.create_test_file("ui_test")
        self.widget.set_file_path(test_file)
        
        # 验证UI状态
        self.assertEqual(self.widget.case_name_display.text(), "ui_test")
        self.assertIsNotNone(self.widget.corner_selection_widget)
        
        # 验证样式（用例名称应该有成功样式）
        case_name_style = self.widget.case_name_display.styleSheet()
        self.assertIn("#28a745", case_name_style)  # 成功颜色
        
        # 验证corner选择组件样式
        corner_style = self.widget.corner_selection_widget.styleSheet()
        self.assertIn("#ffc107", corner_style)  # 警告颜色
        
        # 选择corner后验证状态变化
        self.widget.on_corner_selected("npg_f1_ssg")
        
        # 验证用例信息完整性
        self.assertTrue(self.widget.is_case_info_complete())
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        # 测试无效文件路径
        invalid_path = "/invalid/path/to/vio_summary.log"
        self.widget.set_file_path(invalid_path)
        
        # 验证错误状态
        self.assertIsNone(self.widget.get_current_case_info())
        self.assertFalse(self.widget.is_case_info_complete())
        
        # 验证UI显示错误状态
        status_text = self.widget.status_label.text()
        self.assertIn("✗", status_text)
        
        # 验证用例信息显示重置
        self.assertEqual(self.widget.case_name_display.text(), "未选择文件")
    
    def test_multiple_file_switching(self):
        """测试多个文件切换"""
        # 创建多个测试文件
        file1 = self.create_test_file("case1_npg_f1_ssg")
        file2 = self.create_test_file("case2")
        file3 = self.create_test_file("case3_npg_f2_tt")
        
        # 测试文件1（包含corner）
        self.widget.set_file_path(file1)
        self.assertEqual(self.widget.get_current_case_info().case_name, "case1")
        self.assertEqual(self.widget.get_current_case_info().corner, "npg_f1_ssg")
        self.assertTrue(self.widget.is_case_info_complete())
        
        # 切换到文件2（不包含corner）
        self.widget.set_file_path(file2)
        self.assertEqual(self.widget.get_current_case_info().case_name, "case2")
        self.assertEqual(self.widget.get_current_case_info().corner, "")
        self.assertFalse(self.widget.is_case_info_complete())
        self.assertIsNotNone(self.widget.corner_selection_widget)
        
        # 为文件2选择corner
        self.widget.on_corner_selected("npg_f3_ffg")
        self.assertTrue(self.widget.is_case_info_complete())
        
        # 切换到文件3（包含corner）
        self.widget.set_file_path(file3)
        self.assertEqual(self.widget.get_current_case_info().case_name, "case3")
        self.assertEqual(self.widget.get_current_case_info().corner, "npg_f2_tt")
        self.assertTrue(self.widget.is_case_info_complete())
        self.assertIsNone(self.widget.corner_selection_widget)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)