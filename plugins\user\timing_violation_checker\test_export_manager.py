"""
测试ExportManager类的功能
"""

import os
import tempfile
import pytest
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from export_manager import ExportManager
from models import ViolationData, ExportConfig, ViolationStatus, ConfirmationResult
from exceptions import ExportError


def create_test_violations():
    """创建测试用的违例数据"""
    violations = []
    
    # 待确认违例
    v1 = ViolationData(
        num=1,
        hier="tb_top.cpu.core",
        time_original="1523423FS",
        time_fs=1523423,
        check="setup(posedge clk, data)"
    )
    violations.append(v1)
    
    # 已确认无问题违例
    v2 = ViolationData(
        num=2,
        hier="tb_top.mem.ctrl",
        time_original="2000000FS",
        time_fs=2000000,
        check="hold(posedge clk, addr)"
    )
    v2.set_manual_confirmation("张三", ConfirmationResult.NO_ISSUE, "经检查无问题")
    violations.append(v2)
    
    # 已确认有问题违例
    v3 = ViolationData(
        num=3,
        hier="tb_top.bus.arbiter",
        time_original="500000FS",
        time_fs=500000,
        check="setup(posedge clk, req)"
    )
    v3.set_manual_confirmation("李四", ConfirmationResult.HAS_ISSUE, "需要修改时序约束")
    violations.append(v3)
    
    # 自动确认违例
    v4 = ViolationData(
        num=4,
        hier="tb_top.reset.gen",
        time_original="100000FS",
        time_fs=100000,
        check="hold(posedge clk, reset_n)"
    )
    v4.set_auto_confirmation()
    violations.append(v4)
    
    return violations


def test_export_manager_initialization():
    """测试ExportManager初始化"""
    manager = ExportManager()
    
    assert manager.column_headers is not None
    assert len(manager.column_headers) == 10
    assert "序号" in manager.column_headers
    assert "层级路径" in manager.column_headers


def test_filter_violations():
    """测试违例数据过滤"""
    manager = ExportManager()
    violations = create_test_violations()
    
    # 包含自动确认的配置
    config_with_auto = ExportConfig(format_type="csv", include_auto_confirmed=True)
    filtered_with_auto = manager._filter_violations(violations, config_with_auto)
    assert len(filtered_with_auto) == 4
    
    # 不包含自动确认的配置
    config_without_auto = ExportConfig(format_type="csv", include_auto_confirmed=False)
    filtered_without_auto = manager._filter_violations(violations, config_without_auto)
    assert len(filtered_without_auto) == 3


def test_csv_export():
    """测试CSV导出功能"""
    manager = ExportManager()
    violations = create_test_violations()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        file_path = os.path.join(temp_dir, "test_export.csv")
        config = ExportConfig(format_type="csv", file_path=file_path)
        
        case_info = {
            'case_name': 'test_case',
            'corner': 'npg_f1_ssg'
        }
        
        result_path = manager.export_violations(violations, config, case_info)
        
        assert result_path == file_path
        assert os.path.exists(file_path)
        
        # 检查文件内容
        with open(file_path, 'r', encoding='utf-8-sig') as f:
            content = f.read()
            assert "时序违例确认报告" in content
            assert "test_case" in content
            assert "npg_f1_ssg" in content
            assert "tb_top.cpu.core" in content


def test_excel_export():
    """测试Excel导出功能"""
    try:
        import openpyxl
    except ImportError:
        pytest.skip("openpyxl not available")
    
    manager = ExportManager()
    violations = create_test_violations()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        file_path = os.path.join(temp_dir, "test_export.xlsx")
        config = ExportConfig(format_type="excel", file_path=file_path)
        
        case_info = {
            'case_name': 'test_case',
            'corner': 'npg_f1_tt'
        }
        
        result_path = manager.export_violations(violations, config, case_info)
        
        assert result_path == file_path
        assert os.path.exists(file_path)
        
        # 检查Excel文件内容
        wb = openpyxl.load_workbook(file_path)
        ws = wb.active
        
        # 检查标题
        assert "时序违例确认报告" in str(ws.cell(row=1, column=1).value)
        
        # 检查用例信息
        found_case_name = False
        found_corner = False
        for row in range(1, 10):
            cell_value = str(ws.cell(row=row, column=1).value)
            if "test_case" in cell_value:
                found_case_name = True
            if "npg_f1_tt" in cell_value:
                found_corner = True
        
        assert found_case_name
        assert found_corner


def test_default_filename_generation():
    """测试默认文件名生成"""
    manager = ExportManager()
    
    # 无用例信息
    filename = manager.get_default_filename()
    assert "时序违例确认_" in filename
    assert filename.endswith(".xlsx")
    
    # 有用例信息但无corner
    case_info = {'case_name': 'test_case'}
    filename = manager.get_default_filename(case_info, "csv")
    assert "test_case" in filename
    assert filename.endswith(".csv")
    
    # 有完整用例信息
    case_info = {'case_name': 'test_case', 'corner': 'npg_f1_ssg'}
    filename = manager.get_default_filename(case_info, "excel")
    assert "test_case" in filename
    assert "npg_f1_ssg" in filename
    assert filename.endswith(".xlsx")


def test_export_summary():
    """测试导出摘要信息"""
    manager = ExportManager()
    violations = create_test_violations()
    
    config = ExportConfig(format_type="excel", include_auto_confirmed=True)
    summary = manager.get_export_summary(violations, config)
    
    assert summary['total_violations'] == 4
    assert summary['exported_violations'] == 4
    assert summary['format_type'] == "excel"
    assert summary['include_auto_confirmed'] == True
    assert summary['confirmed_count'] == 3  # 2 manual + 1 auto
    assert summary['pending_count'] == 1
    
    # 测试不包含自动确认的情况
    config_no_auto = ExportConfig(format_type="csv", include_auto_confirmed=False)
    summary_no_auto = manager.get_export_summary(violations, config_no_auto)
    
    assert summary_no_auto['exported_violations'] == 3
    assert summary_no_auto['confirmed_count'] == 2


def test_validate_export_path():
    """测试导出路径验证"""
    manager = ExportManager()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 有效路径
        valid_path = os.path.join(temp_dir, "test.xlsx")
        assert manager.validate_export_path(valid_path) == True
        
        # 无效路径（不存在的驱动器）
        if os.name == 'nt':  # Windows
            invalid_path = "Z:\\nonexistent\\path\\test.xlsx"
            assert manager.validate_export_path(invalid_path) == False


def test_export_error_handling():
    """测试导出错误处理"""
    manager = ExportManager()
    violations = create_test_violations()
    
    # 无效的导出格式 - 这会在ExportConfig初始化时就抛出ValueError
    try:
        config = ExportConfig(format_type="invalid_format", file_path="test.txt")
        manager.export_violations(violations, config)
        assert False, "应该抛出异常"
    except ValueError as e:
        assert "不支持的导出格式" in str(e)
    except ExportError as e:
        assert "不支持的导出格式" in str(e)


if __name__ == "__main__":
    # 运行简单测试
    print("测试ExportManager...")
    
    try:
        test_export_manager_initialization()
        print("✓ 初始化测试通过")
        
        test_filter_violations()
        print("✓ 数据过滤测试通过")
        
        test_csv_export()
        print("✓ CSV导出测试通过")
        
        try:
            test_excel_export()
            print("✓ Excel导出测试通过")
        except Exception as e:
            print(f"⚠ Excel导出测试跳过: {e}")
        
        test_default_filename_generation()
        print("✓ 文件名生成测试通过")
        
        test_export_summary()
        print("✓ 导出摘要测试通过")
        
        test_validate_export_path()
        print("✓ 路径验证测试通过")
        
        test_export_error_handling()
        print("✓ 错误处理测试通过")
        
        print("\n所有测试通过！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()