#!/usr/bin/env python3
"""
Corner选择组件演示

演示CornerSelectionWidget的使用方法和功能。
"""

import sys
import os

# 添加插件目录到Python路径
plugin_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, plugin_dir)

try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                                 QHBoxLayout, QWidget, QLabel, QPushButton, 
                                 QTextEdit, QGroupBox)
    from PyQt5.QtCore import Qt
    
    from utils import CornerSelectionWidget, CaseInfoExtractor
    
    class CornerSelectionDemo(QMainWindow):
        """Corner选择组件演示窗口"""
        
        def __init__(self):
            super().__init__()
            self.setWindowTitle("Corner选择组件演示")
            self.setGeometry(100, 100, 600, 400)
            
            # 创建中央组件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # 创建主布局
            main_layout = QVBoxLayout(central_widget)
            
            # 创建corner选择组
            corner_group = QGroupBox("Corner选择")
            corner_layout = QHBoxLayout(corner_group)
            
            corner_layout.addWidget(QLabel("选择Corner:"))
            
            # 创建corner选择组件
            self.corner_widget = CornerSelectionWidget()
            corner_layout.addWidget(self.corner_widget)
            
            # 添加按钮
            self.clear_button = QPushButton("清除选择")
            self.clear_button.clicked.connect(self.corner_widget.clear_selection)
            corner_layout.addWidget(self.clear_button)
            
            corner_layout.addStretch()
            main_layout.addWidget(corner_group)
            
            # 创建信息显示组
            info_group = QGroupBox("选择信息")
            info_layout = QVBoxLayout(info_group)
            
            self.info_text = QTextEdit()
            self.info_text.setMaximumHeight(200)
            self.info_text.setReadOnly(True)
            info_layout.addWidget(self.info_text)
            
            main_layout.addWidget(info_group)
            
            # 创建测试按钮组
            button_group = QGroupBox("测试功能")
            button_layout = QHBoxLayout(button_group)
            
            self.test_validation_button = QPushButton("测试验证")
            self.test_validation_button.clicked.connect(self.test_validation)
            button_layout.addWidget(self.test_validation_button)
            
            self.show_all_corners_button = QPushButton("显示所有Corner")
            self.show_all_corners_button.clicked.connect(self.show_all_corners)
            button_layout.addWidget(self.show_all_corners_button)
            
            self.set_random_corner_button = QPushButton("设置随机Corner")
            self.set_random_corner_button.clicked.connect(self.set_random_corner)
            button_layout.addWidget(self.set_random_corner_button)
            
            button_layout.addStretch()
            main_layout.addWidget(button_group)
            
            # 连接信号
            self.corner_widget.corner_selected.connect(self.on_corner_selected)
            self.corner_widget.selection_cleared.connect(self.on_selection_cleared)
            
            # 初始化显示
            self.update_info_display()
        
        def on_corner_selected(self, corner: str):
            """处理corner选择事件"""
            self.update_info_display()
            self.info_text.append(f"\n[事件] Corner选择: {corner}")
        
        def on_selection_cleared(self):
            """处理选择清除事件"""
            self.update_info_display()
            self.info_text.append(f"\n[事件] 选择已清除")
        
        def update_info_display(self):
            """更新信息显示"""
            info = self.corner_widget.get_selection_info()
            
            info_text = f"""当前选择信息:
文本: {info['text']}
Corner: {info['corner']}
有效: {info['is_valid']}
错误信息: {info['error_message']}
索引: {info['index']}
"""
            self.info_text.setPlainText(info_text)
        
        def test_validation(self):
            """测试验证功能"""
            current_corner = self.corner_widget.get_selected_corner()
            is_valid = self.corner_widget.is_selection_valid()
            validation_msg = self.corner_widget.get_validation_message()
            
            self.info_text.append(f"""
[验证测试]
当前Corner: {current_corner}
是否有效: {is_valid}
验证消息: {validation_msg}
""")
        
        def show_all_corners(self):
            """显示所有可用的corner"""
            all_corners = self.corner_widget.get_all_valid_corners()
            self.info_text.append(f"""
[所有可用Corner]
{', '.join(all_corners)}
总数: {len(all_corners)}
""")
        
        def set_random_corner(self):
            """设置随机corner"""
            import random
            all_corners = self.corner_widget.get_all_valid_corners()
            random_corner = random.choice(all_corners)
            
            self.corner_widget.set_corner(random_corner)
            self.info_text.append(f"\n[随机设置] 设置Corner为: {random_corner}")
    
    def main():
        """主函数"""
        app = QApplication(sys.argv)
        
        # 创建演示窗口
        demo = CornerSelectionDemo()
        demo.show()
        
        # 运行应用
        sys.exit(app.exec_())
    
    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"PyQt5不可用，无法运行演示: {e}")
    print("请确保已安装PyQt5: pip install PyQt5")
    
    # 提供命令行演示
    print("\n=== 命令行演示Corner选择功能 ===")
    
    from utils import CaseInfoExtractor
    
    print("1. 获取corner选择列表:")
    selection_list = CaseInfoExtractor.get_corner_selection_list()
    for i, option in enumerate(selection_list):
        print(f"   {i}: {option}")
    
    print("\n2. 验证corner选择:")
    test_corners = ["npg_f1_ssg", "invalid_corner", "请选择Corner...", ""]
    for corner in test_corners:
        is_valid, error_msg = CaseInfoExtractor.validate_corner_selection(corner)
        print(f"   '{corner}': 有效={is_valid}, 错误='{error_msg}'")
    
    print("\n3. 索引操作:")
    for corner in ["npg_f1_ssg", "npg_f2_tt", "npg_f3_ff"]:
        index = CaseInfoExtractor.get_corner_index_in_list(corner)
        back_corner = CaseInfoExtractor.get_corner_from_selection_index(index)
        print(f"   {corner} -> 索引{index} -> {back_corner}")
    
    print("\n演示完成!")