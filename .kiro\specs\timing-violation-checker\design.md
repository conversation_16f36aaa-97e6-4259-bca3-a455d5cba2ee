# Design Document

## Overview

时序违例确认插件是一个集成到runsim GUI系统中的插件，用于解析和管理后仿真时序违例日志文件。该插件提供直观的用户界面，支持自动化处理和手动确认流程，最终生成可导出的确认报告。

插件采用模块化设计，遵循现有的插件架构模式，使用PyQt5构建用户界面，并集成到现有的插件管理系统中。

## Architecture

### 系统架构图

```mermaid
graph TB
    A[TimingViolationCheckerPlugin] --> B[TimingViolationDialog]
    B --> C[FileSelectionWidget]
    B --> D[ViolationListWidget]
    B --> E[ConfirmationDialog]
    B --> F[ExportManager]
    
    G[ViolationParser] --> H[ViolationData]
    I[TimeConverter] --> H
    J[CaseInfoExtractor] --> H
    
    A --> G
    A --> I
    A --> J
    
    K[ConfigManager] --> A
    L[ReportGenerator] --> F
```

### 核心组件关系

1. **TimingViolationCheckerPlugin**: 主插件类，继承自PluginBase
2. **TimingViolationDialog**: 主对话框，管理整个用户界面
3. **ViolationParser**: 负责解析vio_summary.log文件
4. **TimeConverter**: 处理时间单位转换
5. **CaseInfoExtractor**: 提取用例名称和corner信息
6. **ConfirmationDialog**: 处理单个违例的确认流程
7. **ExportManager**: 管理导出功能
8. **ReportGenerator**: 生成导出报告

## Components and Interfaces

### 1. TimingViolationCheckerPlugin

主插件类，负责插件的生命周期管理和界面初始化。

```python
class TimingViolationCheckerPlugin(PluginBase):
    @property
    def name(self) -> str
    
    @property
    def version(self) -> str
    
    @property
    def description(self) -> str
    
    def initialize(self, main_window)
    
    def cleanup(self)
    
    def show_dialog(self)
```

### 2. ViolationData

时序违例数据模型，存储解析后的违例信息。

```python
@dataclass
class ViolationData:
    num: int                    # 违例序号
    hier: str                   # 层级路径
    time_original: str          # 原始时间字符串
    time_fs: int               # 转换为飞秒的时间
    check: str                 # 检查信息
    status: ViolationStatus    # 确认状态
    confirmer: str             # 确认人
    reason: str                # 确认理由/解决方案
    auto_confirmed: bool       # 是否自动确认
```

### 3. ViolationParser

解析vio_summary.log文件的核心组件。

```python
class ViolationParser:
    def parse_file(self, file_path: str) -> List[ViolationData]
    
    def _parse_violation_block(self, block: str) -> ViolationData
    
    def _extract_field(self, line: str) -> Tuple[str, str]
```

### 4. TimeConverter

时间单位转换工具。

```python
class TimeConverter:
    @staticmethod
    def to_femtoseconds(time_str: str) -> int
    
    @staticmethod
    def format_time(fs_time: int) -> str
    
    @staticmethod
    def parse_time_unit(time_str: str) -> Tuple[float, str]
```

### 5. TimingViolationDialog

主对话框界面，包含所有用户交互组件。

```python
class TimingViolationDialog(NonModalDialog):
    def __init__(self, parent=None)
    
    def setup_ui(self)
    
    def load_file(self, file_path: str)
    
    def set_reset_time(self, reset_time: int)
    
    def confirm_violation(self, violation: ViolationData)
    
    def export_results(self, format_type: str)
```

### 6. ViolationListWidget

违例列表显示组件。

```python
class ViolationListWidget(QTableWidget):
    violation_selected = pyqtSignal(ViolationData)
    confirm_requested = pyqtSignal(ViolationData)
    
    def __init__(self, parent=None)
    
    def load_violations(self, violations: List[ViolationData])
    
    def update_violation_status(self, violation: ViolationData)
    
    def get_selected_violation(self) -> ViolationData
```

## Data Models

### ViolationStatus枚举

```python
class ViolationStatus(Enum):
    PENDING = "待确认"
    CONFIRMED_OK = "已确认-无问题"
    CONFIRMED_ISSUE = "已确认-有问题"
    AUTO_CONFIRMED = "自动确认"
```

### CaseInfo数据类

```python
@dataclass
class CaseInfo:
    case_name: str
    corner: str
    directory_path: str
```

### ExportConfig数据类

```python
@dataclass
class ExportConfig:
    format_type: str  # "excel" or "csv"
    include_auto_confirmed: bool
    file_path: str
```

## Error Handling

### 错误类型定义

```python
class TimingViolationError(Exception):
    """时序违例插件基础异常"""
    pass

class FileParseError(TimingViolationError):
    """文件解析错误"""
    pass

class TimeConversionError(TimingViolationError):
    """时间转换错误"""
    pass

class ExportError(TimingViolationError):
    """导出错误"""
    pass
```

### 错误处理策略

1. **文件解析错误**: 显示详细错误信息，允许用户重新选择文件
2. **时间转换错误**: 记录警告，使用原始时间字符串
3. **导出错误**: 显示错误对话框，提供重试选项
4. **网络/IO错误**: 提供用户友好的错误提示和解决建议

## Testing Strategy

### 单元测试

1. **ViolationParser测试**
   - 测试正常格式的日志文件解析
   - 测试异常格式的容错处理
   - 测试空文件和大文件处理

2. **TimeConverter测试**
   - 测试各种时间单位转换
   - 测试边界值和异常输入
   - 测试时间格式化输出

3. **CaseInfoExtractor测试**
   - 测试标准目录格式解析
   - 测试缺少corner的情况
   - 测试异常路径格式

### 集成测试

1. **插件加载测试**
   - 测试插件在主窗口中的正确加载
   - 测试菜单项的创建和响应
   - 测试插件清理功能

2. **用户界面测试**
   - 测试对话框的显示和交互
   - 测试文件选择和加载流程
   - 测试确认流程的完整性

3. **导出功能测试**
   - 测试Excel和CSV导出
   - 测试大数据量导出性能
   - 测试导出文件的正确性

### 用户验收测试

1. **完整工作流程测试**
   - 从文件选择到最终导出的完整流程
   - 不同corner选择的正确处理
   - 复位时间设置的自动确认功能

2. **用户体验测试**
   - 界面响应性和易用性
   - 错误提示的清晰度
   - 大量数据的处理性能

## UI Design Specification

### 主对话框布局

```
┌─────────────────────────────────────────────────────────────┐
│ 时序违例确认工具                                              │
├─────────────────────────────────────────────────────────────┤
│ 文件信息                                                     │
│ ┌─────────────────┐ ┌──────────────┐ ┌─────────────────────┐ │
│ │ 选择文件...      │ │ 用例名称     │ │ Corner: [下拉框]    │ │
│ └─────────────────┘ └──────────────┘ └─────────────────────┘ │
│                                                             │
│ 复位时间设置                                                 │
│ ┌─────────────────────────────────┐ ┌─────────────────────┐ │
│ │ 复位时间 (fs): [输入框]          │ │ [应用]              │ │
│ └─────────────────────────────────┘ └─────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 时序违例列表                                                 │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ NUM │ Hier        │ Time      │ Check     │ 状态 │ 操作 │ │
│ ├─────┼─────────────┼───────────┼───────────┼──────┼──────┤ │
│ │  1  │ tb_top.xxx  │ 1523423FS │ setup...  │ 待确认│ 确认 │ │
│ │  2  │ tb_top.yyy  │ 1000000FS │ hold...   │ 已确认│ 查看 │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │ 导出Excel   │ │ 导出CSV     │ │ 全部确认    │ │ 关闭    │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 确认对话框布局

```
┌─────────────────────────────────────────────────────────────┐
│ 时序违例确认                                                 │
├─────────────────────────────────────────────────────────────┤
│ 违例信息                                                     │
│ NUM: 1                                                      │
│ Hier: tb_top.xxx.xxx                                       │
│ Time: 1523423 FS                                           │
│ Check: setup(posedge xxx, xxx)                             │
├─────────────────────────────────────────────────────────────┤
│ 确认信息                                                     │
│ 确认人: ┌─────────────────────────────────────────────────┐ │
│        │ [输入框]                                        │ │
│        └─────────────────────────────────────────────────┘ │
│                                                             │
│ 确认结果: ○ 无问题  ○ 有问题                                │
│                                                             │
│ 理由/解决方案:                                               │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ [多行文本输入框]                                        │ │
│ │                                                         │ │
│ │                                                         │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    ┌─────────┐ ┌─────────┐                  │
│                    │ 确认    │ │ 取消    │                  │
│                    └─────────┘ └─────────┘                  │
└─────────────────────────────────────────────────────────────┘
```

### 颜色和样式规范

- **待确认状态**: 黄色背景 (#FFF3CD)
- **已确认-无问题**: 绿色背景 (#D4EDDA)
- **已确认-有问题**: 红色背景 (#F8D7DA)
- **自动确认**: 灰色背景 (#E2E3E5)
- **按钮样式**: 遵循现有GUI的蓝色主题 (#4a9eff)
- **字体**: Microsoft YaHei, 9pt

### 响应式设计

- 对话框最小尺寸: 1000x700
- 表格列宽自适应内容长度
- 支持窗口大小调整
- 长文本内容支持滚动显示