#!/usr/bin/env python3
"""
测试确认对话框功能

测试ConfirmationDialog类的基本功能，包括界面显示、输入验证和确认逻辑。
"""

import sys
import os

# 添加项目路径到sys.path
sys.path.append(os.path.join(os.getcwd(), 'plugins', 'user', 'timing_violation_checker'))

from PyQt5.QtWidgets import QApplication, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt

from models import ViolationData, ViolationStatus, ConfirmationResult
from confirmation_dialog import ConfirmationDialog, show_confirmation_dialog


def create_test_violation() -> ViolationData:
    """创建测试用的违例数据"""
    return ViolationData(
        num=1,
        hier="tb_top.cpu.core.alu",
        time_original="1523423FS",
        time_fs=1523423,
        check="setup(posedge clk, data)",
        status=ViolationStatus.PENDING
    )


class TestWindow(QWidget):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("确认对话框测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建测试违例数据
        self.test_violation = create_test_violation()
        
        # 设置界面
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout()
        
        # 测试按钮
        test_button = QPushButton("测试确认对话框")
        test_button.clicked.connect(self.test_confirmation_dialog)
        layout.addWidget(test_button)
        
        # 便捷函数测试按钮
        convenience_button = QPushButton("测试便捷函数")
        convenience_button.clicked.connect(self.test_convenience_function)
        layout.addWidget(convenience_button)
        
        # 预填充测试按钮
        prefill_button = QPushButton("测试预填充数据")
        prefill_button.clicked.connect(self.test_prefill_data)
        layout.addWidget(prefill_button)
        
        # 验证测试按钮
        validation_button = QPushButton("测试输入验证")
        validation_button.clicked.connect(self.test_input_validation)
        layout.addWidget(validation_button)
        
        self.setLayout(layout)
    
    def test_confirmation_dialog(self):
        """测试确认对话框基本功能"""
        print("测试确认对话框...")
        
        # 创建对话框
        dialog = ConfirmationDialog(self.test_violation, self)
        
        # 连接信号
        dialog.violation_confirmed.connect(self.on_violation_confirmed)
        dialog.confirmation_cancelled.connect(self.on_confirmation_cancelled)
        
        # 显示对话框
        dialog.exec_()
    
    def test_convenience_function(self):
        """测试便捷函数"""
        print("测试便捷函数...")
        
        # 使用便捷函数显示对话框
        result = show_confirmation_dialog(self.test_violation, self)
        
        if result:
            QMessageBox.information(
                self,
                "确认结果",
                f"确认完成:\n"
                f"确认人: {result.confirmer}\n"
                f"状态: {result.get_status_display()}\n"
                f"理由: {result.reason[:50]}..."
            )
        else:
            QMessageBox.information(self, "取消", "用户取消了确认")
    
    def test_prefill_data(self):
        """测试预填充数据"""
        print("测试预填充数据...")
        
        # 创建对话框
        dialog = ConfirmationDialog(self.test_violation, self)
        
        # 预填充数据
        dialog.set_confirmation_data(
            confirmer="测试用户",
            result=ConfirmationResult.NO_ISSUE,
            reason="这是一个测试理由，用于验证预填充功能是否正常工作。"
        )
        
        # 连接信号
        dialog.violation_confirmed.connect(self.on_violation_confirmed)
        dialog.confirmation_cancelled.connect(self.on_confirmation_cancelled)
        
        # 显示对话框
        dialog.exec_()
    
    def test_input_validation(self):
        """测试输入验证"""
        print("测试输入验证...")
        
        # 创建对话框
        dialog = ConfirmationDialog(self.test_violation, self)
        
        # 测试验证逻辑
        print("初始验证状态:", dialog._validate_input())
        
        # 获取确认数据
        data = dialog.get_confirmation_data()
        print("确认数据:", data)
        
        # 显示对话框让用户测试验证
        dialog.exec_()
    
    def on_violation_confirmed(self, violation: ViolationData):
        """违例确认完成处理"""
        print(f"违例确认完成: NUM={violation.num}")
        print(f"确认人: {violation.confirmer}")
        print(f"确认结果: {violation.confirmation_result}")
        print(f"理由: {violation.reason}")
        print(f"状态: {violation.get_status_display()}")
        
        QMessageBox.information(
            self,
            "确认完成",
            f"违例 NUM {violation.num} 确认完成\n"
            f"确认人: {violation.confirmer}\n"
            f"状态: {violation.get_status_display()}"
        )
    
    def on_confirmation_cancelled(self):
        """确认取消处理"""
        print("确认被取消")
        QMessageBox.information(self, "取消", "确认被取消")


def test_confirmation_dialog_basic():
    """基本功能测试"""
    print("=== 基本功能测试 ===")
    
    # 创建测试违例
    violation = create_test_violation()
    print(f"创建测试违例: NUM={violation.num}, Status={violation.get_status_display()}")
    
    # 测试确认逻辑
    try:
        violation.set_manual_confirmation("测试用户", ConfirmationResult.NO_ISSUE, "测试理由")
        print(f"确认后状态: {violation.get_status_display()}")
        print(f"确认人: {violation.confirmer}")
        print(f"确认结果: {violation.confirmation_result}")
        print(f"理由: {violation.reason}")
        print("✓ 确认逻辑测试通过")
    except Exception as e:
        print(f"✗ 确认逻辑测试失败: {e}")
    
    print()


def test_validation_logic():
    """验证逻辑测试"""
    print("=== 验证逻辑测试 ===")
    
    violation = create_test_violation()
    
    # 测试空确认人
    try:
        violation.set_manual_confirmation("", ConfirmationResult.NO_ISSUE, "理由")
        print("✗ 空确认人验证失败")
    except ValueError:
        print("✓ 空确认人验证通过")
    
    # 测试空理由
    try:
        violation.set_manual_confirmation("用户", ConfirmationResult.NO_ISSUE, "")
        print("✗ 空理由验证失败")
    except ValueError:
        print("✓ 空理由验证通过")
    
    # 测试正常情况
    try:
        violation.set_manual_confirmation("用户", ConfirmationResult.NO_ISSUE, "正常理由")
        print("✓ 正常确认验证通过")
    except Exception as e:
        print(f"✗ 正常确认验证失败: {e}")
    
    print()


def main():
    """主函数"""
    print("确认对话框测试程序")
    print("=" * 50)
    
    # 运行基本测试
    test_confirmation_dialog_basic()
    test_validation_logic()
    
    # 创建GUI应用
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    print("GUI测试窗口已启动")
    print("请点击按钮测试不同功能")
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()