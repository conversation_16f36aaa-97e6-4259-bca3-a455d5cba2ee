"""
测试复位时间自动确认逻辑

验证复位时间设置后的自动确认功能是否正常工作。
"""

import sys
import os

# 添加插件路径到系统路径
plugin_dir = os.path.dirname(os.path.abspath(__file__))
if plugin_dir not in sys.path:
    sys.path.insert(0, plugin_dir)

from models import ViolationData, ViolationStatus, ConfirmationResult
from utils import TimeConverter


def test_auto_confirmation_logic():
    """测试自动确认逻辑"""
    print("测试复位时间自动确认逻辑...")
    
    # 创建测试违例数据
    violations = [
        ViolationData(
            num=1,
            hier="tb_top.cpu.core1",
            time_original="500FS",
            time_fs=500,
            check="setup(posedge clk, data)"
        ),
        ViolationData(
            num=2,
            hier="tb_top.cpu.core2", 
            time_original="1500FS",
            time_fs=1500,
            check="hold(posedge clk, data)"
        ),
        ViolationData(
            num=3,
            hier="tb_top.memory.ctrl",
            time_original="2500FS", 
            time_fs=2500,
            check="setup(posedge clk, addr)"
        )
    ]
    
    # 设置复位时间为 1000FS
    reset_time_fs = 1000
    
    print(f"复位时间设置为: {TimeConverter.format_time(reset_time_fs)}")
    print("违例列表:")
    for v in violations:
        print(f"  NUM {v.num}: {v.time_original} - {v.status.value}")
    
    # 应用自动确认逻辑
    auto_confirmed_count = 0
    for violation in violations:
        if (not violation.is_confirmed() and 
            TimeConverter.is_reset_period_violation(violation.time_fs, reset_time_fs)):
            violation.set_auto_confirmation("复位期间时序违例，可以忽略")
            auto_confirmed_count += 1
    
    print(f"\n自动确认了 {auto_confirmed_count} 个违例")
    print("更新后的违例状态:")
    for v in violations:
        status_text = v.status.value
        if v.is_auto_confirmed():
            status_text += f" (自动确认: {v.reason})"
        print(f"  NUM {v.num}: {v.time_original} - {status_text}")
    
    # 验证结果
    assert violations[0].is_auto_confirmed(), "NUM 1 应该被自动确认 (500FS <= 1000FS)"
    assert not violations[1].is_auto_confirmed(), "NUM 2 不应该被自动确认 (1500FS > 1000FS)"
    assert not violations[2].is_auto_confirmed(), "NUM 3 不应该被自动确认 (2500FS > 1000FS)"
    
    print("\n✓ 自动确认逻辑测试通过")


def test_reset_time_change():
    """测试复位时间变更时的重新计算"""
    print("\n测试复位时间变更时的重新计算...")
    
    # 创建测试违例数据
    violations = [
        ViolationData(
            num=1,
            hier="tb_top.cpu.core1",
            time_original="500FS",
            time_fs=500,
            check="setup(posedge clk, data)"
        ),
        ViolationData(
            num=2,
            hier="tb_top.cpu.core2",
            time_original="1500FS", 
            time_fs=1500,
            check="hold(posedge clk, data)"
        )
    ]
    
    # 第一次设置复位时间为 1000FS
    reset_time_fs = 1000
    print(f"第一次设置复位时间: {TimeConverter.format_time(reset_time_fs)}")
    
    # 应用自动确认
    for violation in violations:
        if (not violation.is_confirmed() and 
            TimeConverter.is_reset_period_violation(violation.time_fs, reset_time_fs)):
            violation.set_auto_confirmation("复位期间时序违例，可以忽略")
    
    print("第一次自动确认后:")
    for v in violations:
        print(f"  NUM {v.num}: {v.time_original} - {v.status.value}")
    
    # 验证第一次结果
    assert violations[0].is_auto_confirmed(), "NUM 1 应该被自动确认"
    assert not violations[1].is_auto_confirmed(), "NUM 2 不应该被自动确认"
    
    # 第二次设置复位时间为 2000FS
    new_reset_time_fs = 2000
    print(f"\n第二次设置复位时间: {TimeConverter.format_time(new_reset_time_fs)}")
    
    # 清除所有自动确认状态
    for violation in violations:
        if violation.is_auto_confirmed():
            violation.status = ViolationStatus.PENDING
            violation.auto_confirmed = False
            violation.confirmer = ""
            violation.reason = ""
            violation.confirmation_result = None
    
    # 重新应用自动确认
    for violation in violations:
        if (not violation.is_confirmed() and 
            TimeConverter.is_reset_period_violation(violation.time_fs, new_reset_time_fs)):
            violation.set_auto_confirmation("复位期间时序违例，可以忽略")
    
    print("第二次自动确认后:")
    for v in violations:
        print(f"  NUM {v.num}: {v.time_original} - {v.status.value}")
    
    # 验证第二次结果
    assert violations[0].is_auto_confirmed(), "NUM 1 应该被自动确认"
    assert violations[1].is_auto_confirmed(), "NUM 2 现在也应该被自动确认"
    
    print("\n✓ 复位时间变更重新计算测试通过")


def test_manual_confirmation_preservation():
    """测试手动确认的违例不会被自动确认覆盖"""
    print("\n测试手动确认的违例不会被自动确认覆盖...")
    
    # 创建测试违例数据
    violation = ViolationData(
        num=1,
        hier="tb_top.cpu.core1",
        time_original="500FS",
        time_fs=500,
        check="setup(posedge clk, data)"
    )
    
    # 先手动确认
    violation.set_manual_confirmation(
        confirmer="测试工程师",
        result=ConfirmationResult.HAS_ISSUE,
        reason="这是一个真正的问题"
    )
    
    print(f"手动确认后: {violation.status.value}")
    print(f"确认人: {violation.confirmer}")
    print(f"确认理由: {violation.reason}")
    
    # 设置复位时间，尝试自动确认
    reset_time_fs = 1000
    
    # 检查是否应该自动确认（不应该，因为已经手动确认）
    if (not violation.is_confirmed() and 
        TimeConverter.is_reset_period_violation(violation.time_fs, reset_time_fs)):
        violation.set_auto_confirmation("复位期间时序违例，可以忽略")
    
    print(f"尝试自动确认后: {violation.status.value}")
    print(f"确认人: {violation.confirmer}")
    print(f"确认理由: {violation.reason}")
    
    # 验证手动确认没有被覆盖
    assert violation.status == ViolationStatus.CONFIRMED_ISSUE, "手动确认状态不应该被改变"
    assert violation.confirmer == "测试工程师", "确认人不应该被改变"
    assert violation.reason == "这是一个真正的问题", "确认理由不应该被改变"
    assert not violation.is_auto_confirmed(), "不应该被标记为自动确认"
    
    print("\n✓ 手动确认保护测试通过")


if __name__ == "__main__":
    try:
        test_auto_confirmation_logic()
        test_reset_time_change()
        test_manual_confirmation_preservation()
        print("\n🎉 所有测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()