#!/usr/bin/env python3
"""
测试组件间交互逻辑

验证时序违例确认对话框中各组件的信号连接、数据传递和状态管理功能。
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加插件目录到路径
plugin_dir = os.path.dirname(os.path.abspath(__file__))
if plugin_dir not in sys.path:
    sys.path.insert(0, plugin_dir)

# 检查PyQt5是否可用
PYQT5_AVAILABLE = True
try:
    from PyQt5.QtWidgets import QApplication, QWidget
    from PyQt5.QtCore import QTimer, pyqtSignal
    from PyQt5.QtTest import QTest
except ImportError:
    PYQT5_AVAILABLE = False
    print("PyQt5 not available, skipping GUI tests")

if PYQT5_AVAILABLE:
    from timing_violation_dialog import TimingViolationDialog
    from models import CaseInfo, ViolationData, ViolationStatus
    from exceptions import TimingViolationError


class TestComponentInteractions(unittest.TestCase):
    """测试组件间交互逻辑"""
    
    def setUp(self):
        """测试前设置"""
        if not PYQT5_AVAILABLE:
            self.skipTest("PyQt5 not available")
        
        # 创建QApplication实例（如果不存在）
        if not QApplication.instance():
            self.app = QApplication([])
        else:
            self.app = QApplication.instance()
        
        # 创建对话框实例
        self.dialog = TimingViolationDialog()
        
        # 创建测试数据
        self.test_case_info = CaseInfo(
            case_name="test_case",
            corner="npg_f1_ssg",
            directory_path="/path/to/test_case_npg_f1_ssg"
        )
        
        self.test_violations = [
            ViolationData(
                num=1,
                hier="tb_top.cpu.core",
                time_original="1000PS",
                time_fs=1000000,
                check="setup(posedge clk, data)",
                status=ViolationStatus.PENDING,
                confirmer="",
                reason="",
                auto_confirmed=False
            ),
            ViolationData(
                num=2,
                hier="tb_top.mem.ctrl",
                time_original="500PS",
                time_fs=500000,
                check="hold(posedge clk, addr)",
                status=ViolationStatus.PENDING,
                confirmer="",
                reason="",
                auto_confirmed=False
            )
        ]
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'dialog'):
            self.dialog.close()
            self.dialog = None
    
    def test_signal_connections(self):
        """测试信号连接是否正确建立"""
        # 验证文件选择组件信号连接
        if hasattr(self.dialog, 'file_selection_widget'):
            self.assertTrue(hasattr(self.dialog.file_selection_widget, 'file_selected'))
            self.assertTrue(hasattr(self.dialog.file_selection_widget, 'case_info_extracted'))
            self.assertTrue(hasattr(self.dialog.file_selection_widget, 'validation_error'))
        
        # 验证复位时间组件信号连接
        if hasattr(self.dialog, 'reset_time_widget'):
            self.assertTrue(hasattr(self.dialog.reset_time_widget, 'reset_time_applied'))
            self.assertTrue(hasattr(self.dialog.reset_time_widget, 'reset_time_cleared'))
            self.assertTrue(hasattr(self.dialog.reset_time_widget, 'validation_error'))
        
        # 验证违例列表组件信号连接
        if hasattr(self.dialog, 'violation_list_widget'):
            self.assertTrue(hasattr(self.dialog.violation_list_widget, 'violation_selected'))
            self.assertTrue(hasattr(self.dialog.violation_list_widget, 'confirm_requested'))
            self.assertTrue(hasattr(self.dialog.violation_list_widget, 'status_changed'))
    
    def test_component_interactions_validation(self):
        """测试组件交互验证功能"""
        # 测试组件交互验证
        result = self.dialog.validate_component_interactions()
        self.assertIsInstance(result, bool)
    
    def test_data_flow_pipeline(self):
        """测试数据流管道功能"""
        # 测试数据变化触发
        self.dialog.trigger_data_change('violations')
        self.assertTrue(self.dialog.pending_data_changes['violations'])
        
        # 测试数据变化处理
        self.dialog.process_data_changes()
        self.assertFalse(self.dialog.pending_data_changes['violations'])
    
    def test_case_info_extraction_handling(self):
        """测试用例信息提取处理"""
        # 模拟用例信息提取成功
        self.dialog.on_case_info_extracted(self.test_case_info)
        
        # 验证数据是否正确设置
        self.assertEqual(self.dialog.case_info, self.test_case_info)
        
        # 验证窗口标题是否更新
        expected_title = f"时序违例确认工具 - {self.test_case_info.case_name} ({self.test_case_info.corner})"
        self.assertEqual(self.dialog.windowTitle(), expected_title)
    
    def test_reset_time_application(self):
        """测试复位时间应用处理"""
        # 设置测试违例数据
        self.dialog.violations = self.test_violations.copy()
        
        # 应用复位时间（750PS = 750000FS）
        reset_time_fs = 750000
        self.dialog.on_reset_time_applied(reset_time_fs)
        
        # 验证复位时间是否正确设置
        self.assertEqual(self.dialog.reset_time_fs, reset_time_fs)
        
        # 验证自动确认逻辑
        self.dialog.apply_reset_time_auto_confirmation(reset_time_fs)
        
        # 检查时间小于复位时间的违例是否被自动确认
        violation_500ps = next(v for v in self.dialog.violations if v.time_fs == 500000)
        self.assertEqual(violation_500ps.status, ViolationStatus.AUTO_CONFIRMED)
        self.assertTrue(violation_500ps.auto_confirmed)
        
        # 检查时间大于复位时间的违例是否保持待确认状态
        violation_1000ps = next(v for v in self.dialog.violations if v.time_fs == 1000000)
        self.assertEqual(violation_1000ps.status, ViolationStatus.PENDING)
        self.assertFalse(violation_1000ps.auto_confirmed)
    
    def test_reset_time_clearing(self):
        """测试复位时间清除处理"""
        # 先设置复位时间和自动确认
        self.dialog.violations = self.test_violations.copy()
        self.dialog.reset_time_fs = 750000
        self.dialog.apply_reset_time_auto_confirmation(750000)
        
        # 清除复位时间
        self.dialog.on_reset_time_cleared()
        
        # 验证复位时间是否清除
        self.assertEqual(self.dialog.reset_time_fs, 0)
        
        # 验证自动确认状态是否清除
        self.dialog.clear_auto_confirmations()
        for violation in self.dialog.violations:
            if violation.auto_confirmed:
                self.assertEqual(violation.status, ViolationStatus.PENDING)
                self.assertFalse(violation.auto_confirmed)
    
    def test_violation_status_change_handling(self):
        """测试违例状态变化处理"""
        # 设置测试违例数据
        self.dialog.violations = self.test_violations.copy()
        
        # 模拟违例状态变化
        updated_violation = self.test_violations[0].copy() if hasattr(self.test_violations[0], 'copy') else self.test_violations[0]
        updated_violation.status = ViolationStatus.CONFIRMED_OK
        updated_violation.confirmer = "test_user"
        updated_violation.reason = "测试确认"
        
        # 处理状态变化
        self.dialog.on_status_changed(updated_violation)
        
        # 验证违例数据是否更新
        updated_in_list = next(v for v in self.dialog.violations if v.num == updated_violation.num)
        self.assertEqual(updated_in_list.status, ViolationStatus.CONFIRMED_OK)
        self.assertEqual(updated_in_list.confirmer, "test_user")
        self.assertEqual(updated_in_list.reason, "测试确认")
    
    def test_interface_state_management(self):
        """测试界面状态管理"""
        # 测试初始状态
        status = self.dialog.get_component_status()
        self.assertIsInstance(status, dict)
        self.assertIn('file_selected', status)
        self.assertIn('violations_loaded', status)
        self.assertIn('reset_time_set', status)
        
        # 测试数据加载后的状态
        self.dialog.violations = self.test_violations
        self.dialog.case_info = self.test_case_info
        self.dialog.reset_time_fs = 1000000
        
        self.dialog.update_interface_state()
        
        # 验证导出按钮状态
        if hasattr(self.dialog, 'export_excel_button'):
            self.assertTrue(self.dialog.export_excel_button.isEnabled())
        
        if hasattr(self.dialog, 'export_csv_button'):
            self.assertTrue(self.dialog.export_csv_button.isEnabled())
    
    def test_component_state_synchronization(self):
        """测试组件状态同步"""
        # 设置测试数据
        self.dialog.violations = self.test_violations
        self.dialog.case_info = self.test_case_info
        self.dialog.reset_time_fs = 1000000
        
        # 触发状态同步
        self.dialog.sync_component_states()
        
        # 验证组件状态是否同步
        # 这里主要验证方法调用不会出错
        self.assertTrue(True)  # 如果没有异常，测试通过
    
    def test_data_consistency_validation(self):
        """测试数据一致性验证"""
        # 设置测试数据
        self.dialog.violations = self.test_violations
        
        # 验证数据一致性
        result = self.dialog.validate_data_consistency()
        self.assertIsInstance(result, bool)
    
    def test_error_handling_in_interactions(self):
        """测试交互过程中的错误处理"""
        # 测试验证错误处理
        error_msg = "测试验证错误"
        self.dialog.on_validation_error(error_msg)
        
        # 测试复位时间验证错误处理
        reset_error_msg = "复位时间格式错误"
        self.dialog.on_reset_time_validation_error(reset_error_msg)
        
        # 验证错误处理不会导致程序崩溃
        self.assertTrue(True)
    
    def test_component_cleanup(self):
        """测试组件清理功能"""
        # 设置一些数据
        self.dialog.violations = self.test_violations
        self.dialog.case_info = self.test_case_info
        
        # 执行清理
        self.dialog.cleanup_resources()
        
        # 验证清理是否正确执行
        # 主要验证方法调用不会出错
        self.assertTrue(True)
    
    @patch('plugins.user.timing_violation_checker.timing_violation_dialog.QMessageBox')
    def test_file_selection_error_handling(self, mock_msgbox):
        """测试文件选择错误处理"""
        # 模拟文件选择错误
        error_msg = "文件不存在"
        self.dialog.on_validation_error(error_msg)
        
        # 验证错误消息框是否被调用
        mock_msgbox.warning.assert_called()
    
    def test_component_enable_disable_logic(self):
        """测试组件启用/禁用逻辑"""
        # 测试文件选择后的组件启用
        self.dialog.enable_components_after_file_selection()
        
        # 验证相关组件是否被启用
        if hasattr(self.dialog, 'reset_time_widget'):
            self.assertTrue(self.dialog.reset_time_widget.isEnabled())
        
        if hasattr(self.dialog, 'violation_list_widget'):
            self.assertTrue(self.dialog.violation_list_widget.isEnabled())
        
        # 测试界面状态重置
        self.dialog.reset_interface_state()
        
        # 验证组件是否被正确重置
        if hasattr(self.dialog, 'reset_time_widget'):
            self.assertFalse(self.dialog.reset_time_widget.isEnabled())
        
        if hasattr(self.dialog, 'violation_list_widget'):
            self.assertFalse(self.dialog.violation_list_widget.isEnabled())


def run_tests():
    """运行测试"""
    if not PYQT5_AVAILABLE:
        print("PyQt5 not available, skipping tests")
        return
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestComponentInteractions)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 返回测试结果
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)