"""
ViolationParser测试文件

用于测试ViolationParser类的各种功能和错误处理。
"""

import os
import sys
import tempfile
from typing import List

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from violation_parser import ViolationParser
from models import ViolationData
from exceptions import FileParseError


def create_test_log_file(content: str) -> str:
    """创建测试用的日志文件
    
    Args:
        content: 文件内容
        
    Returns:
        str: 临时文件路径
    """
    # 创建临时目录结构
    temp_dir = tempfile.mkdtemp()
    case_dir = os.path.join(temp_dir, "test_case")
    log_dir = os.path.join(case_dir, "log")
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建vio_summary.log文件
    file_path = os.path.join(log_dir, "vio_summary.log")
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return file_path


def test_basic_parsing():
    """测试基本解析功能"""
    print("测试基本解析功能...")
    
    # 创建测试数据
    test_content = """
NUM: 1
Hier: tb_top.cpu.core
Time: 1000PS
Check: setup(posedge clk, data)
----
NUM: 2
Hier: tb_top.memory.ctrl
Time: 500NS
Check: hold(posedge clk, addr)
----
NUM: 3
Hier: tb_top.bus.arbiter
Time: 1500FS
Check: setup(negedge rst, enable)
"""
    
    file_path = create_test_log_file(test_content)
    
    try:
        parser = ViolationParser()
        violations = parser.parse_file(file_path)
        
        print(f"成功解析 {len(violations)} 个违例")
        
        # 验证解析结果
        assert len(violations) == 3, f"期望3个违例，实际得到{len(violations)}个"
        
        # 验证第一个违例
        v1 = violations[0]
        assert v1.num == 1, f"第一个违例NUM应为1，实际为{v1.num}"
        assert v1.hier == "tb_top.cpu.core", f"第一个违例Hier不匹配"
        assert v1.time_original == "1000PS", f"第一个违例Time不匹配"
        assert v1.time_fs == 1000000, f"第一个违例时间转换错误，期望1000000，实际{v1.time_fs}"
        
        # 验证第二个违例
        v2 = violations[1]
        assert v2.num == 2, f"第二个违例NUM应为2，实际为{v2.num}"
        assert v2.time_fs == 500000000, f"第二个违例时间转换错误，期望500000000，实际{v2.time_fs}"
        
        print("✓ 基本解析功能测试通过")
        
    except Exception as e:
        print(f"✗ 基本解析功能测试失败: {str(e)}")
        raise
    finally:
        # 清理临时文件
        if os.path.exists(file_path):
            os.remove(file_path)
            os.rmdir(os.path.dirname(file_path))
            os.rmdir(os.path.dirname(os.path.dirname(file_path)))


def test_error_handling():
    """测试错误处理功能"""
    print("测试错误处理功能...")
    
    # 测试文件不存在
    parser = ViolationParser()
    try:
        parser.parse_file("nonexistent_file.log")
        assert False, "应该抛出FileParseError异常"
    except FileParseError as e:
        print(f"✓ 文件不存在错误处理正确: {e.get_user_message()}")
    
    # 测试格式错误的违例块
    test_content_with_errors = """
NUM: 1
Hier: tb_top.cpu.core
Time: 1000PS
Check: setup(posedge clk, data)
----
NUM: invalid_number
Hier: tb_top.memory.ctrl
Time: 500NS
Check: hold(posedge clk, addr)
----
NUM: 3
Hier: 
Time: 1500FS
Check: setup(negedge rst, enable)
----
NUM: 4
Hier: tb_top.valid.module
Time: 2000PS
Check: valid check
"""
    
    file_path = create_test_log_file(test_content_with_errors)
    
    try:
        violations = parser.parse_file(file_path)
        
        # 应该成功解析部分违例
        print(f"成功解析 {len(violations)} 个违例（跳过了错误的违例块）")
        
        # 检查错误和警告
        errors = parser.get_parse_errors()
        warnings = parser.get_warnings()
        
        print(f"解析错误数量: {len(errors)}")
        print(f"警告数量: {len(warnings)}")
        
        assert len(violations) >= 1, "至少应该解析出1个有效违例"
        assert len(errors) >= 1, "应该有解析错误"
        
        print("✓ 错误处理功能测试通过")
        
    except Exception as e:
        print(f"✗ 错误处理功能测试失败: {str(e)}")
        raise
    finally:
        # 清理临时文件
        if os.path.exists(file_path):
            os.remove(file_path)
            os.rmdir(os.path.dirname(file_path))
            os.rmdir(os.path.dirname(os.path.dirname(file_path)))


def test_time_conversion_errors():
    """测试时间转换错误处理"""
    print("测试时间转换错误处理...")
    
    test_content = """
NUM: 1
Hier: tb_top.cpu.core
Time: invalid_time
Check: setup(posedge clk, data)
----
NUM: 2
Hier: tb_top.memory.ctrl
Time: 500XS
Check: hold(posedge clk, addr)
----
NUM: 3
Hier: tb_top.bus.arbiter
Time: 1500FS
Check: setup(negedge rst, enable)
"""
    
    file_path = create_test_log_file(test_content)
    
    try:
        parser = ViolationParser()
        violations = parser.parse_file(file_path)
        
        print(f"成功解析 {len(violations)} 个违例")
        
        # 检查警告信息
        warnings = parser.get_warnings()
        print(f"警告数量: {len(warnings)}")
        
        # 应该有时间转换相关的警告
        time_warnings = [w for w in warnings if "时间转换失败" in w]
        assert len(time_warnings) >= 1, "应该有时间转换失败的警告"
        
        print("✓ 时间转换错误处理测试通过")
        
    except Exception as e:
        print(f"✗ 时间转换错误处理测试失败: {str(e)}")
        raise
    finally:
        # 清理临时文件
        if os.path.exists(file_path):
            os.remove(file_path)
            os.rmdir(os.path.dirname(file_path))
            os.rmdir(os.path.dirname(os.path.dirname(file_path)))


def test_file_path_validation():
    """测试文件路径验证"""
    print("测试文件路径验证...")
    
    parser = ViolationParser()
    
    # 测试有效路径
    valid_paths = [
        "/path/to/case/log/vio_summary.log",
        "case_dir/log/vio_summary.log",
        "C:\\test\\case\\log\\vio_summary.log"
    ]
    
    for path in valid_paths:
        is_valid, error_msg = parser.validate_file_path(path)
        if not is_valid:
            print(f"路径验证失败: {path} - {error_msg}")
        else:
            print(f"✓ 有效路径: {path}")
    
    # 测试无效路径
    invalid_paths = [
        "",
        "invalid_file.txt",
        "/path/to/vio_summary.log",  # 缺少log目录
        "/path/to/log/wrong_file.log"  # 错误的文件名
    ]
    
    for path in invalid_paths:
        is_valid, error_msg = parser.validate_file_path(path)
        if is_valid:
            print(f"路径验证应该失败但通过了: {path}")
        else:
            print(f"✓ 无效路径正确识别: {path} - {error_msg}")
    
    print("✓ 文件路径验证测试通过")


def run_all_tests():
    """运行所有测试"""
    print("开始运行ViolationParser测试...")
    print("=" * 50)
    
    try:
        test_basic_parsing()
        print()
        
        test_error_handling()
        print()
        
        test_time_conversion_errors()
        print()
        
        test_file_path_validation()
        print()
        
        print("=" * 50)
        print("✓ 所有测试通过！")
        
    except Exception as e:
        print("=" * 50)
        print(f"✗ 测试失败: {str(e)}")
        raise


if __name__ == "__main__":
    run_all_tests()