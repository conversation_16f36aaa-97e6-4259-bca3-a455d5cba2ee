"""
时序违例确认插件错误处理工具

提供统一的错误处理、用户友好的错误提示对话框和错误日志记录功能。
"""

import logging
import traceback
import sys
from typing import Optional, Callable, Any, Dict
from functools import wraps
from datetime import datetime

try:
    from PyQt5.QtWidgets import (QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, 
                                QLabel, QPushButton, QTextEdit, QWidget, QApplication)
    from PyQt5.QtCore import Qt, QTimer
    from PyQt5.QtGui import QIcon, QPixmap
    PYQT5_AVAILABLE = True
except ImportError:
    PYQT5_AVAILABLE = False

try:
    # 尝试相对导入（在插件环境中）
    from .exceptions import (TimingViolationError, FileParseError, TimeConversionError,
                           ExportError, ValidationError, CaseInfoExtractionError,
                           PluginInitializationError, ConfigurationError)
except ImportError:
    # 如果相对导入失败，尝试直接导入（在测试环境中）
    from exceptions import (TimingViolationError, FileParseError, TimeConversionError,
                          ExportError, ValidationError, CaseInfoExtractionError,
                          PluginInitializationError, ConfigurationError)


class ErrorLogger:
    """错误日志记录器
    
    提供统一的错误日志记录功能，支持不同级别的日志记录。
    """
    
    def __init__(self, logger_name: str = "TimingViolationChecker"):
        """初始化错误日志记录器
        
        Args:
            logger_name: 日志记录器名称
        """
        self.logger = logging.getLogger(logger_name)
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        if not self.logger.handlers:
            # 设置日志级别
            self.logger.setLevel(logging.DEBUG)
            
            # 创建控制台处理器
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)
            
            # 创建格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            
            # 添加处理器
            self.logger.addHandler(console_handler)
    
    def log_error(self, error: Exception, context: str = "", extra_info: Dict = None):
        """记录错误信息
        
        Args:
            error: 异常对象
            context: 错误上下文信息
            extra_info: 额外信息字典
        """
        error_msg = f"错误: {str(error)}"
        if context:
            error_msg = f"{context} - {error_msg}"
        
        # 记录基本错误信息
        self.logger.error(error_msg)
        
        # 记录异常堆栈
        if hasattr(error, '__traceback__') and error.__traceback__:
            self.logger.debug("异常堆栈:", exc_info=error)
        
        # 记录额外信息
        if extra_info:
            for key, value in extra_info.items():
                self.logger.debug(f"  {key}: {value}")
    
    def log_warning(self, message: str, context: str = ""):
        """记录警告信息
        
        Args:
            message: 警告消息
            context: 上下文信息
        """
        warning_msg = f"{context} - {message}" if context else message
        self.logger.warning(warning_msg)
    
    def log_info(self, message: str, context: str = ""):
        """记录信息
        
        Args:
            message: 信息消息
            context: 上下文信息
        """
        info_msg = f"{context} - {message}" if context else message
        self.logger.info(info_msg)
    
    def log_debug(self, message: str, context: str = ""):
        """记录调试信息
        
        Args:
            message: 调试消息
            context: 上下文信息
        """
        debug_msg = f"{context} - {message}" if context else message
        self.logger.debug(debug_msg)


# 全局错误日志记录器实例
error_logger = ErrorLogger()


def log_exceptions(context: str = "", reraise: bool = True):
    """异常日志记录装饰器
    
    Args:
        context: 错误上下文信息
        reraise: 是否重新抛出异常
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 记录异常
                func_context = context or f"{func.__module__}.{func.__name__}"
                error_logger.log_error(e, func_context, {
                    'function': func.__name__,
                    'args': str(args)[:200],  # 限制长度避免日志过长
                    'kwargs': str(kwargs)[:200]
                })
                
                if reraise:
                    raise
                return None
        return wrapper
    return decorator


if PYQT5_AVAILABLE:
    class ErrorDialog(QDialog):
        """用户友好的错误提示对话框
        
        显示详细的错误信息，提供用户友好的界面。
        """
        
        def __init__(self, parent=None):
            """初始化错误对话框
            
            Args:
                parent: 父窗口
            """
            super().__init__(parent)
            self.setWindowTitle("错误提示")
            self.setModal(True)
            self.setMinimumSize(500, 300)
            self.setMaximumSize(800, 600)
            
            self._setup_ui()
        
        def _setup_ui(self):
            """设置用户界面"""
            layout = QVBoxLayout(self)
            
            # 错误图标和标题区域
            header_layout = QHBoxLayout()
            
            # 错误图标
            icon_label = QLabel()
            icon_label.setPixmap(self.style().standardPixmap(
                self.style().SP_MessageBoxCritical
            ).scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            icon_label.setAlignment(Qt.AlignTop)
            header_layout.addWidget(icon_label)
            
            # 错误标题
            self.title_label = QLabel()
            self.title_label.setWordWrap(True)
            self.title_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #d32f2f;")
            header_layout.addWidget(self.title_label, 1)
            
            layout.addLayout(header_layout)
            
            # 错误消息区域
            self.message_label = QLabel()
            self.message_label.setWordWrap(True)
            self.message_label.setStyleSheet("margin: 10px 0; font-size: 12px;")
            layout.addWidget(self.message_label)
            
            # 详细信息区域（可折叠）
            self.details_text = QTextEdit()
            self.details_text.setMaximumHeight(150)
            self.details_text.setReadOnly(True)
            self.details_text.setStyleSheet("""
                QTextEdit {
                    background-color: #f5f5f5;
                    border: 1px solid #ddd;
                    font-family: 'Courier New', monospace;
                    font-size: 10px;
                }
            """)
            self.details_text.hide()  # 默认隐藏
            layout.addWidget(self.details_text)
            
            # 按钮区域
            button_layout = QHBoxLayout()
            
            # 显示/隐藏详细信息按钮
            self.details_button = QPushButton("显示详细信息")
            self.details_button.clicked.connect(self._toggle_details)
            button_layout.addWidget(self.details_button)
            
            button_layout.addStretch()
            
            # 确定按钮
            ok_button = QPushButton("确定")
            ok_button.setDefault(True)
            ok_button.clicked.connect(self.accept)
            button_layout.addWidget(ok_button)
            
            layout.addLayout(button_layout)
        
        def _toggle_details(self):
            """切换详细信息显示状态"""
            if self.details_text.isVisible():
                self.details_text.hide()
                self.details_button.setText("显示详细信息")
                self.resize(self.width(), self.minimumHeight())
            else:
                self.details_text.show()
                self.details_button.setText("隐藏详细信息")
                self.resize(self.width(), self.height() + 150)
        
        def set_error(self, error: Exception, context: str = ""):
            """设置错误信息
            
            Args:
                error: 异常对象
                context: 错误上下文
            """
            # 设置标题
            if isinstance(error, TimingViolationError):
                self.title_label.setText(f"操作失败: {type(error).__name__}")
            else:
                self.title_label.setText("发生未知错误")
            
            # 设置用户友好的消息
            if hasattr(error, 'get_user_message'):
                user_message = error.get_user_message()
            else:
                user_message = str(error)
            
            if context:
                user_message = f"{context}\n\n{user_message}"
            
            self.message_label.setText(user_message)
            
            # 设置详细信息
            details = []
            details.append(f"错误类型: {type(error).__name__}")
            details.append(f"错误消息: {str(error)}")
            
            if hasattr(error, 'details') and error.details:
                details.append(f"详细信息: {error.details}")
            
            if context:
                details.append(f"上下文: {context}")
            
            details.append(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 添加堆栈信息
            if hasattr(error, '__traceback__') and error.__traceback__:
                details.append("\n堆栈跟踪:")
                details.append(traceback.format_exc())
            
            self.details_text.setPlainText("\n".join(details))
        
        @staticmethod
        def show_error(error: Exception, context: str = "", parent=None):
            """显示错误对话框的静态方法
            
            Args:
                error: 异常对象
                context: 错误上下文
                parent: 父窗口
            """
            dialog = ErrorDialog(parent)
            dialog.set_error(error, context)
            dialog.exec_()
    
    
    class ProgressErrorHandler:
        """进度操作错误处理器
        
        用于处理长时间运行操作中的错误，提供进度指示和错误处理。
        """
        
        def __init__(self, parent=None):
            """初始化进度错误处理器
            
            Args:
                parent: 父窗口
            """
            self.parent = parent
            self.progress_dialog = None
            self.error_count = 0
            self.warning_count = 0
        
        def start_progress(self, title: str, message: str):
            """开始进度指示
            
            Args:
                title: 进度对话框标题
                message: 进度消息
            """
            if PYQT5_AVAILABLE and self.parent:
                from PyQt5.QtWidgets import QProgressDialog
                
                self.progress_dialog = QProgressDialog(message, "取消", 0, 0, self.parent)
                self.progress_dialog.setWindowTitle(title)
                self.progress_dialog.setModal(True)
                self.progress_dialog.show()
                
                # 处理界面事件
                QApplication.processEvents()
        
        def update_progress(self, message: str):
            """更新进度消息
            
            Args:
                message: 新的进度消息
            """
            if self.progress_dialog:
                self.progress_dialog.setLabelText(message)
                QApplication.processEvents()
        
        def handle_error(self, error: Exception, context: str = "", continue_on_error: bool = False):
            """处理进度中的错误
            
            Args:
                error: 异常对象
                context: 错误上下文
                continue_on_error: 是否在错误后继续
                
            Returns:
                bool: 是否继续执行
            """
            self.error_count += 1
            
            # 记录错误
            error_logger.log_error(error, context)
            
            if not continue_on_error:
                self.stop_progress()
                if PYQT5_AVAILABLE:
                    ErrorDialog.show_error(error, context, self.parent)
                return False
            
            # 如果继续执行，显示警告
            self.warning_count += 1
            error_logger.log_warning(f"错误已忽略: {str(error)}", context)
            
            return True
        
        def stop_progress(self):
            """停止进度指示"""
            if self.progress_dialog:
                self.progress_dialog.close()
                self.progress_dialog = None
        
        def get_error_summary(self) -> str:
            """获取错误摘要
            
            Returns:
                str: 错误摘要信息
            """
            if self.error_count == 0 and self.warning_count == 0:
                return "操作成功完成，无错误。"
            
            summary = []
            if self.error_count > 0:
                summary.append(f"发生 {self.error_count} 个错误")
            if self.warning_count > 0:
                summary.append(f"发生 {self.warning_count} 个警告")
            
            return "操作完成，" + "，".join(summary) + "。"


class ErrorHandlerMixin:
    """错误处理混入类
    
    为其他类提供统一的错误处理功能。
    """
    
    def __init__(self):
        """初始化错误处理混入"""
        self._error_logger = error_logger
        self._last_error = None
    
    def handle_error(self, error: Exception, context: str = "", show_dialog: bool = True):
        """处理错误
        
        Args:
            error: 异常对象
            context: 错误上下文
            show_dialog: 是否显示错误对话框
        """
        self._last_error = error
        
        # 记录错误
        self._error_logger.log_error(error, context)
        
        # 显示错误对话框
        if show_dialog and PYQT5_AVAILABLE and hasattr(self, 'parent'):
            ErrorDialog.show_error(error, context, getattr(self, 'parent', None))
    
    def handle_warning(self, message: str, context: str = ""):
        """处理警告
        
        Args:
            message: 警告消息
            context: 警告上下文
        """
        self._error_logger.log_warning(message, context)
    
    def get_last_error(self) -> Optional[Exception]:
        """获取最后一个错误
        
        Returns:
            Optional[Exception]: 最后一个错误，如果没有返回None
        """
        return self._last_error
    
    def clear_last_error(self):
        """清除最后一个错误"""
        self._last_error = None
    
    def safe_execute(self, func: Callable, *args, **kwargs) -> Any:
        """安全执行函数
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            Any: 函数返回值，如果出错返回None
        """
        try:
            return func(*args, **kwargs)
        except Exception as e:
            self.handle_error(e, f"执行 {func.__name__}")
            return None


def safe_call(func: Callable, default_return=None, context: str = "", 
              show_error: bool = True) -> Any:
    """安全调用函数的工具函数
    
    Args:
        func: 要调用的函数
        default_return: 出错时的默认返回值
        context: 错误上下文
        show_error: 是否显示错误
        
    Returns:
        Any: 函数返回值或默认值
    """
    try:
        return func()
    except Exception as e:
        error_logger.log_error(e, context)
        
        if show_error and PYQT5_AVAILABLE:
            # 尝试获取当前活动窗口作为父窗口
            app = QApplication.instance()
            parent = app.activeWindow() if app else None
            ErrorDialog.show_error(e, context, parent)
        
        return default_return


def validate_and_handle_errors(validation_func: Callable, error_message: str = "验证失败"):
    """验证并处理错误的装饰器
    
    Args:
        validation_func: 验证函数，返回(is_valid, error_message)
        error_message: 默认错误消息
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            try:
                # 执行验证
                is_valid, validation_error = validation_func(*args, **kwargs)
                if not is_valid:
                    raise ValidationError("输入验证", str(args), validation_error)
                
                # 执行原函数
                return func(*args, **kwargs)
                
            except Exception as e:
                error_logger.log_error(e, f"验证和执行 {func.__name__}")
                raise
        return wrapper
    return decorator


class BatchErrorHandler:
    """批量操作错误处理器
    
    用于处理批量操作中的错误，收集错误信息并提供摘要。
    """
    
    def __init__(self):
        """初始化批量错误处理器"""
        self.errors = []
        self.warnings = []
        self.success_count = 0
        self.total_count = 0
    
    def start_batch(self, total_count: int):
        """开始批量操作
        
        Args:
            total_count: 总操作数量
        """
        self.total_count = total_count
        self.errors.clear()
        self.warnings.clear()
        self.success_count = 0
    
    def handle_item_error(self, item_index: int, error: Exception, context: str = ""):
        """处理单个项目的错误
        
        Args:
            item_index: 项目索引
            error: 异常对象
            context: 错误上下文
        """
        error_info = {
            'index': item_index,
            'error': error,
            'context': context,
            'timestamp': datetime.now()
        }
        self.errors.append(error_info)
        
        # 记录错误
        error_logger.log_error(error, f"批量操作项目 {item_index}: {context}")
    
    def handle_item_warning(self, item_index: int, message: str, context: str = ""):
        """处理单个项目的警告
        
        Args:
            item_index: 项目索引
            message: 警告消息
            context: 警告上下文
        """
        warning_info = {
            'index': item_index,
            'message': message,
            'context': context,
            'timestamp': datetime.now()
        }
        self.warnings.append(warning_info)
        
        # 记录警告
        error_logger.log_warning(message, f"批量操作项目 {item_index}: {context}")
    
    def handle_item_success(self, item_index: int):
        """处理单个项目成功
        
        Args:
            item_index: 项目索引
        """
        self.success_count += 1
    
    def get_summary(self) -> Dict:
        """获取批量操作摘要
        
        Returns:
            Dict: 包含摘要信息的字典
        """
        return {
            'total_count': self.total_count,
            'success_count': self.success_count,
            'error_count': len(self.errors),
            'warning_count': len(self.warnings),
            'success_rate': self.success_count / self.total_count if self.total_count > 0 else 0,
            'errors': self.errors.copy(),
            'warnings': self.warnings.copy()
        }
    
    def get_summary_text(self) -> str:
        """获取摘要文本
        
        Returns:
            str: 摘要文本
        """
        summary = self.get_summary()
        
        lines = []
        lines.append(f"批量操作完成:")
        lines.append(f"  总数: {summary['total_count']}")
        lines.append(f"  成功: {summary['success_count']}")
        lines.append(f"  错误: {summary['error_count']}")
        lines.append(f"  警告: {summary['warning_count']}")
        lines.append(f"  成功率: {summary['success_rate']:.1%}")
        
        if summary['error_count'] > 0:
            lines.append("\n错误详情:")
            for error_info in summary['errors'][:5]:  # 只显示前5个错误
                lines.append(f"  项目 {error_info['index']}: {str(error_info['error'])}")
            
            if summary['error_count'] > 5:
                lines.append(f"  ... 还有 {summary['error_count'] - 5} 个错误")
        
        return "\n".join(lines)
    
    def has_errors(self) -> bool:
        """检查是否有错误
        
        Returns:
            bool: 是否有错误
        """
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """检查是否有警告
        
        Returns:
            bool: 是否有警告
        """
        return len(self.warnings) > 0
    
    def is_success(self) -> bool:
        """检查是否完全成功
        
        Returns:
            bool: 是否完全成功（无错误无警告）
        """
        return not self.has_errors() and not self.has_warnings()


# 导出的便利函数
def show_error_dialog(error: Exception, context: str = "", parent=None):
    """显示错误对话框的便利函数
    
    Args:
        error: 异常对象
        context: 错误上下文
        parent: 父窗口
    """
    if PYQT5_AVAILABLE:
        ErrorDialog.show_error(error, context, parent)
    else:
        # 如果PyQt5不可用，输出到控制台
        print(f"错误: {context} - {str(error)}")


def create_progress_error_handler(parent=None) -> 'ProgressErrorHandler':
    """创建进度错误处理器的便利函数
    
    Args:
        parent: 父窗口
        
    Returns:
        ProgressErrorHandler: 进度错误处理器实例
    """
    if PYQT5_AVAILABLE:
        return ProgressErrorHandler(parent)
    else:
        # 返回一个简化的处理器
        class SimpleProgressErrorHandler:
            def start_progress(self, title: str, message: str):
                print(f"开始: {title} - {message}")
            
            def update_progress(self, message: str):
                print(f"进度: {message}")
            
            def handle_error(self, error: Exception, context: str = "", continue_on_error: bool = False):
                print(f"错误: {context} - {str(error)}")
                return continue_on_error
            
            def stop_progress(self):
                print("完成")
            
            def get_error_summary(self) -> str:
                return "操作完成"
        
        return SimpleProgressErrorHandler()


def create_batch_error_handler() -> BatchErrorHandler:
    """创建批量错误处理器的便利函数
    
    Returns:
        BatchErrorHandler: 批量错误处理器实例
    """
    return BatchErrorHandler()