#!/usr/bin/env python3
"""
Test script for CaseInfoExtractor
"""

import sys
import os
sys.path.append(os.path.join(os.getcwd(), 'plugins', 'user', 'timing_violation_checker'))

from plugins.user.timing_violation_checker.utils import CaseInfoExtractor
from plugins.user.timing_violation_checker.models import VALID_CORNERS

def test_case_info_extractor():
    print("Testing CaseInfoExtractor...")
    
    # Test case 1: case_name_corner format
    try:
        case_info = CaseInfoExtractor.extract_from_path('test_case_npg_f1_ssg/log/vio_summary.log')
        print(f'Test 1 - case_name_corner: case_name={case_info.case_name}, corner={case_info.corner}')
    except Exception as e:
        print(f'Test 1 failed: {e}')
    
    # Test case 2: case_name only format
    try:
        case_info = CaseInfoExtractor.extract_from_path('test_case/log/vio_summary.log')
        print(f'Test 2 - case_name only: case_name={case_info.case_name}, corner={case_info.corner}')
    except Exception as e:
        print(f'Test 2 failed: {e}')
    
    # Test case 3: invalid corner
    try:
        case_info = CaseInfoExtractor.extract_from_path('test_case_invalid_corner/log/vio_summary.log')
        print(f'Test 3 - invalid corner: case_name={case_info.case_name}, corner={case_info.corner}')
    except Exception as e:
        print(f'Test 3 failed: {e}')
    
    # Test corner validation
    print(f'Valid corners: {VALID_CORNERS}')
    print(f'Is npg_f1_ssg valid: {CaseInfoExtractor.is_corner_valid("npg_f1_ssg")}')
    print(f'Is invalid_corner valid: {CaseInfoExtractor.is_corner_valid("invalid_corner")}')
    
    # Test corner selection functionality
    print(f'Corner display options: {CaseInfoExtractor.get_corner_display_options()}')
    
    # Test needs corner selection
    try:
        needs_selection = CaseInfoExtractor.needs_corner_selection('test_case/log/vio_summary.log')
        print(f'Test case needs corner selection: {needs_selection}')
    except Exception as e:
        print(f'Corner selection test failed: {e}')
    
    # Test create case info with corner
    try:
        case_info = CaseInfoExtractor.create_case_info_with_corner('test_case/log/vio_summary.log', 'npg_f1_ssg')
        print(f'Created case info with corner: case_name={case_info.case_name}, corner={case_info.corner}')
    except Exception as e:
        print(f'Create case info with corner failed: {e}')

if __name__ == '__main__':
    test_case_info_extractor()