"""
时序违例确认插件包

包含时序违例确认插件的所有组件和功能模块。
"""

from .models import (
    ViolationData, 
    ViolationStatus, 
    ConfirmationResult, 
    CaseInfo, 
    ExportConfig,
    VALID_CORNERS,
    is_valid_corner,
    get_corner_display_list
)

from .exceptions import (
    TimingViolationError,
    FileParseError,
    TimeConversionError,
    ExportError,
    ValidationError,
    CaseInfoExtractionError,
    PluginInitializationError,
    ConfigurationError
)

from .utils import TimeConverter

__version__ = "1.0.0"
__author__ = "RunSim Team"
__description__ = "时序违例确认工具插件"

__all__ = [
    # Models
    'ViolationData',
    'ViolationStatus', 
    'ConfirmationResult',
    'CaseInfo',
    'ExportConfig',
    'VALID_CORNERS',
    'is_valid_corner',
    'get_corner_display_list',
    
    # Exceptions
    'TimingViolationError',
    'FileParseError',
    'TimeConversionError',
    'ExportError',
    'ValidationError',
    'CaseInfoExtractionError',
    'PluginInitializationError',
    'ConfigurationError',
    
    # Utils
    'TimeConverter'
]