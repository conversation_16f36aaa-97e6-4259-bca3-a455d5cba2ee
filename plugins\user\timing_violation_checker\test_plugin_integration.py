#!/usr/bin/env python3
"""
测试时序违例确认插件的集成功能

验证插件能够正确加载、初始化和清理。
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_plugin_import():
    """测试插件导入"""
    try:
        # 测试插件主类导入
        from plugins.user.timing_violation_checker_plugin import TimingViolationCheckerPlugin
        print("✓ 插件主类导入成功")
        
        # 测试对话框导入
        from timing_violation_checker.timing_violation_dialog import TimingViolationDialog
        print("✓ 对话框类导入成功")
        
        # 测试插件实例化
        plugin = TimingViolationCheckerPlugin()
        print(f"✓ 插件实例化成功: {plugin.name} v{plugin.version}")
        
        # 测试插件属性
        assert plugin.name == "时序违例确认工具"
        assert plugin.version == "1.0.0"
        assert plugin.description == "解析和确认后仿真时序违例日志文件，支持自动化处理和导出功能"
        assert plugin.admin_controlled == False
        assert plugin.default_enabled == True
        print("✓ 插件属性验证成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_plugin_lifecycle():
    """测试插件生命周期"""
    try:
        from plugins.user.timing_violation_checker_plugin import TimingViolationCheckerPlugin
        
        # 创建插件实例
        plugin = TimingViolationCheckerPlugin()
        
        # 模拟主窗口对象
        class MockMainWindow:
            def __init__(self):
                self.tools_menu = MockMenu()
                
        class MockMenu:
            def __init__(self):
                self.actions = []
                
            def addAction(self, action):
                self.actions.append(action)
                
            def removeAction(self, action):
                if action in self.actions:
                    self.actions.remove(action)
        
        main_window = MockMainWindow()
        
        # 测试初始化
        plugin.initialize(main_window)
        print("✓ 插件初始化成功")
        
        # 验证菜单项已添加
        assert len(main_window.tools_menu.actions) == 1
        assert plugin.menu_action is not None
        print("✓ 菜单项添加成功")
        
        # 测试清理
        plugin.cleanup()
        print("✓ 插件清理成功")
        
        # 验证菜单项已移除
        assert len(main_window.tools_menu.actions) == 0
        assert plugin.main_window is None
        assert plugin.menu_action is None
        print("✓ 资源清理验证成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 生命周期测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试时序违例确认插件集成...")
    print("=" * 50)
    
    # 测试导入
    print("1. 测试插件导入...")
    import_success = test_plugin_import()
    
    if not import_success:
        print("导入测试失败，跳过后续测试")
        return False
    
    print()
    
    # 测试生命周期
    print("2. 测试插件生命周期...")
    lifecycle_success = test_plugin_lifecycle()
    
    print()
    print("=" * 50)
    
    if import_success and lifecycle_success:
        print("✓ 所有测试通过！插件集成功能正常")
        return True
    else:
        print("✗ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)