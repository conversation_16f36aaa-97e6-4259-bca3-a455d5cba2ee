"""
主对话框集成测试

测试文件选择组件与主对话框的集成。
"""

import sys
import os
import tempfile
import unittest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtTest import QTest
    from PyQt5.QtCore import Qt
    PYQT5_AVAILABLE = True
except ImportError:
    PYQT5_AVAILABLE = False

if PYQT5_AVAILABLE:
    from plugins.user.timing_violation_checker.timing_violation_dialog import TimingViolationDialog
    from plugins.user.timing_violation_checker.models import CaseInfo


@unittest.skipUnless(PYQT5_AVAILABLE, "PyQt5 not available")
class TestDialogIntegration(unittest.TestCase):
    """主对话框集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.dialog = TimingViolationDialog()
        
        # 创建临时测试目录
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """清理测试"""
        # 清理临时文件
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        
        # 清理对话框
        if hasattr(self, 'dialog'):
            self.dialog.close()
            self.dialog.deleteLater()
    
    def create_test_file(self, case_dir_name: str) -> str:
        """创建测试文件
        
        Args:
            case_dir_name: 用例目录名称
            
        Returns:
            str: 测试文件路径
        """
        case_dir = os.path.join(self.temp_dir, case_dir_name)
        log_dir = os.path.join(case_dir, "log")
        file_path = os.path.join(log_dir, "vio_summary.log")
        
        os.makedirs(log_dir, exist_ok=True)
        with open(file_path, 'w') as f:
            f.write("test violation summary content")
        
        return file_path
    
    def test_dialog_initialization(self):
        """测试对话框初始化"""
        # 验证对话框基本属性
        self.assertEqual(self.dialog.windowTitle(), "时序违例确认工具")
        self.assertIsNotNone(self.dialog.file_selection_widget)
        
        # 验证初始状态
        self.assertFalse(self.dialog.is_ready_for_processing())
        self.assertEqual(self.dialog.get_current_file_path(), "")
        self.assertIsNone(self.dialog.get_current_case_info())
        
        # 验证按钮初始状态
        self.assertFalse(self.dialog.export_excel_button.isEnabled())
        self.assertFalse(self.dialog.export_csv_button.isEnabled())
    
    def test_file_selection_integration(self):
        """测试文件选择集成"""
        # 创建测试文件
        test_file = self.create_test_file("dialog_test_npg_f1_ssg")
        
        # 通过对话框加载文件
        self.dialog.load_file(test_file)
        
        # 验证文件选择结果
        self.assertEqual(self.dialog.get_current_file_path(), test_file)
        self.assertTrue(self.dialog.is_ready_for_processing())
        
        # 验证用例信息
        case_info = self.dialog.get_current_case_info()
        self.assertIsNotNone(case_info)
        self.assertEqual(case_info.case_name, "dialog_test")
        self.assertEqual(case_info.corner, "npg_f1_ssg")
        
        # 验证按钮状态
        self.assertTrue(self.dialog.export_excel_button.isEnabled())
        self.assertTrue(self.dialog.export_csv_button.isEnabled())
    
    def test_corner_selection_integration(self):
        """测试corner选择集成"""
        # 创建不包含corner的测试文件
        test_file = self.create_test_file("dialog_corner_test")
        
        # 通过对话框加载文件
        self.dialog.load_file(test_file)
        
        # 验证初始状态
        self.assertFalse(self.dialog.is_ready_for_processing())
        self.assertFalse(self.dialog.export_excel_button.isEnabled())
        self.assertFalse(self.dialog.export_csv_button.isEnabled())
        
        # 验证corner选择组件存在
        file_widget = self.dialog.file_selection_widget
        self.assertIsNotNone(file_widget.corner_selection_widget)
        
        # 模拟corner选择
        file_widget.on_corner_selected("npg_f2_tt")
        
        # 验证选择后状态
        self.assertTrue(self.dialog.is_ready_for_processing())
        self.assertTrue(self.dialog.export_excel_button.isEnabled())
        self.assertTrue(self.dialog.export_csv_button.isEnabled())
        
        # 验证用例信息更新
        case_info = self.dialog.get_current_case_info()
        self.assertEqual(case_info.corner, "npg_f2_tt")
    
    def test_signal_handling(self):
        """测试信号处理"""
        # 创建测试文件
        test_file = self.create_test_file("signal_test_npg_f3_ffg")
        
        # 记录信号调用
        file_selected_calls = []
        case_info_calls = []
        
        # 直接连接到文件选择组件的信号
        def on_file_selected(path):
            file_selected_calls.append(path)
        
        def on_case_info_extracted(case_info):
            case_info_calls.append(case_info)
        
        # 连接信号
        self.dialog.file_selection_widget.file_selected.connect(on_file_selected)
        self.dialog.file_selection_widget.case_info_extracted.connect(on_case_info_extracted)
        
        # 触发文件选择
        self.dialog.file_selection_widget.set_file_path(test_file)
        
        # 验证信号调用
        self.assertEqual(len(file_selected_calls), 1)
        self.assertEqual(file_selected_calls[0], test_file)
        self.assertEqual(len(case_info_calls), 1)
        self.assertEqual(case_info_calls[0].case_name, "signal_test")
    
    def test_dialog_state_management(self):
        """测试对话框状态管理"""
        # 验证初始状态
        self.assertFalse(self.dialog.is_ready_for_processing())
        
        # 设置有效文件
        test_file = self.create_test_file("state_test_npg_f1_tt")
        self.dialog.load_file(test_file)
        
        # 验证处理就绪状态
        self.assertTrue(self.dialog.is_ready_for_processing())
        
        # 清除文件选择
        self.dialog.file_selection_widget.clear_file_selection()
        
        # 验证状态重置
        self.assertFalse(self.dialog.is_ready_for_processing())
        self.assertEqual(self.dialog.get_current_file_path(), "")
        self.assertIsNone(self.dialog.get_current_case_info())
    
    def test_ui_component_integration(self):
        """测试UI组件集成"""
        # 验证文件选择组件存在
        self.assertIsNotNone(self.dialog.file_selection_widget)
        
        # 验证按钮存在
        self.assertIsNotNone(self.dialog.export_excel_button)
        self.assertIsNotNone(self.dialog.export_csv_button)
        
        # 验证布局
        layout = self.dialog.layout()
        self.assertIsNotNone(layout)
        
        # 验证文件选择组件在布局中
        found_file_widget = False
        for i in range(layout.count()):
            item = layout.itemAt(i)
            if item and item.widget() == self.dialog.file_selection_widget:
                found_file_widget = True
                break
        
        self.assertTrue(found_file_widget, "文件选择组件应该在主布局中")
    
    def test_error_state_handling(self):
        """测试错误状态处理"""
        # 设置无效文件
        invalid_file = "/invalid/path/vio_summary.log"
        self.dialog.load_file(invalid_file)
        
        # 验证错误状态
        self.assertFalse(self.dialog.is_ready_for_processing())
        self.assertFalse(self.dialog.export_excel_button.isEnabled())
        self.assertFalse(self.dialog.export_csv_button.isEnabled())
        self.assertIsNone(self.dialog.get_current_case_info())


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)