"""
时序违例列表显示组件

提供时序违例数据的表格显示、交互操作和状态管理功能。
"""

from typing import List, Optional
from PyQt5.QtWidgets import (QTableWidget, QTableWidgetItem, QHeaderView, 
                             QPushButton, QHBoxLayout, QWidget, QMessageBox,
                             QAbstractItemView, QMenu, QAction)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QColor, QFont, QCursor

try:
    # 尝试相对导入（在插件环境中）
    from .models import ViolationData, ViolationStatus, ConfirmationResult
    from .utils import TimeConverter
    from .exceptions import TimingViolationError
except ImportError:
    # 如果相对导入失败，尝试直接导入（在测试环境中）
    from models import ViolationData, ViolationStatus, ConfirmationResult
    from utils import TimeConverter
    from exceptions import TimingViolationError


class ViolationListWidget(QTableWidget):
    """时序违例列表显示组件
    
    继承QTableWidget，提供时序违例数据的表格显示、状态颜色标识、
    交互操作和确认功能。
    """
    
    # 自定义信号
    violation_selected = pyqtSignal(ViolationData)      # 违例选择信号
    confirm_requested = pyqtSignal(ViolationData)       # 确认请求信号
    detail_requested = pyqtSignal(ViolationData)        # 详情查看信号
    status_changed = pyqtSignal(ViolationData)          # 状态变化信号
    
    # 表格列定义
    COLUMN_NUM = 0
    COLUMN_HIER = 1
    COLUMN_TIME_ORIGINAL = 2
    COLUMN_TIME_FS = 3
    COLUMN_CHECK = 4
    COLUMN_STATUS = 5
    COLUMN_CONFIRMER = 6
    COLUMN_ACTION = 7
    
    # 列标题
    COLUMN_HEADERS = [
        "NUM", "Hier", "Time", "Time(FS)", "Check", "状态", "确认人", "操作"
    ]
    
    # 状态颜色配置
    STATUS_COLORS = {
        ViolationStatus.PENDING: QColor("#FFF3CD"),           # 待确认 - 黄色
        ViolationStatus.CONFIRMED_OK: QColor("#D4EDDA"),      # 已确认-无问题 - 绿色
        ViolationStatus.CONFIRMED_ISSUE: QColor("#F8D7DA"),   # 已确认-有问题 - 红色
        ViolationStatus.AUTO_CONFIRMED: QColor("#E2E3E5"),    # 自动确认 - 灰色
    }
    
    def __init__(self, parent=None):
        """初始化违例列表组件
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        # 初始化数据
        self.violations = []
        self.action_buttons = {}  # 存储操作按钮的引用
        
        # 设置表格属性
        self._setup_table()
        
        # 连接信号
        self._connect_signals()
    
    def _setup_table(self):
        """设置表格属性和样式"""
        # 设置列数和标题
        self.setColumnCount(len(self.COLUMN_HEADERS))
        self.setHorizontalHeaderLabels(self.COLUMN_HEADERS)
        
        # 设置表格属性
        self.setSelectionBehavior(QAbstractItemView.SelectRows)  # 选择整行
        self.setSelectionMode(QAbstractItemView.SingleSelection)  # 单选
        self.setAlternatingRowColors(True)  # 交替行颜色
        self.setSortingEnabled(True)  # 启用排序
        
        # 设置表头属性
        header = self.horizontalHeader()
        header.setStretchLastSection(False)  # 最后一列不自动拉伸
        
        # 设置列宽
        self._setup_column_widths()
        
        # 设置表格样式
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #007bff;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 5px;
                border: none;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)
        
        # 设置右键菜单
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self._show_context_menu)
    
    def _setup_column_widths(self):
        """设置列宽"""
        header = self.horizontalHeader()
        
        # 设置各列的调整模式和宽度
        header.setSectionResizeMode(self.COLUMN_NUM, QHeaderView.Fixed)
        self.setColumnWidth(self.COLUMN_NUM, 60)
        
        header.setSectionResizeMode(self.COLUMN_HIER, QHeaderView.Stretch)
        
        header.setSectionResizeMode(self.COLUMN_TIME_ORIGINAL, QHeaderView.Fixed)
        self.setColumnWidth(self.COLUMN_TIME_ORIGINAL, 100)
        
        header.setSectionResizeMode(self.COLUMN_TIME_FS, QHeaderView.Fixed)
        self.setColumnWidth(self.COLUMN_TIME_FS, 120)
        
        header.setSectionResizeMode(self.COLUMN_CHECK, QHeaderView.Stretch)
        
        header.setSectionResizeMode(self.COLUMN_STATUS, QHeaderView.Fixed)
        self.setColumnWidth(self.COLUMN_STATUS, 120)
        
        header.setSectionResizeMode(self.COLUMN_CONFIRMER, QHeaderView.Fixed)
        self.setColumnWidth(self.COLUMN_CONFIRMER, 100)
        
        header.setSectionResizeMode(self.COLUMN_ACTION, QHeaderView.Fixed)
        self.setColumnWidth(self.COLUMN_ACTION, 120)
    
    def _connect_signals(self):
        """连接信号和槽"""
        # 连接选择变化信号
        self.itemSelectionChanged.connect(self._on_selection_changed)
        
        # 连接双击信号
        self.itemDoubleClicked.connect(self._on_item_double_clicked)
    
    def load_violations(self, violations: List[ViolationData]):
        """加载违例数据到表格
        
        Args:
            violations: 违例数据列表
        """
        try:
            # 清空现有数据（但不清空violations列表）
            self.action_buttons.clear()
            self.setRowCount(0)
            
            # 保存数据
            self.violations = violations.copy()
            
            # 设置行数
            self.setRowCount(len(violations))
            
            # 填充数据
            for row, violation in enumerate(violations):
                self._populate_row(row, violation)
            
            # 调整表格显示
            self._adjust_table_display()
            
        except Exception as e:
            QMessageBox.critical(self, "数据加载错误", 
                               f"加载违例数据时发生错误:\n{str(e)}")
    
    def _populate_row(self, row: int, violation: ViolationData):
        """填充单行数据
        
        Args:
            row: 行索引
            violation: 违例数据
        """
        try:
            # NUM列
            num_item = QTableWidgetItem(str(violation.num))
            num_item.setTextAlignment(Qt.AlignCenter)
            num_item.setData(Qt.UserRole, violation)  # 存储违例数据
            self.setItem(row, self.COLUMN_NUM, num_item)
            
            # Hier列
            hier_item = QTableWidgetItem(violation.hier)
            hier_item.setToolTip(violation.hier)  # 设置工具提示
            self.setItem(row, self.COLUMN_HIER, hier_item)
            
            # Time列（原始时间）
            time_item = QTableWidgetItem(violation.time_original)
            time_item.setTextAlignment(Qt.AlignCenter)
            time_item.setToolTip(f"原始时间: {violation.time_original}")
            self.setItem(row, self.COLUMN_TIME_ORIGINAL, time_item)
            
            # Time(FS)列（飞秒时间）
            fs_time_text = TimeConverter.format_time(violation.time_fs, "FS")
            fs_item = QTableWidgetItem(fs_time_text)
            fs_item.setTextAlignment(Qt.AlignCenter)
            fs_item.setToolTip(f"飞秒时间: {fs_time_text}")
            self.setItem(row, self.COLUMN_TIME_FS, fs_item)
            
            # Check列
            check_item = QTableWidgetItem(violation.check)
            check_item.setToolTip(violation.check)
            self.setItem(row, self.COLUMN_CHECK, check_item)
            
            # 状态列
            status_item = QTableWidgetItem(violation.get_status_display())
            status_item.setTextAlignment(Qt.AlignCenter)
            self._apply_status_color(status_item, violation.status)
            self.setItem(row, self.COLUMN_STATUS, status_item)
            
            # 确认人列
            confirmer_item = QTableWidgetItem(violation.confirmer)
            confirmer_item.setTextAlignment(Qt.AlignCenter)
            confirmer_item.setToolTip(violation.confirmer if violation.confirmer else "未确认")
            self.setItem(row, self.COLUMN_CONFIRMER, confirmer_item)
            
            # 操作列（按钮）
            self._create_action_button(row, violation)
            
        except Exception as e:
            print(f"填充行数据时发生错误 (行 {row}): {str(e)}")
    
    def _apply_status_color(self, item: QTableWidgetItem, status: ViolationStatus):
        """应用状态颜色
        
        Args:
            item: 表格项
            status: 违例状态
        """
        if status in self.STATUS_COLORS:
            color = self.STATUS_COLORS[status]
            item.setBackground(color)
            
            # 根据背景色调整文字颜色
            if status == ViolationStatus.CONFIRMED_ISSUE:
                item.setForeground(QColor("#721c24"))  # 深红色文字
            elif status == ViolationStatus.CONFIRMED_OK:
                item.setForeground(QColor("#155724"))  # 深绿色文字
            elif status == ViolationStatus.AUTO_CONFIRMED:
                item.setForeground(QColor("#383d41"))  # 深灰色文字
            else:
                item.setForeground(QColor("#856404"))  # 深黄色文字
    
    def _create_action_button(self, row: int, violation: ViolationData):
        """创建操作按钮
        
        Args:
            row: 行索引
            violation: 违例数据
        """
        # 创建按钮容器
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(2, 2, 2, 2)
        button_layout.setSpacing(2)
        
        if violation.is_confirmed():
            # 已确认状态 - 显示查看按钮
            view_button = QPushButton("查看")
            view_button.setMinimumSize(50, 25)
            view_button.setMaximumSize(60, 25)
            view_button.setStyleSheet("""
                QPushButton {
                    background-color: #17a2b8;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #138496;
                }
                QPushButton:pressed {
                    background-color: #117a8b;
                }
            """)
            view_button.clicked.connect(lambda: self._on_view_clicked(violation))
            button_layout.addWidget(view_button)
            
            # 存储按钮引用
            self.action_buttons[row] = {'view': view_button}
        else:
            # 未确认状态 - 显示确认按钮
            confirm_button = QPushButton("确认")
            confirm_button.setMinimumSize(50, 25)
            confirm_button.setMaximumSize(60, 25)
            confirm_button.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
                QPushButton:pressed {
                    background-color: #1e7e34;
                }
            """)
            confirm_button.clicked.connect(lambda: self._on_confirm_clicked(violation))
            button_layout.addWidget(confirm_button)
            
            # 存储按钮引用
            self.action_buttons[row] = {'confirm': confirm_button}
        
        # 设置按钮容器到表格
        self.setCellWidget(row, self.COLUMN_ACTION, button_widget)
    
    def _adjust_table_display(self):
        """调整表格显示"""
        # 调整行高
        for row in range(self.rowCount()):
            self.setRowHeight(row, 35)
        
        # 确保表格内容可见
        if self.rowCount() > 0:
            self.scrollToTop()
    
    def clear_table(self):
        """清空表格数据"""
        # 清空操作按钮引用
        self.action_buttons.clear()
        
        # 清空违例数据
        self.violations.clear()
        
        # 清空表格
        self.setRowCount(0)
        
        # 重新设置表头
        self.setHorizontalHeaderLabels(self.COLUMN_HEADERS)
    
    def update_violation_status(self, violation: ViolationData):
        """更新违例状态显示
        
        Args:
            violation: 更新后的违例数据
        """
        try:
            # 查找对应的行
            row = self._find_violation_row(violation)
            if row == -1:
                print(f"未找到违例 NUM={violation.num} 对应的行")
                return
            
            # 更新状态列
            status_item = self.item(row, self.COLUMN_STATUS)
            if status_item:
                status_item.setText(violation.get_status_display())
                self._apply_status_color(status_item, violation.status)
            
            # 更新确认人列
            confirmer_item = self.item(row, self.COLUMN_CONFIRMER)
            if confirmer_item:
                confirmer_item.setText(violation.confirmer)
                confirmer_item.setToolTip(violation.confirmer if violation.confirmer else "未确认")
            
            # 更新操作按钮
            self._update_action_button(row, violation)
            
            # 更新数据列表中的对应项
            for i, v in enumerate(self.violations):
                if v.num == violation.num:
                    self.violations[i] = violation
                    break
            
            # 发送状态变化信号
            self.status_changed.emit(violation)
            
        except Exception as e:
            print(f"更新违例状态时发生错误: {str(e)}")
    
    def _find_violation_row(self, violation: ViolationData) -> int:
        """查找违例对应的行索引
        
        Args:
            violation: 违例数据
            
        Returns:
            int: 行索引，如果未找到返回-1
        """
        for row in range(self.rowCount()):
            num_item = self.item(row, self.COLUMN_NUM)
            if num_item and int(num_item.text()) == violation.num:
                return row
        return -1
    
    def _update_action_button(self, row: int, violation: ViolationData):
        """更新操作按钮
        
        Args:
            row: 行索引
            violation: 违例数据
        """
        # 移除旧按钮
        if row in self.action_buttons:
            del self.action_buttons[row]
        
        # 创建新按钮
        self._create_action_button(row, violation)
    
    def get_selected_violation(self) -> Optional[ViolationData]:
        """获取当前选择的违例数据
        
        Returns:
            Optional[ViolationData]: 选择的违例数据，如果没有选择返回None
        """
        current_row = self.currentRow()
        if current_row >= 0 and current_row < len(self.violations):
            return self.violations[current_row]
        return None
    
    def get_all_violations(self) -> List[ViolationData]:
        """获取所有违例数据
        
        Returns:
            List[ViolationData]: 所有违例数据列表
        """
        return self.violations.copy()
    
    def get_pending_violations(self) -> List[ViolationData]:
        """获取待确认的违例数据
        
        Returns:
            List[ViolationData]: 待确认违例数据列表
        """
        return [v for v in self.violations if v.status == ViolationStatus.PENDING]
    
    def get_confirmed_violations(self) -> List[ViolationData]:
        """获取已确认的违例数据
        
        Returns:
            List[ViolationData]: 已确认违例数据列表
        """
        return [v for v in self.violations if v.is_confirmed()]
    
    def get_violation_count_by_status(self) -> dict:
        """获取各状态违例数量统计
        
        Returns:
            dict: 状态统计字典
        """
        counts = {status: 0 for status in ViolationStatus}
        
        for violation in self.violations:
            counts[violation.status] += 1
        
        return counts
    
    def _on_selection_changed(self):
        """处理选择变化事件"""
        selected_violation = self.get_selected_violation()
        if selected_violation:
            self.violation_selected.emit(selected_violation)
    
    def _on_item_double_clicked(self, item: QTableWidgetItem):
        """处理双击事件
        
        Args:
            item: 被双击的表格项
        """
        # 获取对应的违例数据
        row = item.row()
        if row >= 0 and row < len(self.violations):
            violation = self.violations[row]
            self.detail_requested.emit(violation)
    
    def _on_confirm_clicked(self, violation: ViolationData):
        """处理确认按钮点击事件
        
        Args:
            violation: 违例数据
        """
        self.confirm_requested.emit(violation)
    
    def _on_view_clicked(self, violation: ViolationData):
        """处理查看按钮点击事件
        
        Args:
            violation: 违例数据
        """
        self.detail_requested.emit(violation)
    
    def _show_context_menu(self, position):
        """显示右键菜单
        
        Args:
            position: 右键点击位置
        """
        item = self.itemAt(position)
        if not item:
            return
        
        # 获取对应的违例数据
        row = item.row()
        if row < 0 or row >= len(self.violations):
            return
        
        violation = self.violations[row]
        
        # 创建右键菜单
        menu = QMenu(self)
        
        # 查看详情动作
        detail_action = QAction("查看详情", self)
        detail_action.triggered.connect(lambda: self.detail_requested.emit(violation))
        menu.addAction(detail_action)
        
        # 根据状态添加不同的动作
        if not violation.is_confirmed():
            menu.addSeparator()
            confirm_action = QAction("确认违例", self)
            confirm_action.triggered.connect(lambda: self.confirm_requested.emit(violation))
            menu.addAction(confirm_action)
        
        # 显示菜单
        menu.exec_(self.mapToGlobal(position))
    
    def select_violation_by_num(self, num: int) -> bool:
        """根据NUM选择违例
        
        Args:
            num: 违例序号
            
        Returns:
            bool: 是否选择成功
        """
        for row in range(self.rowCount()):
            num_item = self.item(row, self.COLUMN_NUM)
            if num_item and int(num_item.text()) == num:
                self.selectRow(row)
                self.scrollToItem(num_item)
                return True
        return False
    
    def filter_by_status(self, status: ViolationStatus):
        """按状态过滤显示
        
        Args:
            status: 要显示的状态
        """
        for row in range(self.rowCount()):
            violation = self.violations[row] if row < len(self.violations) else None
            if violation:
                should_show = violation.status == status
                self.setRowHidden(row, not should_show)
    
    def show_all_violations(self):
        """显示所有违例"""
        for row in range(self.rowCount()):
            self.setRowHidden(row, False)
    
    def get_visible_violation_count(self) -> int:
        """获取可见违例数量
        
        Returns:
            int: 可见违例数量
        """
        count = 0
        for row in range(self.rowCount()):
            if not self.isRowHidden(row):
                count += 1
        return count
    
    def export_visible_data(self) -> List[dict]:
        """导出可见数据
        
        Returns:
            List[dict]: 可见违例数据列表
        """
        visible_data = []
        
        for row in range(self.rowCount()):
            if not self.isRowHidden(row) and row < len(self.violations):
                violation = self.violations[row]
                visible_data.append({
                    'num': violation.num,
                    'hier': violation.hier,
                    'time_original': violation.time_original,
                    'time_fs': violation.time_fs,
                    'check': violation.check,
                    'status': violation.get_status_display(),
                    'confirmer': violation.confirmer,
                    'reason': violation.reason,
                    'auto_confirmed': violation.auto_confirmed
                })
        
        return visible_data
    
    def refresh_display(self):
        """刷新显示"""
        # 重新加载当前数据
        current_violations = self.violations.copy()
        self.load_violations(current_violations)
    
    def set_font_size(self, size: int):
        """设置字体大小
        
        Args:
            size: 字体大小
        """
        font = QFont()
        font.setPointSize(size)
        self.setFont(font)
    
    def get_summary_info(self) -> dict:
        """获取汇总信息
        
        Returns:
            dict: 汇总信息字典
        """
        total_count = len(self.violations)
        status_counts = self.get_violation_count_by_status()
        visible_count = self.get_visible_violation_count()
        
        return {
            'total_count': total_count,
            'visible_count': visible_count,
            'pending_count': status_counts.get(ViolationStatus.PENDING, 0),
            'confirmed_ok_count': status_counts.get(ViolationStatus.CONFIRMED_OK, 0),
            'confirmed_issue_count': status_counts.get(ViolationStatus.CONFIRMED_ISSUE, 0),
            'auto_confirmed_count': status_counts.get(ViolationStatus.AUTO_CONFIRMED, 0)
        }