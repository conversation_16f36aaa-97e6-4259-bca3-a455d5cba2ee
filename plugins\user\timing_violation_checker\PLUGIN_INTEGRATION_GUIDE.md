# 时序违例确认插件集成指南

## 概述

时序违例确认插件已成功集成到runsim GUI系统中，遵循现有的插件架构模式和设计风格。

## 集成特性

### 1. 插件管理系统兼容性

- **自动注册**: 插件会自动被插件管理器发现和加载
- **配置管理**: 支持通过插件管理器进行启用/禁用控制
- **热重载**: 支持插件代码的热重载功能
- **权限控制**: 不需要管理员权限，普通用户即可使用

### 2. 菜单集成

- **工具菜单**: 插件菜单项添加到主窗口的"工具"菜单中
- **状态提示**: 菜单项包含状态栏提示信息
- **自动清理**: 插件禁用时自动移除菜单项

### 3. 配置持久化

- **配置文件**: `timing_violation_checker_config.json`
- **窗口几何**: 自动保存和恢复对话框位置和大小
- **用户偏好**: 支持各种用户界面偏好设置
- **导出设置**: 记住用户的导出格式偏好

### 4. GUI设计风格一致性

- **非模态对话框**: 使用NonModalDialog基类确保一致的窗口行为
- **样式继承**: 自动继承主窗口的样式表和字体设置
- **窗口管理**: 集成到主窗口的活动插件窗口管理系统

## 配置选项

### 默认配置

```json
{
    "window_geometry": {
        "width": 1000,
        "height": 700,
        "x": 100,
        "y": 100
    },
    "default_reset_time": 0,
    "auto_confirm_reset_violations": true,
    "export_format": "excel",
    "include_auto_confirmed_in_export": true,
    "last_file_directory": "",
    "ui_preferences": {
        "show_time_in_femtoseconds": true,
        "highlight_unconfirmed": true,
        "auto_refresh_on_file_change": false
    }
}
```

### 配置说明

- `window_geometry`: 对话框窗口的位置和大小
- `default_reset_time`: 默认复位时间（飞秒）
- `auto_confirm_reset_violations`: 是否自动确认复位期间的违例
- `export_format`: 默认导出格式（excel/csv）
- `include_auto_confirmed_in_export`: 导出时是否包含自动确认的违例
- `last_file_directory`: 上次选择文件的目录
- `ui_preferences`: 用户界面偏好设置

## 使用方法

### 1. 启动插件

1. 启动runsim GUI应用程序
2. 在菜单栏中选择"工具" -> "时序违例确认工具"
3. 插件对话框将打开并显示

### 2. 插件管理

1. 在菜单栏中选择"插件" -> "管理插件..."
2. 在插件列表中找到"时序违例确认工具"
3. 可以启用/禁用插件或查看插件信息

### 3. 配置管理

- 插件配置会自动保存到`timing_violation_checker_config.json`
- 窗口位置和大小会在关闭时自动保存
- 用户偏好设置会持久化保存

## 错误处理

### 常见问题

1. **插件无法加载**
   - 检查依赖项是否完整
   - 查看控制台错误信息
   - 确认PyQt5已正确安装

2. **菜单项未显示**
   - 检查插件是否已启用
   - 重启应用程序
   - 检查插件管理器中的状态

3. **配置丢失**
   - 检查配置文件权限
   - 确认配置文件格式正确
   - 删除配置文件让插件重新创建

### 调试信息

插件会在控制台输出调试信息：
- `[插件] 时序违例确认工具 v1.0.0 初始化成功`
- `[插件] 时序违例确认工具 清理完成`
- 错误信息会包含详细的异常信息

## 开发者信息

### 插件结构

```
plugins/user/timing_violation_checker_plugin.py  # 主插件文件
plugins/user/timing_violation_checker/           # 插件包目录
├── __init__.py                                  # 包初始化
├── timing_violation_dialog.py                  # 主对话框
├── models.py                                    # 数据模型
├── utils.py                                     # 工具函数
├── exceptions.py                                # 异常定义
└── ...                                          # 其他组件
```

### 扩展插件

要扩展插件功能，可以：
1. 修改配置选项
2. 添加新的菜单项
3. 扩展对话框功能
4. 添加新的工具组件

### 测试

运行集成测试：
```bash
python plugins/user/timing_violation_checker/test_plugin_system_integration.py
```

## 版本信息

- **插件版本**: 1.0.0
- **兼容性**: runsim GUI v1.0+
- **依赖**: PyQt5, Python 3.6+
- **作者**: RunSim Team