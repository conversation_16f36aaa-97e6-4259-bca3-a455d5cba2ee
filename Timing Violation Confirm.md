开发一款后仿时序为例确认插件，具体要求如下：
1. 由用户选择时序为例日志vio_summary.log，其路径是{用例仿真目录}/log/vio_summary.log
2. 解析该时序为例log，形成一份checklist，由用户一一确认，其中vio_summary.log格式如下：
   ```
   ------------------------------------------------------------
   NUM    : 1
   Hier   : tb_top.xxx.xxx
   Time   : 1523423 FS
   Check  : setup( posedge xxx, xxx )
   ------------------------------------------------------------
   NUM    : 2
   Hier   : tb_top.xxx.xxx
   Time   : 1523423 FS
   Check  : hold(posedge xxx, xxx)
   ------------------------------------------------------------
   NUM    : 3
   ```
   vio_summary.log中，每个时序为例信息由"----"分隔，每个时序为例信息中，每行由" : "分隔，其中" : "分隔的左边是key，右边是value，NUM表格时序为例序号，Hier表示时序为例发生层级，Time表示时序为例发生时间（FS或者PS或者NS，即飞秒、皮秒、纳秒，1皮秒(ps) =1000飞秒(fs) = 1000000纳秒(ns)），Check表示为例信息
3. 首先要进行时间信息判断，由用户输入复位时间，如果时序为例发生的时间小于复位时间，则自动标记这些时序为例为“已确认”，备注是“复位期间时序为例，可以忽略”
4. 其他时序为例需要用户一一点击确认，需要给出确认人以及理由，如果确认没有问题，则需要给出理由，如果有问题，则需要给出具体解决方案
5. 仿真用例目录的具体格式是{case_name}_{corner}，其中case_name是仿真用例名称，corner是仿真用例corner，插件GUI要体现这两个信息，不过如果{case_name}后面没有跟corner信息，则可以给出下拉框，有用户自主选择，合法corner如下：
   ```
   npg_f1_ssg、npg_f2_ssg、npg_f3_ssg、npg_f4_ssg、npg_f5_ssg、npg_f6_ssg、npg_f7_ssg、npg_f1_ffg、npg_f2_ffg、npg_f3_ffg、npg_f4_ffg、npg_f5_ffg、npg_f6_ffg、npg_f7_ffg、npg_f1_tt、npg_f2_tt、npg_f3_tt
   ```
6. 需要支持checklist导出，表格信息需要包括所有时序为例信息、确认结果、确认人以及确认备注等信息
7. 先给出具体UI设计图让我确认