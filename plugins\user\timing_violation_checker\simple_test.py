"""
简单测试ViolationListWidget基本功能
"""

import sys
import os

# 添加插件路径到sys.path
plugin_path = os.path.dirname(os.path.abspath(__file__))
if plugin_path not in sys.path:
    sys.path.insert(0, plugin_path)

from PyQt5.QtWidgets import QApplication
from models import ViolationData, ViolationStatus
from violation_list_widget import ViolationListWidget


def test_basic_functionality():
    """测试基本功能"""
    app = QApplication(sys.argv)
    
    # 创建组件
    widget = ViolationListWidget()
    
    # 创建测试数据
    violations = [
        ViolationData(
            num=1,
            hier="tb_top.cpu.core0",
            time_original="1500000FS",
            time_fs=1500000,
            check="setup(posedge clk, data)",
            status=ViolationStatus.PENDING
        ),
        ViolationData(
            num=2,
            hier="tb_top.memory.ctrl",
            time_original="2000000FS",
            time_fs=2000000,
            check="hold(posedge clk, addr)",
            status=ViolationStatus.CONFIRMED_OK
        )
    ]
    
    print("创建了测试数据:", len(violations))
    
    # 加载数据
    widget.load_violations(violations)
    print("加载数据后行数:", widget.rowCount())
    print("存储的违例数量:", len(widget.get_all_violations()))
    
    # 测试获取汇总信息
    summary = widget.get_summary_info()
    print("汇总信息:", summary)
    
    # 测试过滤
    widget.filter_by_status(ViolationStatus.PENDING)
    visible_count = widget.get_visible_violation_count()
    print("过滤后可见数量:", visible_count)
    
    # 测试导出
    export_data = widget.export_visible_data()
    print("导出数据数量:", len(export_data))
    
    print("基本功能测试完成")


if __name__ == '__main__':
    test_basic_functionality()