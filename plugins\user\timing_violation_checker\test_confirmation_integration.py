#!/usr/bin/env python3
"""
测试确认对话框集成功能

测试确认对话框与主对话框和违例列表的集成功能。
"""

import sys
import os

# 添加项目路径到sys.path
sys.path.append(os.path.join(os.getcwd(), 'plugins', 'user', 'timing_violation_checker'))

from PyQt5.QtWidgets import QApplication, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt

from models import ViolationData, ViolationStatus, ConfirmationResult
from timing_violation_dialog import TimingViolationDialog
from violation_list_widget import ViolationListWidget
from confirmation_dialog import ConfirmationDialog


def create_test_violations() -> list:
    """创建测试用的违例数据列表"""
    violations = [
        ViolationData(
            num=1,
            hier="tb_top.cpu.core.alu",
            time_original="1523423FS",
            time_fs=1523423,
            check="setup(posedge clk, data)",
            status=ViolationStatus.PENDING
        ),
        ViolationData(
            num=2,
            hier="tb_top.cpu.cache.ctrl",
            time_original="2000000FS",
            time_fs=2000000,
            check="hold(posedge clk, addr)",
            status=ViolationStatus.PENDING
        ),
        ViolationData(
            num=3,
            hier="tb_top.mem.controller",
            time_original="500FS",
            time_fs=500,
            check="setup(negedge clk, we)",
            status=ViolationStatus.AUTO_CONFIRMED,
            confirmer="系统自动",
            reason="复位期间时序违例，可以忽略",
            auto_confirmed=True
        )
    ]
    return violations


class TestIntegrationWindow(QWidget):
    """集成测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("确认对话框集成测试")
        self.setGeometry(100, 100, 500, 400)
        
        # 创建测试数据
        self.test_violations = create_test_violations()
        
        # 设置界面
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout()
        
        # 测试主对话框集成
        main_dialog_button = QPushButton("测试主对话框集成")
        main_dialog_button.clicked.connect(self.test_main_dialog_integration)
        layout.addWidget(main_dialog_button)
        
        # 测试违例列表集成
        list_widget_button = QPushButton("测试违例列表集成")
        list_widget_button.clicked.connect(self.test_list_widget_integration)
        layout.addWidget(list_widget_button)
        
        # 测试确认流程
        workflow_button = QPushButton("测试完整确认流程")
        workflow_button.clicked.connect(self.test_confirmation_workflow)
        layout.addWidget(workflow_button)
        
        # 测试批量确认
        batch_button = QPushButton("测试批量确认")
        batch_button.clicked.connect(self.test_batch_confirmation)
        layout.addWidget(batch_button)
        
        # 测试统计信息
        stats_button = QPushButton("测试统计信息")
        stats_button.clicked.connect(self.test_statistics)
        layout.addWidget(stats_button)
        
        self.setLayout(layout)
    
    def test_main_dialog_integration(self):
        """测试主对话框集成"""
        print("测试主对话框集成...")
        
        try:
            # 创建主对话框
            dialog = TimingViolationDialog(self)
            
            # 加载测试违例数据
            dialog.load_violations_to_list(self.test_violations)
            
            # 显示对话框
            dialog.show()
            
            print("✓ 主对话框集成测试成功")
            
        except Exception as e:
            print(f"✗ 主对话框集成测试失败: {e}")
            QMessageBox.critical(self, "错误", f"主对话框集成测试失败:\n{str(e)}")
    
    def test_list_widget_integration(self):
        """测试违例列表集成"""
        print("测试违例列表集成...")
        
        try:
            # 创建违例列表组件
            list_widget = ViolationListWidget(self)
            
            # 加载测试数据
            list_widget.load_violations(self.test_violations)
            
            # 连接确认信号
            list_widget.confirm_requested.connect(self.handle_confirm_request)
            
            # 显示列表
            list_widget.show()
            list_widget.resize(800, 400)
            
            print("✓ 违例列表集成测试成功")
            
        except Exception as e:
            print(f"✗ 违例列表集成测试失败: {e}")
            QMessageBox.critical(self, "错误", f"违例列表集成测试失败:\n{str(e)}")
    
    def test_confirmation_workflow(self):
        """测试完整确认流程"""
        print("测试完整确认流程...")
        
        try:
            # 选择一个待确认的违例
            pending_violation = None
            for violation in self.test_violations:
                if violation.status == ViolationStatus.PENDING:
                    pending_violation = violation
                    break
            
            if not pending_violation:
                QMessageBox.information(self, "提示", "没有找到待确认的违例")
                return
            
            # 创建确认对话框
            dialog = ConfirmationDialog(pending_violation, self)
            
            # 连接信号
            dialog.violation_confirmed.connect(self.handle_violation_confirmed)
            dialog.confirmation_cancelled.connect(self.handle_confirmation_cancelled)
            
            # 显示对话框
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                print("✓ 完整确认流程测试成功")
            else:
                print("- 用户取消了确认流程")
            
        except Exception as e:
            print(f"✗ 完整确认流程测试失败: {e}")
            QMessageBox.critical(self, "错误", f"完整确认流程测试失败:\n{str(e)}")
    
    def test_batch_confirmation(self):
        """测试批量确认"""
        print("测试批量确认...")
        
        try:
            # 创建主对话框
            dialog = TimingViolationDialog(self)
            
            # 加载测试违例数据
            dialog.load_violations_to_list(self.test_violations.copy())
            
            # 执行批量确认
            dialog.confirm_all_pending_violations(
                confirmer="批量测试用户",
                result_type="无问题",
                reason="批量确认测试理由"
            )
            
            # 检查结果
            stats = dialog.get_confirmation_statistics()
            print(f"批量确认后统计: {stats}")
            
            print("✓ 批量确认测试成功")
            
        except Exception as e:
            print(f"✗ 批量确认测试失败: {e}")
            QMessageBox.critical(self, "错误", f"批量确认测试失败:\n{str(e)}")
    
    def test_statistics(self):
        """测试统计信息"""
        print("测试统计信息...")
        
        try:
            # 创建主对话框
            dialog = TimingViolationDialog(self)
            
            # 加载测试违例数据
            dialog.load_violations_to_list(self.test_violations)
            
            # 获取统计信息
            stats = dialog.get_confirmation_statistics()
            
            # 显示统计信息
            stats_text = f"""确认统计信息:
            
总违例数: {stats['total']}
待确认: {stats['pending']}
已确认-无问题: {stats['confirmed_ok']}
已确认-有问题: {stats['confirmed_issue']}
自动确认: {stats['auto_confirmed']}
手动确认: {stats['manual_confirmed']}"""
            
            QMessageBox.information(self, "统计信息", stats_text)
            
            print("✓ 统计信息测试成功")
            print(f"统计结果: {stats}")
            
        except Exception as e:
            print(f"✗ 统计信息测试失败: {e}")
            QMessageBox.critical(self, "错误", f"统计信息测试失败:\n{str(e)}")
    
    def handle_confirm_request(self, violation: ViolationData):
        """处理确认请求"""
        print(f"收到确认请求: NUM={violation.num}")
        
        # 创建确认对话框
        dialog = ConfirmationDialog(violation, self)
        
        # 连接信号
        dialog.violation_confirmed.connect(self.handle_violation_confirmed)
        dialog.confirmation_cancelled.connect(self.handle_confirmation_cancelled)
        
        # 显示对话框
        dialog.exec_()
    
    def handle_violation_confirmed(self, violation: ViolationData):
        """处理违例确认完成"""
        print(f"违例确认完成: NUM={violation.num}, 状态={violation.get_status_display()}")
        
        QMessageBox.information(
            self,
            "确认完成",
            f"违例 NUM {violation.num} 确认完成\n"
            f"确认人: {violation.confirmer}\n"
            f"状态: {violation.get_status_display()}"
        )
    
    def handle_confirmation_cancelled(self):
        """处理确认取消"""
        print("确认被取消")


def test_confirmation_logic():
    """测试确认逻辑"""
    print("=== 确认逻辑测试 ===")
    
    violations = create_test_violations()
    
    # 测试确认前状态
    pending_count = sum(1 for v in violations if v.status == ViolationStatus.PENDING)
    print(f"确认前待确认违例数: {pending_count}")
    
    # 确认第一个违例
    if violations:
        violation = violations[0]
        if violation.status == ViolationStatus.PENDING:
            violation.set_manual_confirmation("测试用户", ConfirmationResult.NO_ISSUE, "测试理由")
            print(f"违例 {violation.num} 确认完成: {violation.get_status_display()}")
    
    # 测试确认后状态
    pending_count = sum(1 for v in violations if v.status == ViolationStatus.PENDING)
    confirmed_count = sum(1 for v in violations if v.is_confirmed())
    print(f"确认后待确认违例数: {pending_count}")
    print(f"确认后已确认违例数: {confirmed_count}")
    
    print("✓ 确认逻辑测试通过")
    print()


def test_data_validation():
    """测试数据验证"""
    print("=== 数据验证测试 ===")
    
    violation = create_test_violations()[0]
    
    # 测试有效确认数据
    try:
        violation.set_manual_confirmation("有效用户", ConfirmationResult.NO_ISSUE, "有效理由")
        print("✓ 有效确认数据验证通过")
    except Exception as e:
        print(f"✗ 有效确认数据验证失败: {e}")
    
    # 测试无效确认数据
    test_cases = [
        ("", ConfirmationResult.NO_ISSUE, "理由", "空确认人"),
        ("用户", ConfirmationResult.NO_ISSUE, "", "空理由"),
        ("  ", ConfirmationResult.NO_ISSUE, "理由", "空白确认人"),
        ("用户", ConfirmationResult.NO_ISSUE, "  ", "空白理由"),
    ]
    
    for confirmer, result, reason, desc in test_cases:
        try:
            # 重置违例状态
            violation.status = ViolationStatus.PENDING
            violation.confirmer = ""
            violation.reason = ""
            violation.confirmation_result = None
            
            violation.set_manual_confirmation(confirmer, result, reason)
            print(f"✗ {desc}验证失败")
        except ValueError:
            print(f"✓ {desc}验证通过")
        except Exception as e:
            print(f"? {desc}验证异常: {e}")
    
    print()


def main():
    """主函数"""
    print("确认对话框集成测试程序")
    print("=" * 50)
    
    # 运行基本测试
    test_confirmation_logic()
    test_data_validation()
    
    # 创建GUI应用
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestIntegrationWindow()
    window.show()
    
    print("GUI集成测试窗口已启动")
    print("请点击按钮测试不同的集成功能")
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()