# Requirements Document

## Introduction

本文档定义了后仿时序违例确认插件的功能需求。该插件用于解析仿真用例的时序违例日志文件(vio_summary.log)，生成可交互的检查清单，支持用户逐一确认时序违例，并提供导出功能。插件需要集成到现有的runsim GUI系统中。

## Requirements

### Requirement 1

**User Story:** 作为仿真工程师，我希望能够选择时序违例日志文件，以便开始时序违例确认流程

#### Acceptance Criteria

1. WHEN 用户启动时序违例确认插件 THEN 系统 SHALL 提供文件选择界面
2. WHEN 用户选择vio_summary.log文件 THEN 系统 SHALL 验证文件路径格式为{用例仿真目录}/log/vio_summary.log
3. WHEN 文件路径不符合预期格式 THEN 系统 SHALL 显示错误提示信息
4. WHEN 文件不存在或无法读取 THEN 系统 SHALL 显示相应的错误信息

### Requirement 2

**User Story:** 作为仿真工程师，我希望系统能够解析vio_summary.log文件，以便生成结构化的时序违例信息

#### Acceptance Criteria

1. WHEN 系统读取vio_summary.log文件 THEN 系统 SHALL 按照"----"分隔符解析每个时序违例条目
2. WHEN 解析每个时序违例条目 THEN 系统 SHALL 提取NUM、Hier、Time、Check字段信息
3. WHEN 解析Time字段 THEN 系统 SHALL 识别时间单位(FS/PS/NS)并转换为统一单位
4. WHEN 解析失败 THEN 系统 SHALL 记录错误信息并继续处理其他条目
5. WHEN 解析完成 THEN 系统 SHALL 生成结构化的时序违例数据列表

### Requirement 3

**User Story:** 作为仿真工程师，我希望系统能够识别用例名称和corner信息，以便在界面上正确显示

#### Acceptance Criteria

1. WHEN 系统解析文件路径 THEN 系统 SHALL 从目录名提取case_name和corner信息
2. WHEN 目录格式为{case_name}_{corner} THEN 系统 SHALL 自动识别并显示两个字段
3. WHEN 目录格式仅为{case_name} THEN 系统 SHALL 提供corner下拉选择框
4. WHEN 提供corner选择 THEN 系统 SHALL 包含所有合法corner选项(npg_f1_ssg到npg_f3_tt)
5. WHEN 用户未选择corner THEN 系统 SHALL 提示用户完成选择

### Requirement 4

**User Story:** 作为仿真工程师，我希望能够设置复位时间，以便自动标记复位期间的时序违例

#### Acceptance Criteria

1. WHEN 用户进入确认界面 THEN 系统 SHALL 提供复位时间输入框
2. WHEN 用户输入复位时间 THEN 系统 SHALL 验证输入格式和数值有效性
3. WHEN 复位时间设置完成 THEN 系统 SHALL 自动标记发生时间小于复位时间的违例为"已确认"
4. WHEN 自动标记违例 THEN 系统 SHALL 设置备注为"复位期间时序违例，可以忽略"
5. WHEN 复位时间更改 THEN 系统 SHALL 重新计算并更新自动确认状态

### Requirement 5

**User Story:** 作为仿真工程师，我希望能够逐一确认时序违例，以便记录确认结果和理由

#### Acceptance Criteria

1. WHEN 显示时序违例列表 THEN 系统 SHALL 为每个未自动确认的违例提供确认操作
2. WHEN 用户点击确认按钮 THEN 系统 SHALL 弹出确认对话框
3. WHEN 确认对话框打开 THEN 系统 SHALL 要求输入确认人和确认理由
4. WHEN 用户选择"无问题" THEN 系统 SHALL 要求提供无问题的具体理由
5. WHEN 用户选择"有问题" THEN 系统 SHALL 要求提供具体解决方案
6. WHEN 确认信息完整 THEN 系统 SHALL 保存确认结果并更新列表状态
7. WHEN 确认信息不完整 THEN 系统 SHALL 提示用户补充必要信息

### Requirement 6

**User Story:** 作为仿真工程师，我希望能够查看时序违例的详细信息，以便做出准确的确认决策

#### Acceptance Criteria

1. WHEN 显示时序违例列表 THEN 系统 SHALL 显示NUM、Hier、Time、Check等关键信息
2. WHEN 用户点击违例条目 THEN 系统 SHALL 显示完整的违例详情
3. WHEN 显示时间信息 THEN 系统 SHALL 同时显示原始时间和转换后的统一单位时间
4. WHEN 显示确认状态 THEN 系统 SHALL 用不同颜色或图标区分已确认和未确认状态
5. WHEN 显示自动确认项 THEN 系统 SHALL 明确标识为自动确认并显示相应备注

### Requirement 7

**User Story:** 作为仿真工程师，我希望能够导出确认结果，以便生成报告和存档

#### Acceptance Criteria

1. WHEN 用户点击导出按钮 THEN 系统 SHALL 提供导出格式选择(Excel/CSV)
2. WHEN 执行导出操作 THEN 系统 SHALL 包含所有时序违例信息、确认结果、确认人、确认备注
3. WHEN 导出Excel格式 THEN 系统 SHALL 提供良好的表格格式和列标题
4. WHEN 导出完成 THEN 系统 SHALL 提示用户导出成功并显示文件保存位置
5. WHEN 导出失败 THEN 系统 SHALL 显示具体错误信息

### Requirement 8

**User Story:** 作为仿真工程师，我希望插件能够集成到现有GUI系统中，以便统一的用户体验

#### Acceptance Criteria

1. WHEN 插件启动 THEN 系统 SHALL 遵循现有GUI的设计风格和交互模式
2. WHEN 插件运行 THEN 系统 SHALL 与现有插件管理系统兼容
3. WHEN 处理大量数据 THEN 系统 SHALL 提供进度指示和响应式界面
4. WHEN 发生错误 THEN 系统 SHALL 使用统一的错误处理和日志记录机制
5. WHEN 插件关闭 THEN 系统 SHALL 正确清理资源和保存状态