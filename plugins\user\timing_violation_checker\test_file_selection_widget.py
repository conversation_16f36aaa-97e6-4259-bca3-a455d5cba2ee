"""
文件选择组件测试

测试文件选择组件的功能，包括文件路径验证、
用例信息提取和corner选择功能。
"""

import sys
import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

try:
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtTest import QTest
    from PyQt5.QtCore import Qt
    PYQT5_AVAILABLE = True
except ImportError:
    PYQT5_AVAILABLE = False

if PYQT5_AVAILABLE:
    from plugins.user.timing_violation_checker.file_selection_widget import FileSelectionWidget
    from plugins.user.timing_violation_checker.models import CaseInfo
    from plugins.user.timing_violation_checker.utils import CaseInfoExtractor


@unittest.skipUnless(PYQT5_AVAILABLE, "PyQt5 not available")
class TestFileSelectionWidget(unittest.TestCase):
    """文件选择组件测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.widget = FileSelectionWidget()
        
        # 创建临时测试文件
        self.temp_dir = tempfile.mkdtemp()
        self.test_case_dir = os.path.join(self.temp_dir, "test_case_npg_f1_ssg")
        self.test_log_dir = os.path.join(self.test_case_dir, "log")
        self.test_file_path = os.path.join(self.test_log_dir, "vio_summary.log")
        
        # 创建目录结构
        os.makedirs(self.test_log_dir, exist_ok=True)
        
        # 创建测试文件
        with open(self.test_file_path, 'w') as f:
            f.write("test violation summary content")
    
    def tearDown(self):
        """清理测试"""
        # 清理临时文件
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        
        # 清理组件
        if hasattr(self, 'widget'):
            self.widget.close()
            self.widget.deleteLater()
    
    def test_initial_state(self):
        """测试初始状态"""
        # 检查初始状态
        self.assertEqual(self.widget.get_current_file_path(), "")
        self.assertIsNone(self.widget.get_current_case_info())
        self.assertFalse(self.widget.is_file_selected())
        self.assertFalse(self.widget.is_case_info_complete())
        
        # 检查UI元素
        self.assertEqual(self.widget.path_input.text(), "")
        self.assertEqual(self.widget.case_name_display.text(), "未选择文件")
        self.assertTrue(self.widget.browse_button.isEnabled())
    
    def test_set_valid_file_path_with_corner(self):
        """测试设置有效的包含corner的文件路径"""
        # 设置文件路径
        self.widget.set_file_path(self.test_file_path)
        
        # 检查文件路径设置
        self.assertEqual(self.widget.get_current_file_path(), self.test_file_path)
        self.assertEqual(self.widget.path_input.text(), self.test_file_path)
        
        # 检查用例信息提取
        case_info = self.widget.get_current_case_info()
        self.assertIsNotNone(case_info)
        self.assertEqual(case_info.case_name, "test_case")
        self.assertEqual(case_info.corner, "npg_f1_ssg")
        
        # 检查状态
        self.assertTrue(self.widget.is_file_selected())
        self.assertTrue(self.widget.is_case_info_complete())
    
    def test_set_valid_file_path_without_corner(self):
        """测试设置有效的不包含corner的文件路径"""
        # 创建不包含corner的测试文件
        no_corner_case_dir = os.path.join(self.temp_dir, "test_case_only")
        no_corner_log_dir = os.path.join(no_corner_case_dir, "log")
        no_corner_file_path = os.path.join(no_corner_log_dir, "vio_summary.log")
        
        os.makedirs(no_corner_log_dir, exist_ok=True)
        with open(no_corner_file_path, 'w') as f:
            f.write("test content")
        
        # 设置文件路径
        self.widget.set_file_path(no_corner_file_path)
        
        # 检查文件路径设置
        self.assertEqual(self.widget.get_current_file_path(), no_corner_file_path)
        
        # 检查用例信息提取
        case_info = self.widget.get_current_case_info()
        self.assertIsNotNone(case_info)
        self.assertEqual(case_info.case_name, "test_case_only")
        self.assertEqual(case_info.corner, "")
        
        # 检查状态
        self.assertTrue(self.widget.is_file_selected())
        self.assertFalse(self.widget.is_case_info_complete())  # 需要选择corner
        
        # 检查是否显示corner选择组件
        self.assertIsNotNone(self.widget.corner_selection_widget)
    
    def test_set_invalid_file_path(self):
        """测试设置无效的文件路径"""
        invalid_path = "/invalid/path/to/file.log"
        
        # 设置无效路径
        self.widget.set_file_path(invalid_path)
        
        # 检查状态
        self.assertEqual(self.widget.get_current_file_path(), invalid_path)
        self.assertIsNone(self.widget.get_current_case_info())
        self.assertTrue(self.widget.is_file_selected())  # 路径已设置
        self.assertFalse(self.widget.is_case_info_complete())  # 但信息不完整
    
    def test_clear_file_selection(self):
        """测试清除文件选择"""
        # 先设置文件
        self.widget.set_file_path(self.test_file_path)
        self.assertTrue(self.widget.is_file_selected())
        
        # 清除选择
        self.widget.clear_file_selection()
        
        # 检查清除结果
        self.assertEqual(self.widget.get_current_file_path(), "")
        self.assertIsNone(self.widget.get_current_case_info())
        self.assertFalse(self.widget.is_file_selected())
        self.assertFalse(self.widget.is_case_info_complete())
        
        # 检查UI重置
        self.assertEqual(self.widget.path_input.text(), "")
        self.assertEqual(self.widget.case_name_display.text(), "未选择文件")
    
    def test_corner_selection(self):
        """测试corner选择功能"""
        # 创建不包含corner的测试文件
        no_corner_case_dir = os.path.join(self.temp_dir, "test_case_only")
        no_corner_log_dir = os.path.join(no_corner_case_dir, "log")
        no_corner_file_path = os.path.join(no_corner_log_dir, "vio_summary.log")
        
        os.makedirs(no_corner_log_dir, exist_ok=True)
        with open(no_corner_file_path, 'w') as f:
            f.write("test content")
        
        # 设置文件路径
        self.widget.set_file_path(no_corner_file_path)
        
        # 检查corner选择组件是否显示
        self.assertIsNotNone(self.widget.corner_selection_widget)
        
        # 模拟corner选择
        test_corner = "npg_f1_ssg"
        self.widget.on_corner_selected(test_corner)
        
        # 检查corner选择结果
        case_info = self.widget.get_current_case_info()
        self.assertIsNotNone(case_info)
        self.assertEqual(case_info.corner, test_corner)
        self.assertTrue(self.widget.is_case_info_complete())
    
    def test_validation_status(self):
        """测试验证状态"""
        # 初始状态
        status = self.widget.get_validation_status()
        self.assertFalse(status['file_selected'])
        self.assertFalse(status['case_info_complete'])
        self.assertEqual(status['file_path'], "")
        self.assertIsNone(status['case_info'])
        self.assertFalse(status['needs_corner_selection'])
        
        # 设置有效文件后
        self.widget.set_file_path(self.test_file_path)
        status = self.widget.get_validation_status()
        self.assertTrue(status['file_selected'])
        self.assertTrue(status['case_info_complete'])
        self.assertEqual(status['file_path'], self.test_file_path)
        self.assertIsNotNone(status['case_info'])
        self.assertFalse(status['needs_corner_selection'])
    
    def test_validate_current_selection(self):
        """测试当前选择验证"""
        # 初始状态验证
        is_valid, error_msg = self.widget.validate_current_selection()
        self.assertFalse(is_valid)
        self.assertIn("请选择", error_msg)
        
        # 设置有效文件后验证
        self.widget.set_file_path(self.test_file_path)
        is_valid, error_msg = self.widget.validate_current_selection()
        self.assertTrue(is_valid)
        self.assertEqual(error_msg, "")
    
    def test_signals(self):
        """测试信号发送"""
        # 创建信号接收器
        file_selected_received = []
        case_info_received = []
        corner_required_received = []
        validation_error_received = []
        
        def on_file_selected(path):
            file_selected_received.append(path)
        
        def on_case_info_extracted(case_info):
            case_info_received.append(case_info)
        
        def on_corner_selection_required(case_name):
            corner_required_received.append(case_name)
        
        def on_validation_error(error):
            validation_error_received.append(error)
        
        # 连接信号
        self.widget.file_selected.connect(on_file_selected)
        self.widget.case_info_extracted.connect(on_case_info_extracted)
        self.widget.corner_selection_required.connect(on_corner_selection_required)
        self.widget.validation_error.connect(on_validation_error)
        
        # 测试有效文件信号
        self.widget.set_file_path(self.test_file_path)
        
        # 检查信号发送（对于包含corner的文件，应该发送file_selected和case_info_extracted信号）
        self.assertEqual(len(file_selected_received), 1)
        self.assertEqual(file_selected_received[0], self.test_file_path)
        self.assertEqual(len(case_info_received), 1)
        self.assertEqual(case_info_received[0].case_name, "test_case")
        
        # 验证错误信号的测试在其他测试中已经覆盖
        # 这里主要测试正常流程的信号发送
    
    def test_enable_disable(self):
        """测试启用/禁用功能"""
        # 初始状态应该是启用的
        self.assertTrue(self.widget.browse_button.isEnabled())
        
        # 禁用组件
        self.widget.set_enabled(False)
        self.assertFalse(self.widget.browse_button.isEnabled())
        
        # 重新启用
        self.widget.set_enabled(True)
        self.assertTrue(self.widget.browse_button.isEnabled())
    
    def test_reset(self):
        """测试重置功能"""
        # 设置一些数据
        self.widget.set_file_path(self.test_file_path)
        self.assertTrue(self.widget.is_file_selected())
        
        # 重置
        self.widget.reset()
        
        # 检查重置结果
        self.assertFalse(self.widget.is_file_selected())
        self.assertEqual(self.widget.get_current_file_path(), "")
        self.assertIsNone(self.widget.get_current_case_info())
    
    @patch('plugins.user.timing_violation_checker.file_selection_widget.QFileDialog')
    def test_browse_file_dialog(self, mock_dialog):
        """测试浏览文件对话框"""
        # 模拟文件对话框返回
        mock_instance = MagicMock()
        mock_dialog.return_value = mock_instance
        mock_instance.exec_.return_value = mock_dialog.Accepted
        mock_instance.selectedFiles.return_value = [self.test_file_path]
        
        # 触发浏览按钮点击
        self.widget.browse_file()
        
        # 检查文件对话框是否被调用
        mock_dialog.assert_called_once()
        
        # 检查文件路径是否被设置
        self.assertEqual(self.widget.get_current_file_path(), self.test_file_path)


class TestFileSelectionIntegration(unittest.TestCase):
    """文件选择组件集成测试"""
    
    def test_case_info_extractor_integration(self):
        """测试与CaseInfoExtractor的集成"""
        # 创建临时测试文件
        temp_dir = tempfile.mkdtemp()
        try:
            test_case_dir = os.path.join(temp_dir, "integration_test_npg_f2_tt")
            test_log_dir = os.path.join(test_case_dir, "log")
            test_file_path = os.path.join(test_log_dir, "vio_summary.log")
            
            os.makedirs(test_log_dir, exist_ok=True)
            with open(test_file_path, 'w') as f:
                f.write("integration test content")
            
            # 测试CaseInfoExtractor直接调用
            case_info = CaseInfoExtractor.extract_from_path(test_file_path)
            self.assertEqual(case_info.case_name, "integration_test")
            self.assertEqual(case_info.corner, "npg_f2_tt")
            
            # 测试通过FileSelectionWidget使用
            if PYQT5_AVAILABLE:
                if not QApplication.instance():
                    app = QApplication([])
                
                widget = FileSelectionWidget()
                widget.set_file_path(test_file_path)
                
                extracted_info = widget.get_current_case_info()
                self.assertIsNotNone(extracted_info)
                self.assertEqual(extracted_info.case_name, "integration_test")
                self.assertEqual(extracted_info.corner, "npg_f2_tt")
                
                widget.close()
                widget.deleteLater()
        
        finally:
            # 清理临时文件
            import shutil
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)