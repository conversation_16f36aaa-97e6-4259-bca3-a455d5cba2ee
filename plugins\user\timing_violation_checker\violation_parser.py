"""
时序违例日志文件解析器

用于解析vio_summary.log文件，提取时序违例信息并生成结构化数据。
"""

import os
import re
from typing import List, Dict, Optional, Tuple

try:
    # 尝试相对导入（在插件环境中）
    from .models import ViolationData, ViolationStatus
    from .exceptions import FileParseError, TimeConversionError
    from .utils import TimeConverter
except ImportError:
    # 如果相对导入失败，尝试直接导入（在测试环境中）
    from models import ViolationData, ViolationStatus
    from exceptions import FileParseError, TimeConversionError
    from utils import TimeConverter


class ViolationParser:
    """时序违例日志文件解析器
    
    负责解析vio_summary.log文件，按照"----"分隔符解析违例块，
    提取NUM、Hier、Time、Check等字段信息。
    """
    
    # 违例块分隔符
    VIOLATION_SEPARATOR = "----"
    
    # 字段提取正则表达式
    FIELD_PATTERNS = {
        'NUM': re.compile(r'NUM\s*:\s*(\d+)', re.IGNORECASE),
        'Hier': re.compile(r'Hier\s*:\s*(.+)', re.IGNORECASE),
        'Time': re.compile(r'Time\s*:\s*(.+)', re.IGNORECASE),
        'Check': re.compile(r'Check\s*:\s*(.+)', re.IGNORECASE),
    }
    
    def __init__(self):
        """初始化解析器"""
        self.parse_errors = []  # 存储解析过程中的错误
        self.warnings = []      # 存储解析过程中的警告
    
    def parse_file(self, file_path: str, progress_callback=None) -> List[ViolationData]:
        """解析vio_summary.log文件
        
        Args:
            file_path: 文件路径
            progress_callback: 进度回调函数，接受(current, total, message)参数
            
        Returns:
            List[ViolationData]: 解析出的时序违例数据列表
            
        Raises:
            FileParseError: 当文件不存在、无法读取或格式错误时
        """
        # 清空之前的错误和警告
        self.parse_errors.clear()
        self.warnings.clear()
        
        # 验证文件路径格式
        is_valid, error_msg = self.validate_file_path(file_path)
        if not is_valid:
            raise FileParseError(file_path, f"文件路径格式错误: {error_msg}")
        
        # 验证文件存在性和可读性
        try:
            self._validate_file_accessibility(file_path)
        except FileParseError:
            raise
        except Exception as e:
            raise FileParseError(file_path, f"文件验证失败: {str(e)}")
        
        # 读取文件内容
        try:
            content = self._read_file_content(file_path)
        except FileParseError:
            raise
        except Exception as e:
            raise FileParseError(file_path, f"读取文件时发生未知错误: {str(e)}")
        
        # 检查文件内容有效性
        if not content.strip():
            raise FileParseError(file_path, "文件内容为空")
        
        # 按分隔符分割违例块
        violation_blocks = self._split_violation_blocks(content)
        
        if not violation_blocks:
            raise FileParseError(file_path, "未找到有效的时序违例块，请检查文件格式是否正确")
        
        # 解析每个违例块
        violations = []
        successful_count = 0
        total_blocks = len(violation_blocks)
        
        for i, block in enumerate(violation_blocks):
            try:
                # 更新进度
                if progress_callback:
                    current_progress = int((i / total_blocks) * 80) + 20  # 20-100%的进度范围
                    message = f"正在解析第 {i + 1}/{total_blocks} 个违例块..."
                    if not progress_callback(current_progress, 100, message):
                        # 用户取消了操作
                        raise FileParseError(file_path, "用户取消了解析操作")
                
                violation = self._parse_violation_block(block, i + 1)
                if violation:
                    violations.append(violation)
                    successful_count += 1
            except Exception as e:
                error_msg = f"解析第 {i + 1} 个违例块时发生错误: {str(e)}"
                self.parse_errors.append({
                    'block_index': i + 1,
                    'error': str(e),
                    'block_preview': block[:100] + '...' if len(block) > 100 else block
                })
                # 继续处理其他块，不中断整个解析过程
                continue
        
        # 验证解析结果
        self._validate_parse_results(violations, len(violation_blocks), file_path)
        
        # 记录解析统计信息
        if self.parse_errors:
            warning_msg = f"解析完成：成功 {successful_count} 个，失败 {len(self.parse_errors)} 个违例块"
            self.warnings.append(warning_msg)
        
        return violations
    
    def _split_violation_blocks(self, content: str) -> List[str]:
        """按分隔符分割违例块
        
        Args:
            content: 文件内容
            
        Returns:
            List[str]: 违例块列表
        """
        # 按分隔符分割
        blocks = content.split(self.VIOLATION_SEPARATOR)
        
        # 过滤空块和只包含空白字符的块
        valid_blocks = []
        for block in blocks:
            block = block.strip()
            if block:
                valid_blocks.append(block)
        
        return valid_blocks
    
    def _parse_violation_block(self, block: str, block_index: int) -> Optional[ViolationData]:
        """解析单个违例块
        
        Args:
            block: 违例块内容
            block_index: 块索引（用于错误报告）
            
        Returns:
            Optional[ViolationData]: 解析出的违例数据，解析失败时返回None
            
        Raises:
            Exception: 当解析过程中发生错误时
        """
        if not block or not block.strip():
            raise ValueError(f"违例块 {block_index} 内容为空")
        
        # 提取各个字段
        fields = {}
        missing_fields = []
        
        for field_name, pattern in self.FIELD_PATTERNS.items():
            try:
                value = self._extract_field(block, field_name, pattern)
                if value is None:
                    missing_fields.append(field_name)
                else:
                    fields[field_name] = value
            except Exception as e:
                raise ValueError(f"违例块 {block_index} 提取字段 {field_name} 时发生错误: {str(e)}")
        
        # 检查缺失的必需字段
        if missing_fields:
            raise ValueError(f"违例块 {block_index} 缺少必需字段: {', '.join(missing_fields)}")
        
        # 验证和转换数据
        try:
            # 验证NUM字段
            try:
                num = int(fields['NUM'])
                if num <= 0:
                    raise ValueError(f"NUM字段必须为正整数，当前值: {fields['NUM']}")
            except ValueError as e:
                if "invalid literal" in str(e):
                    raise ValueError(f"NUM字段格式错误，必须为整数: {fields['NUM']}")
                else:
                    raise ValueError(f"NUM字段验证失败: {str(e)}")
            
            # 验证Hier字段
            hier = fields['Hier'].strip()
            if not hier:
                raise ValueError("Hier字段不能为空")
            
            # 验证和转换Time字段
            time_original = fields['Time'].strip()
            if not time_original:
                raise ValueError("Time字段不能为空")
            
            # 尝试转换时间
            time_fs = 0
            time_conversion_failed = False
            try:
                time_fs = TimeConverter.to_femtoseconds(time_original)
            except TimeConversionError as e:
                # 时间转换失败时记录警告，但不中断解析
                warning_msg = f"违例块 {block_index} 时间转换失败: {e.get_user_message()}，将使用0作为默认值"
                self.warnings.append(warning_msg)
                time_conversion_failed = True
            except Exception as e:
                warning_msg = f"违例块 {block_index} 时间转换发生未知错误: {str(e)}，将使用0作为默认值"
                self.warnings.append(warning_msg)
                time_conversion_failed = True
            
            # 验证Check字段
            check = fields['Check'].strip()
            if not check:
                raise ValueError("Check字段不能为空")
            
            # 创建ViolationData对象
            try:
                violation = ViolationData(
                    num=num,
                    hier=hier,
                    time_original=time_original,
                    time_fs=time_fs,
                    check=check,
                    status=ViolationStatus.PENDING,
                    confirmer="",
                    reason="",
                    auto_confirmed=False
                )
                
                # 如果时间转换失败，添加额外的标记
                if time_conversion_failed:
                    violation.reason = f"[时间转换失败] {violation.reason}".strip()
                
                return violation
                
            except Exception as e:
                raise ValueError(f"创建ViolationData对象失败: {str(e)}")
            
        except ValueError as e:
            # 重新抛出ValueError，添加块索引信息
            raise ValueError(f"违例块 {block_index} 数据验证失败: {str(e)}")
        except Exception as e:
            raise ValueError(f"违例块 {block_index} 数据处理失败: {str(e)}")
    
    def _extract_field(self, block: str, field_name: str, pattern: re.Pattern) -> Optional[str]:
        """从违例块中提取指定字段的值
        
        Args:
            block: 违例块内容
            field_name: 字段名称
            pattern: 提取模式
            
        Returns:
            Optional[str]: 提取的字段值，未找到时返回None
        """
        try:
            # 尝试多行匹配
            matches = pattern.findall(block)
            if matches:
                # 如果有多个匹配，取第一个非空的
                for match in matches:
                    if isinstance(match, tuple):
                        value = match[0].strip() if match[0] else None
                    else:
                        value = match.strip() if match else None
                    
                    if value:
                        return value
            
            # 如果没有找到匹配，尝试更宽松的匹配
            if field_name.upper() in block.upper():
                # 尝试按行分割查找
                lines = block.split('\n')
                for line in lines:
                    line = line.strip()
                    if field_name.upper() in line.upper():
                        # 尝试提取冒号后的内容
                        if ':' in line:
                            parts = line.split(':', 1)
                            if len(parts) == 2:
                                value = parts[1].strip()
                                if value:
                                    return value
                        
                        # 尝试提取等号后的内容
                        if '=' in line:
                            parts = line.split('=', 1)
                            if len(parts) == 2:
                                value = parts[1].strip()
                                if value:
                                    return value
            
            return None
            
        except Exception as e:
            # 记录提取错误但不中断处理
            warning_msg = f"提取字段 {field_name} 时发生错误: {str(e)}"
            self.warnings.append(warning_msg)
            return None
    
    def get_parse_errors(self) -> List[str]:
        """获取解析过程中的错误列表
        
        Returns:
            List[str]: 错误信息列表
        """
        return self.parse_errors.copy()
    
    def get_warnings(self) -> List[str]:
        """获取解析过程中的警告列表
        
        Returns:
            List[str]: 警告信息列表
        """
        return self.warnings.copy()
    
    def has_parse_errors(self) -> bool:
        """检查是否有解析错误
        
        Returns:
            bool: 是否有解析错误
        """
        return len(self.parse_errors) > 0
    
    def has_warnings(self) -> bool:
        """检查是否有警告
        
        Returns:
            bool: 是否有警告
        """
        return len(self.warnings) > 0
    
    def get_parse_summary(self) -> Dict[str, any]:
        """获取解析结果摘要
        
        Returns:
            Dict[str, any]: 解析摘要信息
        """
        return {
            'error_count': len(self.parse_errors),
            'warning_count': len(self.warnings),
            'errors': self.parse_errors.copy(),
            'warnings': self.warnings.copy()
        }
    
    @staticmethod
    def validate_file_path(file_path: str) -> Tuple[bool, str]:
        """验证文件路径格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if not file_path or not isinstance(file_path, str):
            return False, "文件路径不能为空"
        
        file_path = file_path.strip()
        if not file_path:
            return False, "文件路径不能为空"
        
        # 检查文件名是否为vio_summary.log
        filename = os.path.basename(file_path)
        if filename.lower() != 'vio_summary.log':
            return False, "文件名必须为 vio_summary.log"
        
        # 检查路径是否包含log目录
        path_parts = os.path.normpath(file_path).split(os.sep)
        if 'log' not in path_parts:
            return False, "文件路径应包含 log 目录"
        
        # 检查路径格式：应为 {用例仿真目录}/log/vio_summary.log
        if len(path_parts) < 2:
            return False, "文件路径格式不正确，应为 {用例仿真目录}/log/vio_summary.log"
        
        # 检查倒数第二个目录是否为log
        if path_parts[-2].lower() != 'log':
            return False, "vio_summary.log 文件应位于 log 目录中"
        
        return True, ""
    
    @staticmethod
    def extract_case_directory(file_path: str) -> Optional[str]:
        """从文件路径提取用例仿真目录
        
        Args:
            file_path: vio_summary.log文件路径
            
        Returns:
            Optional[str]: 用例仿真目录路径，提取失败时返回None
        """
        if not file_path:
            return None
        
        try:
            # 标准化路径
            normalized_path = os.path.normpath(file_path)
            path_parts = normalized_path.split(os.sep)
            
            # 查找log目录的位置
            log_index = -1
            for i, part in enumerate(path_parts):
                if part.lower() == 'log':
                    log_index = i
                    break
            
            if log_index == -1 or log_index == 0:
                return None
            
            # 用例仿真目录是log目录的父目录
            case_dir_parts = path_parts[:log_index]
            case_directory = os.sep.join(case_dir_parts)
            
            return case_directory
            
        except Exception:
            return None
    
    def validate_parsed_data(self, violations: List[ViolationData]) -> Tuple[bool, List[str]]:
        """验证解析出的数据
        
        Args:
            violations: 违例数据列表
            
        Returns:
            Tuple[bool, List[str]]: (是否有效, 验证错误列表)
        """
        if not violations:
            return False, ["没有解析出任何违例数据"]
        
        validation_errors = []
        
        # 检查NUM字段的唯一性
        num_set = set()
        for i, violation in enumerate(violations):
            if violation.num in num_set:
                validation_errors.append(f"第 {i+1} 个违例的NUM字段重复: {violation.num}")
            else:
                num_set.add(violation.num)
        
        # 检查必需字段
        for i, violation in enumerate(violations):
            if not violation.hier:
                validation_errors.append(f"第 {i+1} 个违例的Hier字段为空")
            
            if not violation.time_original:
                validation_errors.append(f"第 {i+1} 个违例的Time字段为空")
            
            if not violation.check:
                validation_errors.append(f"第 {i+1} 个违例的Check字段为空")
            
            if violation.time_fs < 0:
                validation_errors.append(f"第 {i+1} 个违例的时间值为负数: {violation.time_fs}")
        
        return len(validation_errors) == 0, validation_errors
    
    def _validate_file_accessibility(self, file_path: str):
        """验证文件的可访问性
        
        Args:
            file_path: 文件路径
            
        Raises:
            FileParseError: 当文件不可访问时
        """
        # 检查文件是否存在
        if not os.path.exists(file_path):
            # 提供更详细的错误信息
            parent_dir = os.path.dirname(file_path)
            if not os.path.exists(parent_dir):
                raise FileParseError(file_path, f"文件目录不存在：{parent_dir}，请检查路径是否正确")
            else:
                raise FileParseError(file_path, "文件不存在，请检查文件名是否正确")
        
        # 检查是否为文件（而不是目录）
        if not os.path.isfile(file_path):
            if os.path.isdir(file_path):
                raise FileParseError(file_path, "指定路径是一个目录，不是文件")
            else:
                raise FileParseError(file_path, "指定路径不是一个有效的文件")
        
        # 检查文件是否可读
        if not os.access(file_path, os.R_OK):
            raise FileParseError(file_path, "文件无法读取，请检查文件权限或确保文件未被其他程序占用")
        
        # 检查文件大小（避免处理过大的文件）
        try:
            file_size = os.path.getsize(file_path)
            max_size = 100 * 1024 * 1024  # 100MB
            if file_size > max_size:
                raise FileParseError(
                    file_path, 
                    f"文件过大 ({file_size / 1024 / 1024:.1f}MB)，最大支持 {max_size / 1024 / 1024}MB。请确认这是正确的vio_summary.log文件"
                )
            
            if file_size == 0:
                raise FileParseError(file_path, "文件大小为0，可能是空文件或仿真未产生时序违例")
            
            # 检查文件是否过小（可能不是有效的日志文件）
            if file_size < 10:
                warning_msg = f"文件大小很小 ({file_size} 字节)，可能不包含有效的时序违例信息"
                self.warnings.append(warning_msg)
                
        except OSError as e:
            raise FileParseError(file_path, f"无法获取文件信息: {str(e)}，请检查文件是否被占用或权限不足")
    
    def _read_file_content(self, file_path: str) -> str:
        """读取文件内容，支持多种编码格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件内容
            
        Raises:
            FileParseError: 当文件读取失败时
        """
        # 尝试多种编码格式
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                    content = f.read()
                    
                # 检查内容是否有效（不全是乱码）
                if self._is_valid_content(content):
                    return content
                    
            except UnicodeDecodeError:
                continue
            except IOError as e:
                raise FileParseError(file_path, f"文件读取失败: {str(e)}")
            except Exception as e:
                raise FileParseError(file_path, f"读取文件时发生错误: {str(e)}")
        
        # 如果所有编码都失败，使用最后一次尝试的结果
        try:
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
                warning_msg = "文件编码可能不正确，已使用UTF-8编码强制读取"
                self.warnings.append(warning_msg)
                return content
        except Exception as e:
            raise FileParseError(file_path, f"无法读取文件内容: {str(e)}")
    
    def _is_valid_content(self, content: str) -> bool:
        """检查文件内容是否有效
        
        Args:
            content: 文件内容
            
        Returns:
            bool: 内容是否有效
        """
        if not content or len(content.strip()) < 10:
            return False
        
        # 检查是否包含预期的关键字
        expected_keywords = ['NUM', 'Hier', 'Time', 'Check']
        found_keywords = sum(1 for keyword in expected_keywords if keyword in content)
        
        # 至少包含一半的关键字才认为是有效内容
        return found_keywords >= len(expected_keywords) // 2
    
    def _validate_parse_results(self, violations: List[ViolationData], total_blocks: int, file_path: str):
        """验证解析结果的有效性
        
        Args:
            violations: 解析出的违例列表
            total_blocks: 总违例块数
            file_path: 文件路径
            
        Raises:
            FileParseError: 当解析结果无效时
        """
        # 如果没有成功解析任何违例，抛出异常
        if not violations:
            if self.parse_errors:
                error_details = []
                for error in self.parse_errors[:5]:  # 只显示前5个错误
                    if isinstance(error, dict):
                        error_details.append(f"块 {error['block_index']}: {error['error']}")
                    else:
                        error_details.append(str(error))
                
                error_summary = "\n".join(error_details)
                if len(self.parse_errors) > 5:
                    error_summary += f"\n... 还有 {len(self.parse_errors) - 5} 个错误"
                
                raise FileParseError(file_path, f"未能解析出任何有效的时序违例:\n{error_summary}")
            else:
                raise FileParseError(file_path, "未能解析出任何有效的时序违例，可能文件格式不正确或文件为空")
        
        # 检查解析成功率
        success_rate = len(violations) / total_blocks if total_blocks > 0 else 0
        if success_rate < 0.5:  # 成功率低于50%
            warning_msg = f"解析成功率较低 ({success_rate:.1%})，请检查文件格式是否正确。成功: {len(violations)}/{total_blocks}"
            self.warnings.append(warning_msg)
        elif success_rate < 1.0:  # 成功率不是100%但大于50%
            info_msg = f"部分违例块解析失败，成功率: {success_rate:.1%} ({len(violations)}/{total_blocks})"
            self.warnings.append(info_msg)
        
        # 验证解析出的数据
        is_valid, validation_errors = self.validate_parsed_data(violations)
        if not is_valid:
            error_summary = "\n".join(validation_errors[:3])  # 只显示前3个验证错误
            if len(validation_errors) > 3:
                error_summary += f"\n... 还有 {len(validation_errors) - 3} 个验证错误"
            
            raise FileParseError(file_path, f"解析出的数据验证失败:\n{error_summary}")
    
    def get_detailed_parse_errors(self) -> List[Dict[str, any]]:
        """获取详细的解析错误信息
        
        Returns:
            List[Dict[str, any]]: 详细错误信息列表
        """
        return [error for error in self.parse_errors if isinstance(error, dict)]
    
    def get_simple_parse_errors(self) -> List[str]:
        """获取简单的解析错误信息
        
        Returns:
            List[str]: 简单错误信息列表
        """
        simple_errors = []
        for error in self.parse_errors:
            if isinstance(error, dict):
                simple_errors.append(f"块 {error['block_index']}: {error['error']}")
            else:
                simple_errors.append(str(error))
        return simple_errors
    
    def log_parse_results(self, violations: List[ViolationData], file_path: str):
        """记录解析结果日志
        
        Args:
            violations: 解析出的违例列表
            file_path: 文件路径
        """
        print(f"[ViolationParser] 解析文件: {file_path}")
        print(f"[ViolationParser] 成功解析 {len(violations)} 个时序违例")
        
        if self.parse_errors:
            print(f"[ViolationParser] 解析错误: {len(self.parse_errors)} 个")
            for error in self.get_simple_parse_errors()[:3]:
                print(f"[ViolationParser]   - {error}")
            if len(self.parse_errors) > 3:
                print(f"[ViolationParser]   - ... 还有 {len(self.parse_errors) - 3} 个错误")
        
        if self.warnings:
            print(f"[ViolationParser] 警告: {len(self.warnings)} 个")
            for warning in self.warnings[:3]:
                print(f"[ViolationParser]   - {warning}")
            if len(self.warnings) > 3:
                print(f"[ViolationParser]   - ... 还有 {len(self.warnings) - 3} 个警告")
    
    def create_error_report(self, file_path: str) -> str:
        """创建详细的错误报告
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 格式化的错误报告
        """
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("时序违例解析错误报告")
        report_lines.append("=" * 60)
        report_lines.append(f"文件路径: {file_path}")
        report_lines.append(f"解析时间: {self._get_current_time()}")
        report_lines.append("")
        
        # 错误统计
        report_lines.append("错误统计:")
        report_lines.append(f"  解析错误: {len(self.parse_errors)} 个")
        report_lines.append(f"  警告信息: {len(self.warnings)} 个")
        report_lines.append("")
        
        # 详细错误信息
        if self.parse_errors:
            report_lines.append("详细错误信息:")
            for i, error in enumerate(self.parse_errors, 1):
                if isinstance(error, dict):
                    report_lines.append(f"  {i}. 违例块 {error['block_index']}: {error['error']}")
                    if 'block_preview' in error:
                        preview = error['block_preview'].replace('\n', '\\n')
                        report_lines.append(f"     块内容预览: {preview}")
                else:
                    report_lines.append(f"  {i}. {error}")
            report_lines.append("")
        
        # 警告信息
        if self.warnings:
            report_lines.append("警告信息:")
            for i, warning in enumerate(self.warnings, 1):
                report_lines.append(f"  {i}. {warning}")
            report_lines.append("")
        
        # 恢复建议
        suggestions = self.get_error_recovery_suggestions()
        if suggestions:
            report_lines.append("恢复建议:")
            for i, suggestion in enumerate(suggestions, 1):
                report_lines.append(f"  {i}. {suggestion}")
            report_lines.append("")
        
        report_lines.append("=" * 60)
        
        return "\n".join(report_lines)
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def clear_errors_and_warnings(self):
        """清空错误和警告信息"""
        self.parse_errors.clear()
        self.warnings.clear()
    
    def get_error_recovery_suggestions(self) -> List[str]:
        """获取错误恢复建议
        
        Returns:
            List[str]: 错误恢复建议列表
        """
        suggestions = []
        
        if not self.parse_errors:
            return suggestions
        
        # 分析错误类型并提供相应建议
        error_types = {
            'missing_field': 0,
            'invalid_num': 0,
            'empty_field': 0,
            'time_conversion': 0,
            'format_error': 0
        }
        
        for error in self.parse_errors:
            if isinstance(error, dict):
                error_msg = error.get('error', '').lower()
                
                if '缺少必需字段' in error_msg:
                    error_types['missing_field'] += 1
                elif 'num字段' in error_msg and ('格式错误' in error_msg or '必须为整数' in error_msg):
                    error_types['invalid_num'] += 1
                elif '不能为空' in error_msg:
                    error_types['empty_field'] += 1
                elif '时间转换失败' in error_msg:
                    error_types['time_conversion'] += 1
                else:
                    error_types['format_error'] += 1
        
        # 根据错误类型提供建议
        if error_types['missing_field'] > 0:
            suggestions.append(f"发现 {error_types['missing_field']} 个违例块缺少必需字段（NUM、Hier、Time、Check），请检查文件格式是否完整")
        
        if error_types['invalid_num'] > 0:
            suggestions.append(f"发现 {error_types['invalid_num']} 个违例块的NUM字段格式错误，NUM必须为正整数")
        
        if error_types['empty_field'] > 0:
            suggestions.append(f"发现 {error_types['empty_field']} 个违例块存在空字段，请确保所有字段都有有效值")
        
        if error_types['time_conversion'] > 0:
            suggestions.append(f"发现 {error_types['time_conversion']} 个时间格式错误，支持的时间单位：FS（飞秒）、PS（皮秒）、NS（纳秒）")
        
        if error_types['format_error'] > 0:
            suggestions.append(f"发现 {error_types['format_error']} 个其他格式错误，请检查违例块是否使用'----'正确分隔")
        
        # 通用建议
        if len(self.parse_errors) > len(self.get_simple_parse_errors()) // 2:
            suggestions.append("建议检查文件编码格式，确保使用UTF-8或GBK编码")
            suggestions.append("建议检查文件是否被截断或损坏")
        
        return suggestions
    
    def get_parse_statistics(self) -> Dict[str, any]:
        """获取详细的解析统计信息
        
        Returns:
            Dict[str, any]: 解析统计信息
        """
        return {
            'total_errors': len(self.parse_errors),
            'total_warnings': len(self.warnings),
            'error_details': self.get_detailed_parse_errors(),
            'warning_list': self.warnings.copy(),
            'recovery_suggestions': self.get_error_recovery_suggestions(),
            'has_critical_errors': len(self.parse_errors) > 0,
            'has_warnings': len(self.warnings) > 0
        }