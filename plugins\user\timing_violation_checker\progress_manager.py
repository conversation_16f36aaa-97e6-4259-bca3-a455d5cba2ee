"""
时序违例确认插件进度管理器

提供进度指示器、响应式界面和性能优化功能。
"""

import time
import threading
from typing import Optional, Callable, Any, Dict
from functools import wraps

try:
    from PyQt5.QtWidgets import (QProgressDialog, QProgressBar, QLabel, QVBoxLayout, 
                                QHBoxLayout, QDialog, QPushButton, QApplication, QWidget)
    from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt, QObject
    from PyQt5.QtGui import QMovie
    PYQT5_AVAILABLE = True
except ImportError:
    PYQT5_AVAILABLE = False

try:
    # 尝试相对导入（在插件环境中）
    from .error_handler import error_logger, ErrorHandlerMixin
except ImportError:
    # 如果相对导入失败，尝试直接导入（在测试环境中）
    from error_handler import error_logger, ErrorHandlerMixin


class ProgressTracker:
    """进度跟踪器
    
    跟踪操作进度，提供进度计算和估算功能。
    """
    
    def __init__(self, total_items: int = 100):
        """初始化进度跟踪器
        
        Args:
            total_items: 总项目数量
        """
        self.total_items = total_items
        self.completed_items = 0
        self.start_time = None
        self.last_update_time = None
        self.update_interval = 0.1  # 更新间隔（秒）
    
    def start(self):
        """开始跟踪"""
        self.start_time = time.time()
        self.last_update_time = self.start_time
        self.completed_items = 0
    
    def update(self, completed_items: int = None, increment: int = 1):
        """更新进度
        
        Args:
            completed_items: 已完成项目数量（如果提供，直接设置）
            increment: 增量（如果completed_items未提供，则增加此值）
        """
        current_time = time.time()
        
        if completed_items is not None:
            self.completed_items = completed_items
        else:
            self.completed_items += increment
        
        self.last_update_time = current_time
    
    def get_progress_percentage(self) -> float:
        """获取进度百分比
        
        Returns:
            float: 进度百分比（0-100）
        """
        if self.total_items <= 0:
            return 100.0
        
        return min(100.0, (self.completed_items / self.total_items) * 100)
    
    def get_elapsed_time(self) -> float:
        """获取已用时间
        
        Returns:
            float: 已用时间（秒）
        """
        if self.start_time is None:
            return 0.0
        
        return time.time() - self.start_time
    
    def get_estimated_remaining_time(self) -> float:
        """获取预估剩余时间
        
        Returns:
            float: 预估剩余时间（秒）
        """
        if self.completed_items <= 0 or self.start_time is None:
            return 0.0
        
        elapsed_time = self.get_elapsed_time()
        remaining_items = self.total_items - self.completed_items
        
        if remaining_items <= 0:
            return 0.0
        
        time_per_item = elapsed_time / self.completed_items
        return remaining_items * time_per_item
    
    def get_speed(self) -> float:
        """获取处理速度
        
        Returns:
            float: 处理速度（项目/秒）
        """
        elapsed_time = self.get_elapsed_time()
        if elapsed_time <= 0:
            return 0.0
        
        return self.completed_items / elapsed_time
    
    def should_update_ui(self) -> bool:
        """检查是否应该更新UI
        
        Returns:
            bool: 是否应该更新UI
        """
        if self.last_update_time is None:
            return True
        
        return (time.time() - self.last_update_time) >= self.update_interval
    
    def get_status_text(self) -> str:
        """获取状态文本
        
        Returns:
            str: 状态文本
        """
        percentage = self.get_progress_percentage()
        elapsed_time = self.get_elapsed_time()
        remaining_time = self.get_estimated_remaining_time()
        speed = self.get_speed()
        
        status_parts = []
        status_parts.append(f"{self.completed_items}/{self.total_items} ({percentage:.1f}%)")
        
        if elapsed_time > 0:
            status_parts.append(f"已用时: {self._format_time(elapsed_time)}")
        
        if remaining_time > 0:
            status_parts.append(f"剩余: {self._format_time(remaining_time)}")
        
        if speed > 0:
            status_parts.append(f"速度: {speed:.1f} 项/秒")
        
        return " | ".join(status_parts)
    
    def _format_time(self, seconds: float) -> str:
        """格式化时间显示
        
        Args:
            seconds: 秒数
            
        Returns:
            str: 格式化的时间字符串
        """
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes}分{secs}秒"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}时{minutes}分"
    
    def is_complete(self) -> bool:
        """检查是否完成
        
        Returns:
            bool: 是否完成
        """
        return self.completed_items >= self.total_items
    
    def reset(self, total_items: int = None):
        """重置进度跟踪器
        
        Args:
            total_items: 新的总项目数量（可选）
        """
        if total_items is not None:
            self.total_items = total_items
        
        self.completed_items = 0
        self.start_time = None
        self.last_update_time = None


if PYQT5_AVAILABLE:
    class ProgressWorkerThread(QThread):
        """进度工作线程
        
        在后台线程中执行长时间运行的任务，并发送进度信号。
        """
        
        # 信号定义
        progress_updated = pyqtSignal(int, str)  # 进度值, 状态消息
        task_completed = pyqtSignal(object)      # 任务结果
        task_failed = pyqtSignal(Exception)      # 任务异常
        status_changed = pyqtSignal(str)         # 状态变化
        
        def __init__(self, task_func: Callable, *args, **kwargs):
            """初始化工作线程
            
            Args:
                task_func: 要执行的任务函数
                *args: 任务函数参数
                **kwargs: 任务函数关键字参数
            """
            super().__init__()
            self.task_func = task_func
            self.task_args = args
            self.task_kwargs = kwargs
            self.is_cancelled = False
            self.progress_tracker = ProgressTracker()
        
        def run(self):
            """运行任务"""
            try:
                # 开始进度跟踪
                self.progress_tracker.start()
                self.status_changed.emit("正在处理...")
                
                # 执行任务
                result = self.task_func(
                    progress_callback=self._progress_callback,
                    cancel_check=self._is_cancelled,
                    *self.task_args,
                    **self.task_kwargs
                )
                
                if not self.is_cancelled:
                    self.task_completed.emit(result)
                
            except Exception as e:
                error_logger.log_error(e, "工作线程执行任务")
                self.task_failed.emit(e)
        
        def _progress_callback(self, completed: int, total: int = None, message: str = ""):
            """进度回调函数
            
            Args:
                completed: 已完成数量
                total: 总数量（可选）
                message: 状态消息
            """
            if self.is_cancelled:
                return
            
            if total is not None:
                self.progress_tracker.total_items = total
            
            self.progress_tracker.update(completed)
            
            if self.progress_tracker.should_update_ui():
                percentage = int(self.progress_tracker.get_progress_percentage())
                status_text = message or self.progress_tracker.get_status_text()
                self.progress_updated.emit(percentage, status_text)
        
        def _is_cancelled(self) -> bool:
            """检查是否被取消
            
            Returns:
                bool: 是否被取消
            """
            return self.is_cancelled
        
        def cancel(self):
            """取消任务"""
            self.is_cancelled = True
            self.status_changed.emit("正在取消...")
    
    
    class EnhancedProgressDialog(QDialog, ErrorHandlerMixin):
        """增强的进度对话框
        
        提供更丰富的进度显示和用户交互功能。
        """
        
        def __init__(self, parent=None):
            """初始化进度对话框
            
            Args:
                parent: 父窗口
            """
            super().__init__(parent)
            ErrorHandlerMixin.__init__(self)
            
            self.setWindowTitle("处理进度")
            self.setModal(True)
            self.setMinimumSize(400, 150)
            self.setMaximumSize(600, 200)
            
            self.worker_thread = None
            self.is_cancellable = True
            
            self._setup_ui()
        
        def _setup_ui(self):
            """设置用户界面"""
            layout = QVBoxLayout(self)
            
            # 状态标签
            self.status_label = QLabel("准备开始...")
            self.status_label.setWordWrap(True)
            layout.addWidget(self.status_label)
            
            # 进度条
            self.progress_bar = QProgressBar()
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(0)
            layout.addWidget(self.progress_bar)
            
            # 详细信息标签
            self.detail_label = QLabel("")
            self.detail_label.setStyleSheet("color: #666; font-size: 10px;")
            self.detail_label.setWordWrap(True)
            layout.addWidget(self.detail_label)
            
            # 按钮区域
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            
            self.cancel_button = QPushButton("取消")
            self.cancel_button.clicked.connect(self._cancel_task)
            button_layout.addWidget(self.cancel_button)
            
            layout.addLayout(button_layout)
        
        def start_task(self, task_func: Callable, *args, **kwargs):
            """开始执行任务
            
            Args:
                task_func: 任务函数
                *args: 任务参数
                **kwargs: 任务关键字参数
            """
            if self.worker_thread and self.worker_thread.isRunning():
                self.handle_warning("任务已在运行中")
                return
            
            # 创建工作线程
            self.worker_thread = ProgressWorkerThread(task_func, *args, **kwargs)
            
            # 连接信号
            self.worker_thread.progress_updated.connect(self._on_progress_updated)
            self.worker_thread.task_completed.connect(self._on_task_completed)
            self.worker_thread.task_failed.connect(self._on_task_failed)
            self.worker_thread.status_changed.connect(self._on_status_changed)
            
            # 启动线程
            self.worker_thread.start()
            
            # 显示对话框
            self.show()
        
        def _on_progress_updated(self, percentage: int, status_text: str):
            """处理进度更新
            
            Args:
                percentage: 进度百分比
                status_text: 状态文本
            """
            self.progress_bar.setValue(percentage)
            self.detail_label.setText(status_text)
            
            # 处理界面事件
            QApplication.processEvents()
        
        def _on_task_completed(self, result):
            """处理任务完成
            
            Args:
                result: 任务结果
            """
            self.status_label.setText("任务完成")
            self.progress_bar.setValue(100)
            self.cancel_button.setText("关闭")
            
            # 自动关闭对话框
            QTimer.singleShot(1000, self.accept)
        
        def _on_task_failed(self, error: Exception):
            """处理任务失败
            
            Args:
                error: 异常对象
            """
            self.status_label.setText("任务失败")
            self.cancel_button.setText("关闭")
            
            # 显示错误
            self.handle_error(error, "任务执行失败")
        
        def _on_status_changed(self, status: str):
            """处理状态变化
            
            Args:
                status: 新状态
            """
            self.status_label.setText(status)
        
        def _cancel_task(self):
            """取消任务"""
            if self.worker_thread and self.worker_thread.isRunning():
                if self.is_cancellable:
                    self.worker_thread.cancel()
                    self.status_label.setText("正在取消...")
                    self.cancel_button.setEnabled(False)
                else:
                    self.handle_warning("当前任务无法取消，请等待完成")
            else:
                self.reject()
        
        def set_cancellable(self, cancellable: bool):
            """设置是否可取消
            
            Args:
                cancellable: 是否可取消
            """
            self.is_cancellable = cancellable
            self.cancel_button.setEnabled(cancellable)
        
        def closeEvent(self, event):
            """处理关闭事件"""
            if self.worker_thread and self.worker_thread.isRunning():
                if self.is_cancellable:
                    self.worker_thread.cancel()
                    self.worker_thread.wait(3000)  # 等待3秒
                else:
                    event.ignore()
                    return
            
            event.accept()
    
    
    class SimpleProgressDialog(QProgressDialog):
        """简单进度对话框
        
        基于QProgressDialog的简化版本，用于快速显示进度。
        """
        
        def __init__(self, title: str, message: str, parent=None):
            """初始化简单进度对话框
            
            Args:
                title: 对话框标题
                message: 进度消息
                parent: 父窗口
            """
            super().__init__(message, "取消", 0, 100, parent)
            self.setWindowTitle(title)
            self.setModal(True)
            self.setMinimumDuration(500)  # 500ms后显示
            
            # 连接取消信号
            self.canceled.connect(self._on_canceled)
            
            self.is_cancelled = False
        
        def _on_canceled(self):
            """处理取消事件"""
            self.is_cancelled = True
        
        def update_progress(self, value: int, message: str = ""):
            """更新进度
            
            Args:
                value: 进度值（0-100）
                message: 进度消息
            """
            if self.is_cancelled:
                return False
            
            self.setValue(value)
            if message:
                self.setLabelText(message)
            
            # 处理界面事件
            QApplication.processEvents()
            
            return not self.is_cancelled
        
        def is_canceled(self) -> bool:
            """检查是否被取消
            
            Returns:
                bool: 是否被取消
            """
            return self.is_cancelled


class PerformanceMonitor:
    """性能监视器
    
    监控操作性能，提供性能统计和优化建议。
    """
    
    def __init__(self):
        """初始化性能监视器"""
        self.operations = {}
        self.memory_usage = []
        self.start_time = None
    
    def start_operation(self, operation_name: str):
        """开始监控操作
        
        Args:
            operation_name: 操作名称
        """
        if operation_name not in self.operations:
            self.operations[operation_name] = {
                'count': 0,
                'total_time': 0.0,
                'min_time': float('inf'),
                'max_time': 0.0,
                'current_start': None
            }
        
        self.operations[operation_name]['current_start'] = time.time()
        
        if self.start_time is None:
            self.start_time = time.time()
    
    def end_operation(self, operation_name: str):
        """结束监控操作
        
        Args:
            operation_name: 操作名称
        """
        if operation_name not in self.operations:
            return
        
        op = self.operations[operation_name]
        if op['current_start'] is None:
            return
        
        duration = time.time() - op['current_start']
        
        op['count'] += 1
        op['total_time'] += duration
        op['min_time'] = min(op['min_time'], duration)
        op['max_time'] = max(op['max_time'], duration)
        op['current_start'] = None
    
    def get_operation_stats(self, operation_name: str) -> Dict:
        """获取操作统计信息
        
        Args:
            operation_name: 操作名称
            
        Returns:
            Dict: 统计信息
        """
        if operation_name not in self.operations:
            return {}
        
        op = self.operations[operation_name]
        if op['count'] == 0:
            return {'count': 0}
        
        avg_time = op['total_time'] / op['count']
        
        return {
            'count': op['count'],
            'total_time': op['total_time'],
            'average_time': avg_time,
            'min_time': op['min_time'],
            'max_time': op['max_time'],
            'operations_per_second': op['count'] / op['total_time'] if op['total_time'] > 0 else 0
        }
    
    def get_all_stats(self) -> Dict:
        """获取所有操作的统计信息
        
        Returns:
            Dict: 所有统计信息
        """
        stats = {}
        for op_name in self.operations:
            stats[op_name] = self.get_operation_stats(op_name)
        
        # 添加总体统计
        if self.start_time:
            stats['total_elapsed_time'] = time.time() - self.start_time
        
        return stats
    
    def get_performance_report(self) -> str:
        """获取性能报告
        
        Returns:
            str: 性能报告文本
        """
        stats = self.get_all_stats()
        
        lines = ["性能报告:"]
        lines.append("=" * 50)
        
        if 'total_elapsed_time' in stats:
            lines.append(f"总运行时间: {stats['total_elapsed_time']:.2f}秒")
            lines.append("")
        
        for op_name, op_stats in stats.items():
            if op_name == 'total_elapsed_time':
                continue
            
            if op_stats.get('count', 0) == 0:
                continue
            
            lines.append(f"操作: {op_name}")
            lines.append(f"  执行次数: {op_stats['count']}")
            lines.append(f"  总时间: {op_stats['total_time']:.3f}秒")
            lines.append(f"  平均时间: {op_stats['average_time']:.3f}秒")
            lines.append(f"  最短时间: {op_stats['min_time']:.3f}秒")
            lines.append(f"  最长时间: {op_stats['max_time']:.3f}秒")
            lines.append(f"  执行速度: {op_stats['operations_per_second']:.1f} 次/秒")
            lines.append("")
        
        return "\n".join(lines)
    
    def reset(self):
        """重置监视器"""
        self.operations.clear()
        self.memory_usage.clear()
        self.start_time = None


def monitor_performance(operation_name: str, monitor: PerformanceMonitor = None):
    """性能监控装饰器
    
    Args:
        operation_name: 操作名称
        monitor: 性能监视器实例
    """
    if monitor is None:
        monitor = PerformanceMonitor()
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            monitor.start_operation(operation_name)
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                monitor.end_operation(operation_name)
        return wrapper
    return decorator


def with_progress(title: str = "处理中", message: str = "请稍候...", 
                 cancellable: bool = True, parent=None):
    """进度显示装饰器
    
    Args:
        title: 进度对话框标题
        message: 进度消息
        cancellable: 是否可取消
        parent: 父窗口
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            if not PYQT5_AVAILABLE:
                # 如果PyQt5不可用，直接执行函数
                return func(*args, **kwargs)
            
            progress_dialog = SimpleProgressDialog(title, message, parent)
            progress_dialog.show()
            
            try:
                # 如果函数支持进度回调，传递回调函数
                if 'progress_callback' in func.__code__.co_varnames:
                    def progress_callback(value: int, msg: str = ""):
                        return progress_dialog.update_progress(value, msg or message)
                    
                    kwargs['progress_callback'] = progress_callback
                
                result = func(*args, **kwargs)
                progress_dialog.setValue(100)
                return result
                
            finally:
                progress_dialog.close()
        
        return wrapper
    return decorator


# 全局性能监视器实例
global_performance_monitor = PerformanceMonitor()


def create_progress_dialog(title: str, message: str, parent=None) -> 'SimpleProgressDialog':
    """创建进度对话框的便利函数
    
    Args:
        title: 对话框标题
        message: 进度消息
        parent: 父窗口
        
    Returns:
        SimpleProgressDialog: 进度对话框实例
    """
    if PYQT5_AVAILABLE:
        return SimpleProgressDialog(title, message, parent)
    else:
        # 返回一个简化的进度处理器
        class SimpleProgress:
            def __init__(self, title: str, message: str, parent=None):
                self.title = title
                self.message = message
                print(f"开始: {title} - {message}")
            
            def update_progress(self, value: int, message: str = ""):
                print(f"进度: {value}% - {message or self.message}")
                return True
            
            def is_canceled(self) -> bool:
                return False
            
            def close(self):
                print(f"完成: {self.title}")
        
        return SimpleProgress(title, message, parent)


def create_enhanced_progress_dialog(parent=None) -> 'EnhancedProgressDialog':
    """创建增强进度对话框的便利函数
    
    Args:
        parent: 父窗口
        
    Returns:
        EnhancedProgressDialog: 增强进度对话框实例
    """
    if PYQT5_AVAILABLE:
        return EnhancedProgressDialog(parent)
    else:
        # 返回一个简化的处理器
        class SimpleEnhancedProgress:
            def start_task(self, task_func: Callable, *args, **kwargs):
                print("开始执行任务...")
                try:
                    result = task_func(*args, **kwargs)
                    print("任务完成")
                    return result
                except Exception as e:
                    print(f"任务失败: {str(e)}")
                    raise
            
            def set_cancellable(self, cancellable: bool):
                pass
        
        return SimpleEnhancedProgress()