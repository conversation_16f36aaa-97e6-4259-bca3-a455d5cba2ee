#!/usr/bin/env python3
"""
Edge case tests for CaseInfoExtractor
"""

import sys
import os
sys.path.append(os.path.join(os.getcwd(), 'plugins', 'user', 'timing_violation_checker'))

from plugins.user.timing_violation_checker.utils import CaseInfoExtractor
from plugins.user.timing_violation_checker.models import VALID_CORNERS

def test_edge_cases():
    print("Testing CaseInfoExtractor edge cases...")
    
    test_cases = [
        # Valid cases with corners
        ('my_test_case_npg_f1_ssg/log/vio_summary.log', 'my_test_case', 'npg_f1_ssg'),
        ('simple_npg_f2_tt/log/vio_summary.log', 'simple', 'npg_f2_tt'),
        ('complex_test_case_name_npg_f3_ff/log/vio_summary.log', 'complex_test_case_name', 'npg_f3_ff'),
        
        # Valid cases without corners
        ('my_test_case/log/vio_summary.log', 'my_test_case', ''),
        ('simple/log/vio_summary.log', 'simple', ''),
        ('complex_test_case_name/log/vio_summary.log', 'complex_test_case_name', ''),
        
        # Cases with invalid corners (should treat whole name as case_name)
        ('test_case_invalid_corner/log/vio_summary.log', 'test_case_invalid_corner', ''),
        ('my_test_npg_invalid/log/vio_summary.log', 'my_test_npg_invalid', ''),
        
        # Edge case: corner name appears in middle
        ('npg_f1_ssg_test_case/log/vio_summary.log', 'npg_f1_ssg_test_case', ''),
        ('test_npg_f1_ssg_case_npg_f2_tt/log/vio_summary.log', 'test_npg_f1_ssg_case', 'npg_f2_tt'),
    ]
    
    for i, (file_path, expected_case, expected_corner) in enumerate(test_cases, 1):
        try:
            case_info = CaseInfoExtractor.extract_from_path(file_path)
            actual_case = case_info.case_name
            actual_corner = case_info.corner
            
            if actual_case == expected_case and actual_corner == expected_corner:
                print(f'✓ Test {i}: PASS - {file_path}')
                print(f'  Expected: case_name={expected_case}, corner={expected_corner}')
                print(f'  Actual:   case_name={actual_case}, corner={actual_corner}')
            else:
                print(f'✗ Test {i}: FAIL - {file_path}')
                print(f'  Expected: case_name={expected_case}, corner={expected_corner}')
                print(f'  Actual:   case_name={actual_case}, corner={actual_corner}')
        except Exception as e:
            print(f'✗ Test {i}: ERROR - {file_path}')
            print(f'  Error: {e}')
        print()
    
    # Test error cases
    print("Testing error cases...")
    error_cases = [
        ('', 'Empty path'),
        ('not_vio_summary.log', 'Wrong filename'),
        ('test_case/vio_summary.log', 'Not in log directory'),
        ('test_case/log/wrong_file.log', 'Wrong filename'),
        ('test@case/log/vio_summary.log', 'Invalid characters'),
    ]
    
    for i, (file_path, description) in enumerate(error_cases, 1):
        try:
            case_info = CaseInfoExtractor.extract_from_path(file_path)
            print(f'✗ Error Test {i}: FAIL - {description} should have failed')
            print(f'  Got: case_name={case_info.case_name}, corner={case_info.corner}')
        except Exception as e:
            print(f'✓ Error Test {i}: PASS - {description}')
            print(f'  Expected error: {e}')
        print()

if __name__ == '__main__':
    test_edge_cases()