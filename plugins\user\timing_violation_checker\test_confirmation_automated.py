#!/usr/bin/env python3
"""
自动化确认对话框测试

测试确认对话框的核心功能，不需要GUI交互。
"""

import sys
import os

# 添加项目路径到sys.path
sys.path.append(os.path.join(os.getcwd(), 'plugins', 'user', 'timing_violation_checker'))

from models import ViolationData, ViolationStatus, ConfirmationResult


def create_test_violation() -> ViolationData:
    """创建测试用的违例数据"""
    return ViolationData(
        num=1,
        hier="tb_top.cpu.core.alu",
        time_original="1523423FS",
        time_fs=1523423,
        check="setup(posedge clk, data)",
        status=ViolationStatus.PENDING
    )


def test_violation_confirmation():
    """测试违例确认功能"""
    print("=== 违例确认功能测试 ===")
    
    violation = create_test_violation()
    
    # 测试初始状态
    assert violation.status == ViolationStatus.PENDING
    assert not violation.is_confirmed()
    assert not violation.is_auto_confirmed()
    print("✓ 初始状态测试通过")
    
    # 测试手动确认 - 无问题
    violation.set_manual_confirmation("测试用户", ConfirmationResult.NO_ISSUE, "这是测试理由")
    assert violation.status == ViolationStatus.CONFIRMED_OK
    assert violation.is_confirmed()
    assert not violation.is_auto_confirmed()
    assert violation.confirmer == "测试用户"
    assert violation.confirmation_result == ConfirmationResult.NO_ISSUE
    assert violation.reason == "这是测试理由"
    print("✓ 手动确认-无问题测试通过")
    
    # 重置违例状态
    violation.status = ViolationStatus.PENDING
    violation.confirmer = ""
    violation.reason = ""
    violation.confirmation_result = None
    violation.auto_confirmed = False
    
    # 测试手动确认 - 有问题
    violation.set_manual_confirmation("测试用户2", ConfirmationResult.HAS_ISSUE, "这是解决方案")
    assert violation.status == ViolationStatus.CONFIRMED_ISSUE
    assert violation.is_confirmed()
    assert not violation.is_auto_confirmed()
    assert violation.confirmer == "测试用户2"
    assert violation.confirmation_result == ConfirmationResult.HAS_ISSUE
    assert violation.reason == "这是解决方案"
    print("✓ 手动确认-有问题测试通过")
    
    # 测试自动确认
    violation.set_auto_confirmation("复位期间违例")
    assert violation.status == ViolationStatus.AUTO_CONFIRMED
    assert violation.is_confirmed()
    assert violation.is_auto_confirmed()
    assert violation.confirmer == "系统自动"
    assert violation.confirmation_result == ConfirmationResult.NO_ISSUE
    assert violation.reason == "复位期间违例"
    print("✓ 自动确认测试通过")
    
    print()


def test_validation_logic():
    """测试验证逻辑"""
    print("=== 验证逻辑测试 ===")
    
    violation = create_test_violation()
    
    # 测试空确认人
    try:
        violation.set_manual_confirmation("", ConfirmationResult.NO_ISSUE, "理由")
        assert False, "应该抛出ValueError"
    except ValueError:
        print("✓ 空确认人验证通过")
    
    # 测试空白确认人
    try:
        violation.set_manual_confirmation("   ", ConfirmationResult.NO_ISSUE, "理由")
        assert False, "应该抛出ValueError"
    except ValueError:
        print("✓ 空白确认人验证通过")
    
    # 测试空理由
    try:
        violation.set_manual_confirmation("用户", ConfirmationResult.NO_ISSUE, "")
        assert False, "应该抛出ValueError"
    except ValueError:
        print("✓ 空理由验证通过")
    
    # 测试空白理由
    try:
        violation.set_manual_confirmation("用户", ConfirmationResult.NO_ISSUE, "   ")
        assert False, "应该抛出ValueError"
    except ValueError:
        print("✓ 空白理由验证通过")
    
    # 测试正常情况
    try:
        violation.set_manual_confirmation("正常用户", ConfirmationResult.NO_ISSUE, "正常理由")
        print("✓ 正常确认验证通过")
    except Exception as e:
        assert False, f"正常确认不应该抛出异常: {e}"
    
    print()


def test_status_transitions():
    """测试状态转换"""
    print("=== 状态转换测试 ===")
    
    violation = create_test_violation()
    
    # 测试从待确认到已确认-无问题
    assert violation.status == ViolationStatus.PENDING
    violation.set_manual_confirmation("用户1", ConfirmationResult.NO_ISSUE, "无问题理由")
    assert violation.status == ViolationStatus.CONFIRMED_OK
    print("✓ 待确认 -> 已确认-无问题")
    
    # 重置状态
    violation.status = ViolationStatus.PENDING
    violation.confirmer = ""
    violation.reason = ""
    violation.confirmation_result = None
    
    # 测试从待确认到已确认-有问题
    violation.set_manual_confirmation("用户2", ConfirmationResult.HAS_ISSUE, "有问题解决方案")
    assert violation.status == ViolationStatus.CONFIRMED_ISSUE
    print("✓ 待确认 -> 已确认-有问题")
    
    # 重置状态
    violation.status = ViolationStatus.PENDING
    violation.confirmer = ""
    violation.reason = ""
    violation.confirmation_result = None
    violation.auto_confirmed = False
    
    # 测试从待确认到自动确认
    violation.set_auto_confirmation()
    assert violation.status == ViolationStatus.AUTO_CONFIRMED
    assert violation.auto_confirmed == True
    print("✓ 待确认 -> 自动确认")
    
    print()


def test_confirmation_data_integrity():
    """测试确认数据完整性"""
    print("=== 确认数据完整性测试 ===")
    
    violation = create_test_violation()
    
    # 测试手动确认数据完整性
    confirmer = "完整性测试用户"
    result = ConfirmationResult.NO_ISSUE
    reason = "这是一个完整性测试理由，包含足够的信息来验证数据保存的正确性。"
    
    violation.set_manual_confirmation(confirmer, result, reason)
    
    # 验证数据完整性
    assert violation.confirmer == confirmer
    assert violation.confirmation_result == result
    assert violation.reason == reason
    assert not violation.auto_confirmed
    assert violation.status == ViolationStatus.CONFIRMED_OK
    print("✓ 手动确认数据完整性验证通过")
    
    # 测试自动确认数据完整性
    violation.status = ViolationStatus.PENDING
    violation.confirmer = ""
    violation.reason = ""
    violation.confirmation_result = None
    violation.auto_confirmed = False
    
    auto_reason = "自动确认测试理由"
    violation.set_auto_confirmation(auto_reason)
    
    assert violation.confirmer == "系统自动"
    assert violation.confirmation_result == ConfirmationResult.NO_ISSUE
    assert violation.reason == auto_reason
    assert violation.auto_confirmed == True
    assert violation.status == ViolationStatus.AUTO_CONFIRMED
    print("✓ 自动确认数据完整性验证通过")
    
    print()


def test_edge_cases():
    """测试边界情况"""
    print("=== 边界情况测试 ===")
    
    violation = create_test_violation()
    
    # 测试长确认人名称
    long_confirmer = "这是一个非常长的确认人名称" * 10
    violation.set_manual_confirmation(long_confirmer, ConfirmationResult.NO_ISSUE, "理由")
    assert violation.confirmer == long_confirmer
    print("✓ 长确认人名称测试通过")
    
    # 重置状态
    violation.status = ViolationStatus.PENDING
    violation.confirmer = ""
    violation.reason = ""
    violation.confirmation_result = None
    
    # 测试长理由
    long_reason = "这是一个非常长的理由，用于测试系统是否能够正确处理长文本内容。" * 50
    violation.set_manual_confirmation("用户", ConfirmationResult.HAS_ISSUE, long_reason)
    assert violation.reason == long_reason
    print("✓ 长理由测试通过")
    
    # 测试特殊字符
    violation.status = ViolationStatus.PENDING
    violation.confirmer = ""
    violation.reason = ""
    violation.confirmation_result = None
    
    special_confirmer = "用户@#$%^&*()"
    special_reason = "理由包含特殊字符：!@#$%^&*()_+-=[]{}|;':\",./<>?"
    violation.set_manual_confirmation(special_confirmer, ConfirmationResult.NO_ISSUE, special_reason)
    assert violation.confirmer == special_confirmer
    assert violation.reason == special_reason
    print("✓ 特殊字符测试通过")
    
    print()


def main():
    """主函数"""
    print("自动化确认对话框测试")
    print("=" * 50)
    
    try:
        test_violation_confirmation()
        test_validation_logic()
        test_status_transitions()
        test_confirmation_data_integrity()
        test_edge_cases()
        
        print("🎉 所有自动化测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)