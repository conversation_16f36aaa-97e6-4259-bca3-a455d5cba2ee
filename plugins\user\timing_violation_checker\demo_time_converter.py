#!/usr/bin/env python3
"""
TimeConverter功能演示脚本

展示TimeConverter类的各种功能和用法。
"""

from .utils import TimeConverter
from .exceptions import TimeConversionError


def demo_basic_conversion():
    """演示基本时间转换功能"""
    print("=== 基本时间转换功能演示 ===")
    
    test_times = ["1000FS", "1.5PS", "2NS", "0.001NS"]
    
    for time_str in test_times:
        try:
            fs_value = TimeConverter.to_femtoseconds(time_str)
            formatted = TimeConverter.format_time(fs_value)
            print(f"{time_str:>8} -> {fs_value:>10} FS -> {formatted}")
        except TimeConversionError as e:
            print(f"{time_str:>8} -> 错误: {e.get_user_message()}")
    
    print()


def demo_time_comparison():
    """演示时间比较功能"""
    print("=== 时间比较功能演示 ===")
    
    time_pairs = [
        ("1000FS", "1PS"),
        ("1PS", "1000FS"), 
        ("2NS", "1000PS"),
        ("500PS", "1NS")
    ]
    
    for time1, time2 in time_pairs:
        try:
            result = TimeConverter.compare_time_strings(time1, time2)
            if result == 0:
                comparison = "等于"
            elif result < 0:
                comparison = "小于"
            else:
                comparison = "大于"
            
            print(f"{time1:>8} {comparison} {time2}")
        except TimeConversionError as e:
            print(f"{time1} vs {time2} -> 错误: {e.get_user_message()}")
    
    print()


def demo_reset_time_validation():
    """演示复位时间验证功能"""
    print("=== 复位时间验证功能演示 ===")
    
    test_reset_times = [
        "1000FS",
        "1.5PS", 
        "2NS",
        "0FS",
        "-1000FS",
        "invalid",
        ""
    ]
    
    for reset_time in test_reset_times:
        valid, error = TimeConverter.validate_reset_time(reset_time)
        status = "有效" if valid else f"无效 - {error}"
        print(f"{reset_time:>10} -> {status}")
    
    print()


def demo_reset_period_check():
    """演示复位期间违例检查功能"""
    print("=== 复位期间违例检查演示 ===")
    
    reset_time = "2PS"  # 2000FS
    reset_time_fs = TimeConverter.to_femtoseconds(reset_time)
    
    violation_times = ["500FS", "1PS", "2PS", "3PS", "1NS"]
    
    print(f"复位时间: {reset_time} ({reset_time_fs} FS)")
    print("违例时间检查:")
    
    for vio_time in violation_times:
        try:
            vio_time_fs = TimeConverter.to_femtoseconds(vio_time)
            is_reset_period = TimeConverter.is_reset_period_violation(vio_time_fs, reset_time_fs)
            status = "复位期间违例" if is_reset_period else "正常违例"
            print(f"  {vio_time:>8} ({vio_time_fs:>7} FS) -> {status}")
        except TimeConversionError as e:
            print(f"  {vio_time:>8} -> 错误: {e.get_user_message()}")
    
    print()


def demo_batch_validation():
    """演示批量验证功能"""
    print("=== 批量时间验证演示 ===")
    
    time_strings = [
        "1000FS",
        "1.5PS", 
        "2NS",
        "invalid_time",
        "",
        "-500FS",
        "1000MS",
        "0.001NS"
    ]
    
    valid_times, invalid_times = TimeConverter.batch_validate_times(time_strings)
    
    print("有效时间:")
    for valid_time in valid_times:
        print(f"  [{valid_time['index']}] {valid_time['original']:>12} -> {valid_time['fs_value']:>10} FS -> {valid_time['formatted']}")
    
    print("\n无效时间:")
    for invalid_time in invalid_times:
        print(f"  [{invalid_time['index']}] {invalid_time['original']:>12} -> {invalid_time['error']}")
    
    print()


def demo_time_difference():
    """演示时间差值计算功能"""
    print("=== 时间差值计算演示 ===")
    
    time_pairs = [
        (2000, 1000),      # 2PS - 1PS
        (1000000, 500000), # 1NS - 0.5NS
        (1500, 500),       # 1.5PS - 0.5PS
    ]
    
    for time1_fs, time2_fs in time_pairs:
        diff_auto = TimeConverter.get_time_difference(time1_fs, time2_fs)
        diff_fs = TimeConverter.get_time_difference(time1_fs, time2_fs, "FS")
        diff_ps = TimeConverter.get_time_difference(time1_fs, time2_fs, "PS")
        
        time1_formatted = TimeConverter.format_time(time1_fs)
        time2_formatted = TimeConverter.format_time(time2_fs)
        
        print(f"{time1_formatted} - {time2_formatted}:")
        print(f"  自动单位: {diff_auto}")
        print(f"  飞秒:     {diff_fs}")
        print(f"  皮秒:     {diff_ps}")
        print()


def main():
    """主演示函数"""
    print("TimeConverter 功能演示")
    print("=" * 50)
    print()
    
    try:
        demo_basic_conversion()
        demo_time_comparison()
        demo_reset_time_validation()
        demo_reset_period_check()
        demo_batch_validation()
        demo_time_difference()
        
        print("演示完成！")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")


if __name__ == "__main__":
    main()