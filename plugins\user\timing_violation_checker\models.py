"""
时序违例确认插件数据模型

定义时序违例数据结构、状态枚举和相关数据类。
"""

from dataclasses import dataclass
from enum import Enum
from typing import Optional


class ViolationStatus(Enum):
    """时序违例确认状态枚举"""
    PENDING = "待确认"
    CONFIRMED_OK = "已确认-无问题"
    CONFIRMED_ISSUE = "已确认-有问题"
    AUTO_CONFIRMED = "自动确认"


class ConfirmationResult(Enum):
    """确认结果枚举"""
    NO_ISSUE = "无问题"
    HAS_ISSUE = "有问题"


@dataclass
class ViolationData:
    """时序违例数据模型
    
    存储从vio_summary.log文件解析出的时序违例信息，
    以及用户确认的相关信息。
    """
    num: int                                    # 违例序号
    hier: str                                   # 层级路径
    time_original: str                          # 原始时间字符串
    time_fs: int                               # 转换为飞秒的时间
    check: str                                 # 检查信息
    status: ViolationStatus = ViolationStatus.PENDING  # 确认状态
    confirmer: str = ""                        # 确认人
    reason: str = ""                           # 确认理由/解决方案
    auto_confirmed: bool = False               # 是否自动确认
    confirmation_result: Optional[ConfirmationResult] = None  # 确认结果
    
    def __post_init__(self):
        """数据验证和初始化后处理"""
        # 确保num为正整数
        if self.num <= 0:
            raise ValueError(f"违例序号必须为正整数，当前值: {self.num}")
        
        # 确保hier不为空
        if not self.hier.strip():
            raise ValueError("层级路径不能为空")
        
        # 确保time_original不为空
        if not self.time_original.strip():
            raise ValueError("原始时间字符串不能为空")
        
        # 确保time_fs为非负整数
        if self.time_fs < 0:
            raise ValueError(f"飞秒时间必须为非负整数，当前值: {self.time_fs}")
        
        # 确保check不为空
        if not self.check.strip():
            raise ValueError("检查信息不能为空")
    
    def is_confirmed(self) -> bool:
        """检查违例是否已确认"""
        return self.status != ViolationStatus.PENDING
    
    def is_auto_confirmed(self) -> bool:
        """检查违例是否为自动确认"""
        return self.auto_confirmed or self.status == ViolationStatus.AUTO_CONFIRMED
    
    def get_status_display(self) -> str:
        """获取状态显示文本"""
        return self.status.value
    
    def set_manual_confirmation(self, confirmer: str, result: ConfirmationResult, reason: str):
        """设置手动确认信息
        
        Args:
            confirmer: 确认人
            result: 确认结果
            reason: 确认理由
        """
        if not confirmer.strip():
            raise ValueError("确认人不能为空")
        
        if not reason.strip():
            raise ValueError("确认理由不能为空")
        
        self.confirmer = confirmer.strip()
        self.confirmation_result = result
        self.reason = reason.strip()
        self.auto_confirmed = False
        
        # 根据确认结果设置状态
        if result == ConfirmationResult.NO_ISSUE:
            self.status = ViolationStatus.CONFIRMED_OK
        else:
            self.status = ViolationStatus.CONFIRMED_ISSUE
    
    def set_auto_confirmation(self, reason: str = "复位期间时序违例，可以忽略"):
        """设置自动确认
        
        Args:
            reason: 自动确认理由
        """
        self.auto_confirmed = True
        self.status = ViolationStatus.AUTO_CONFIRMED
        self.reason = reason
        self.confirmer = "系统自动"
        self.confirmation_result = ConfirmationResult.NO_ISSUE
    
    def is_exportable(self) -> bool:
        """检查违例是否可导出
        
        Returns:
            bool: 是否可导出
        """
        # 所有违例都可以导出，包括待确认的
        return True


@dataclass
class CaseInfo:
    """用例信息数据模型
    
    存储从文件路径提取的用例名称和corner信息。
    """
    case_name: str                             # 用例名称
    corner: str                                # Corner信息
    directory_path: str                        # 目录路径
    
    def __post_init__(self):
        """数据验证"""
        if not self.case_name.strip():
            raise ValueError("用例名称不能为空")
        
        if not self.directory_path.strip():
            raise ValueError("目录路径不能为空")
        
        # corner可以为空，表示需要用户选择
        self.case_name = self.case_name.strip()
        self.corner = self.corner.strip()
        self.directory_path = self.directory_path.strip()
    
    def has_corner(self) -> bool:
        """检查是否已有corner信息"""
        return bool(self.corner)
    
    def get_display_name(self) -> str:
        """获取显示名称"""
        if self.has_corner():
            return f"{self.case_name}_{self.corner}"
        return self.case_name


@dataclass
class ExportConfig:
    """导出配置数据模型
    
    存储导出操作的配置信息。
    """
    format_type: str                           # 导出格式类型 ("excel" 或 "csv")
    include_auto_confirmed: bool = True        # 是否包含自动确认的违例
    file_path: str = ""                        # 导出文件路径
    only_confirmed: bool = False               # 是否仅导出已确认的违例
    
    def __post_init__(self):
        """数据验证"""
        valid_formats = ["excel", "csv"]
        if self.format_type not in valid_formats:
            raise ValueError(f"不支持的导出格式: {self.format_type}，支持的格式: {valid_formats}")
    
    def is_excel_format(self) -> bool:
        """检查是否为Excel格式"""
        return self.format_type == "excel"
    
    def is_csv_format(self) -> bool:
        """检查是否为CSV格式"""
        return self.format_type == "csv"
    
    def get_file_extension(self) -> str:
        """获取文件扩展名"""
        if self.is_excel_format():
            return ".xlsx"
        elif self.is_csv_format():
            return ".csv"
        return ""
    
    def get_filter_description(self) -> str:
        """获取过滤条件描述
        
        Returns:
            str: 过滤条件的文字描述
        """
        descriptions = []
        
        if self.only_confirmed:
            descriptions.append("仅已确认违例")
        else:
            descriptions.append("所有违例")
        
        if not self.include_auto_confirmed:
            descriptions.append("排除自动确认")
        
        return ", ".join(descriptions) if descriptions else "默认设置"


# 合法的corner选项列表
VALID_CORNERS = [
    "npg_f1_ssg",
    "npg_f1_tt", 
    "npg_f1_ffg",
    "npg_f2_ssg",
    "npg_f2_tt",
    "npg_f2_ffg", 
    "npg_f3_ssg",
    "npg_f3_ffg",
    "npg_f4_ssg",
    "npg_f4_ffg",
    "npg_f5_ssg",
    "npg_f5_ffg",
    "npg_f6_ssg",
    "npg_f6_ffg",
    "npg_f7_ssg",
    "npg_f7_ffg"
]


def is_valid_corner(corner: str) -> bool:
    """检查corner是否合法
    
    Args:
        corner: corner字符串
        
    Returns:
        bool: 是否为合法corner
    """
    return corner.strip() in VALID_CORNERS


def get_corner_display_list() -> list:
    """获取corner显示列表
    
    Returns:
        list: corner选项列表
    """
    return VALID_CORNERS.copy()