"""
ViolationListWidget集成测试

测试违例列表组件与主对话框的集成。
"""

import sys
import os
from typing import List

# 添加插件路径到sys.path
plugin_path = os.path.dirname(os.path.abspath(__file__))
if plugin_path not in sys.path:
    sys.path.insert(0, plugin_path)

from PyQt5.QtWidgets import QApplication, QPushButton, QVBoxLayout, QWidget, QMainWindow
from PyQt5.QtCore import Qt

from models import ViolationData, ViolationStatus, ConfirmationResult
from timing_violation_dialog import TimingViolationDialog


class IntegrationTestWindow(QMainWindow):
    """集成测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("ViolationListWidget 集成测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建界面
        self.setup_ui()
        
        # 创建测试数据
        self.test_violations = self._create_test_data()
    
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 打开对话框按钮
        open_dialog_btn = QPushButton("打开时序违例确认对话框")
        open_dialog_btn.clicked.connect(self.open_dialog)
        layout.addWidget(open_dialog_btn)
        
        # 测试加载数据按钮
        load_data_btn = QPushButton("加载测试数据到对话框")
        load_data_btn.clicked.connect(self.load_test_data)
        layout.addWidget(load_data_btn)
        
        # 对话框引用
        self.dialog = None
    
    def _create_test_data(self) -> List[ViolationData]:
        """创建测试数据"""
        test_data = []
        
        violations_config = [
            (1, "tb_top.cpu.core0", "1500000FS", 1500000, "setup(posedge clk, data)", ViolationStatus.PENDING),
            (2, "tb_top.memory.ctrl", "2000000FS", 2000000, "hold(posedge clk, addr)", ViolationStatus.CONFIRMED_OK),
            (3, "tb_top.bus.arbiter", "500000FS", 500000, "setup(negedge rst_n, enable)", ViolationStatus.AUTO_CONFIRMED),
            (4, "tb_top.io.uart", "3000000FS", 3000000, "hold(posedge clk, req)", ViolationStatus.CONFIRMED_ISSUE),
        ]
        
        for num, hier, time_orig, time_fs, check, status in violations_config:
            violation = ViolationData(
                num=num,
                hier=hier,
                time_original=time_orig,
                time_fs=time_fs,
                check=check,
                status=status
            )
            
            # 为已确认的违例设置确认信息
            if status == ViolationStatus.CONFIRMED_OK:
                violation.set_manual_confirmation(
                    confirmer="集成测试",
                    result=ConfirmationResult.NO_ISSUE,
                    reason="集成测试 - 无问题"
                )
            elif status == ViolationStatus.CONFIRMED_ISSUE:
                violation.set_manual_confirmation(
                    confirmer="集成测试",
                    result=ConfirmationResult.HAS_ISSUE,
                    reason="集成测试 - 有问题需要修复"
                )
            elif status == ViolationStatus.AUTO_CONFIRMED:
                violation.set_auto_confirmation("复位期间时序违例，可以忽略")
            
            test_data.append(violation)
        
        return test_data
    
    def open_dialog(self):
        """打开时序违例确认对话框"""
        if self.dialog is None:
            self.dialog = TimingViolationDialog(self)
        
        self.dialog.show()
        self.dialog.raise_()
        self.dialog.activateWindow()
        
        print("时序违例确认对话框已打开")
    
    def load_test_data(self):
        """加载测试数据到对话框"""
        if self.dialog is None:
            self.open_dialog()
        
        # 加载测试数据到违例列表
        self.dialog.load_violations_to_list(self.test_violations)
        
        print(f"已加载 {len(self.test_violations)} 条测试违例数据到对话框")
        
        # 获取违例列表组件并验证功能
        violation_list = self.dialog.get_violation_list_widget()
        if violation_list:
            summary = violation_list.get_summary_info()
            print(f"违例汇总: 总数={summary['total_count']}, 待确认={summary['pending_count']}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = IntegrationTestWindow()
    window.show()
    
    print("集成测试窗口已启动")
    print("点击按钮打开时序违例确认对话框并测试功能")
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()