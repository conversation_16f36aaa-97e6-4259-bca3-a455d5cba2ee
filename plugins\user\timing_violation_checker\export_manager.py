"""
时序违例确认插件导出管理器

负责将时序违例数据导出为Excel或CSV格式文件。
"""

import csv
import os
from datetime import datetime
from typing import List, Optional

try:
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.utils import get_column_letter
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

try:
    from .models import ViolationData, ExportConfig, ViolationStatus
    from .exceptions import ExportError
except ImportError:
    # For standalone testing
    from models import ViolationData, ExportConfig, ViolationStatus
    from exceptions import ExportError


class ExportManager:
    """导出管理器
    
    负责将时序违例数据导出为不同格式的文件。
    支持Excel和CSV格式导出。
    """
    
    # Excel样式定义
    HEADER_STYLE = {
        'font': Font(bold=True, color='FFFFFF'),
        'fill': PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid'),
        'alignment': Alignment(horizontal='center', vertical='center'),
        'border': Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    }
    
    # 状态颜色映射
    STATUS_COLORS = {
        ViolationStatus.PENDING: 'FFF3CD',          # 黄色
        ViolationStatus.CONFIRMED_OK: 'D4EDDA',     # 绿色
        ViolationStatus.CONFIRMED_ISSUE: 'F8D7DA',  # 红色
        ViolationStatus.AUTO_CONFIRMED: 'E2E3E5'    # 灰色
    }
    
    def __init__(self):
        """初始化导出管理器"""
        self.column_headers = [
            '序号', '层级路径', '原始时间', '时间(fs)', '检查信息',
            '状态', '确认人', '确认结果', '确认理由', '自动确认'
        ]
    
    def export_violations(self, violations: List[ViolationData], config: ExportConfig,
                         case_info: Optional[dict] = None, progress_callback=None) -> str:
        """导出时序违例数据
        
        Args:
            violations: 违例数据列表
            config: 导出配置
            case_info: 用例信息字典，包含case_name和corner
            progress_callback: 进度回调函数，接受(current, total, message)参数
            
        Returns:
            str: 导出文件的完整路径
            
        Raises:
            ExportError: 导出失败时抛出
        """
        try:
            # 过滤数据
            filtered_violations = self._filter_violations(violations, config)
            
            if config.is_excel_format():
                return self._export_to_excel(filtered_violations, config, case_info)
            elif config.is_csv_format():
                return self._export_to_csv(filtered_violations, config, case_info)
            else:
                raise ExportError(f"不支持的导出格式: {config.format_type}")
                
        except Exception as e:
            raise ExportError(f"导出失败: {str(e)}") from e
    
    def _filter_violations(self, violations: List[ViolationData], 
                          config: ExportConfig) -> List[ViolationData]:
        """根据配置过滤违例数据
        
        Args:
            violations: 原始违例数据列表
            config: 导出配置
            
        Returns:
            List[ViolationData]: 过滤后的违例数据列表
        """
        filtered_violations = violations.copy()
        
        # 如果不包含自动确认的违例，过滤掉它们
        if not config.include_auto_confirmed:
            filtered_violations = [v for v in filtered_violations if not v.is_auto_confirmed()]
        
        # 如果只导出已确认的违例，过滤掉待确认的违例
        if hasattr(config, 'only_confirmed') and config.only_confirmed:
            filtered_violations = [v for v in filtered_violations if v.is_confirmed()]
        
        return filtered_violations
    
    def _export_to_excel(self, violations: List[ViolationData], config: ExportConfig,
                        case_info: Optional[dict] = None) -> str:
        """导出为Excel格式
        
        Args:
            violations: 违例数据列表
            config: 导出配置
            case_info: 用例信息
            
        Returns:
            str: 导出文件路径
            
        Raises:
            ExportError: Excel导出失败时抛出
        """
        if not OPENPYXL_AVAILABLE:
            raise ExportError("Excel导出需要安装openpyxl库")
        
        try:
            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "时序违例确认报告"
            
            # 添加标题信息
            current_row = self._add_excel_header_info(ws, case_info)
            
            # 添加表格标题
            current_row = self._add_excel_table_headers(ws, current_row)
            
            # 添加数据行
            current_row = self._add_excel_data_rows(ws, violations, current_row)
            
            # 调整列宽
            self._adjust_excel_column_widths(ws)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(config.file_path), exist_ok=True)
            
            # 保存文件
            wb.save(config.file_path)
            
            return config.file_path
            
        except Exception as e:
            raise ExportError(f"Excel导出失败: {str(e)}") from e
    
    def _add_excel_header_info(self, ws, case_info: Optional[dict]) -> int:
        """添加Excel文件头部信息
        
        Args:
            ws: Excel工作表
            case_info: 用例信息
            
        Returns:
            int: 下一行的行号
        """
        current_row = 1
        
        # 标题
        ws.cell(row=current_row, column=1, value="时序违例确认报告")
        ws.cell(row=current_row, column=1).font = Font(size=16, bold=True)
        current_row += 1
        
        # 生成时间
        ws.cell(row=current_row, column=1, value=f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        current_row += 1
        
        # 用例信息
        if case_info:
            if case_info.get('case_name'):
                ws.cell(row=current_row, column=1, value=f"用例名称: {case_info['case_name']}")
                current_row += 1
            
            if case_info.get('corner'):
                ws.cell(row=current_row, column=1, value=f"Corner: {case_info['corner']}")
                current_row += 1
        
        # 空行
        current_row += 1
        
        return current_row
    
    def _add_excel_table_headers(self, ws, start_row: int) -> int:
        """添加Excel表格标题行
        
        Args:
            ws: Excel工作表
            start_row: 开始行号
            
        Returns:
            int: 下一行的行号
        """
        for col_idx, header in enumerate(self.column_headers, 1):
            cell = ws.cell(row=start_row, column=col_idx, value=header)
            
            # 应用标题样式
            cell.font = self.HEADER_STYLE['font']
            cell.fill = self.HEADER_STYLE['fill']
            cell.alignment = self.HEADER_STYLE['alignment']
            cell.border = self.HEADER_STYLE['border']
        
        return start_row + 1
    
    def _add_excel_data_rows(self, ws, violations: List[ViolationData], start_row: int) -> int:
        """添加Excel数据行
        
        Args:
            ws: Excel工作表
            violations: 违例数据列表
            start_row: 开始行号
            
        Returns:
            int: 下一行的行号
        """
        current_row = start_row
        
        for violation in violations:
            # 数据列
            data_row = [
                violation.num,
                violation.hier,
                violation.time_original,
                violation.time_fs,
                violation.check,
                violation.get_status_display(),
                violation.confirmer,
                violation.confirmation_result.value if violation.confirmation_result else "",
                violation.reason,
                "是" if violation.auto_confirmed else "否"
            ]
            
            # 填充数据
            for col_idx, value in enumerate(data_row, 1):
                cell = ws.cell(row=current_row, column=col_idx, value=value)
                
                # 添加边框
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                
                # 根据状态设置背景色
                if col_idx == 6:  # 状态列
                    status_color = self.STATUS_COLORS.get(violation.status)
                    if status_color:
                        cell.fill = PatternFill(start_color=status_color, 
                                              end_color=status_color, 
                                              fill_type='solid')
            
            current_row += 1
        
        return current_row
    
    def _adjust_excel_column_widths(self, ws):
        """调整Excel列宽
        
        Args:
            ws: Excel工作表
        """
        # 预设列宽
        column_widths = {
            1: 8,   # 序号
            2: 30,  # 层级路径
            3: 15,  # 原始时间
            4: 15,  # 时间(fs)
            5: 25,  # 检查信息
            6: 15,  # 状态
            7: 12,  # 确认人
            8: 12,  # 确认结果
            9: 30,  # 确认理由
            10: 10  # 自动确认
        }
        
        for col_idx, width in column_widths.items():
            column_letter = get_column_letter(col_idx)
            ws.column_dimensions[column_letter].width = width
    
    def _export_to_csv(self, violations: List[ViolationData], config: ExportConfig,
                      case_info: Optional[dict] = None) -> str:
        """导出为CSV格式
        
        Args:
            violations: 违例数据列表
            config: 导出配置
            case_info: 用例信息
            
        Returns:
            str: 导出文件路径
            
        Raises:
            ExportError: CSV导出失败时抛出
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(config.file_path), exist_ok=True)
            
            with open(config.file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入头部信息
                writer.writerow(['# 时序违例确认报告'])
                writer.writerow([f'# 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                
                if case_info:
                    if case_info.get('case_name'):
                        writer.writerow([f'# 用例名称: {case_info["case_name"]}'])
                    if case_info.get('corner'):
                        writer.writerow([f'# Corner: {case_info["corner"]}'])
                
                writer.writerow([])  # 空行
                
                # 写入表格标题
                writer.writerow(self.column_headers)
                
                # 写入数据行
                for violation in violations:
                    data_row = [
                        violation.num,
                        violation.hier,
                        violation.time_original,
                        violation.time_fs,
                        violation.check,
                        violation.get_status_display(),
                        violation.confirmer,
                        violation.confirmation_result.value if violation.confirmation_result else "",
                        violation.reason,
                        "是" if violation.auto_confirmed else "否"
                    ]
                    writer.writerow(data_row)
            
            return config.file_path
            
        except Exception as e:
            raise ExportError(f"CSV导出失败: {str(e)}") from e
    
    def get_default_filename(self, case_info: Optional[dict] = None, 
                           format_type: str = "excel") -> str:
        """生成默认文件名
        
        Args:
            case_info: 用例信息
            format_type: 导出格式类型
            
        Returns:
            str: 默认文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if case_info and case_info.get('case_name'):
            case_name = case_info['case_name']
            corner = case_info.get('corner', '')
            
            if corner:
                base_name = f"时序违例确认_{case_name}_{corner}_{timestamp}"
            else:
                base_name = f"时序违例确认_{case_name}_{timestamp}"
        else:
            base_name = f"时序违例确认_{timestamp}"
        
        if format_type == "excel":
            return f"{base_name}.xlsx"
        elif format_type == "csv":
            return f"{base_name}.csv"
        else:
            return f"{base_name}.txt"
    
    def validate_export_path(self, file_path: str) -> bool:
        """验证导出路径是否有效
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 路径是否有效
        """
        try:
            # 检查目录是否可以创建
            directory = os.path.dirname(file_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
            
            # 检查文件是否可以写入
            with open(file_path, 'w') as f:
                pass
            
            # 删除测试文件
            if os.path.exists(file_path):
                os.remove(file_path)
            
            return True
            
        except Exception:
            return False
    
    def get_export_summary(self, violations: List[ViolationData], 
                          config: ExportConfig) -> dict:
        """获取导出摘要信息
        
        Args:
            violations: 违例数据列表
            config: 导出配置
            
        Returns:
            dict: 导出摘要信息
        """
        filtered_violations = self._filter_violations(violations, config)
        
        # 统计各状态数量
        status_counts = {}
        for status in ViolationStatus:
            status_counts[status.value] = sum(1 for v in filtered_violations if v.status == status)
        
        return {
            'total_violations': len(violations),
            'exported_violations': len(filtered_violations),
            'format_type': config.format_type,
            'include_auto_confirmed': config.include_auto_confirmed,
            'status_counts': status_counts,
            'confirmed_count': sum(1 for v in filtered_violations if v.is_confirmed()),
            'pending_count': sum(1 for v in filtered_violations if not v.is_confirmed())
        }