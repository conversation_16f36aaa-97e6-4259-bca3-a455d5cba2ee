"""
时序违例确认主对话框

提供时序违例确认的主要用户界面，集成文件选择、违例列表显示、
确认操作和导出功能。
"""

from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QMessageBox, QSplitter, QGroupBox,
                             QFrame, QMenu)
from PyQt5.QtCore import Qt, pyqtSignal

from plugins.base import NonModalDialog
from .exceptions import TimingViolationError
from .error_handler import ErrorHandlerMixin, show_error_dialog, create_progress_error_handler
from .progress_manager import create_progress_dialog, global_performance_monitor, monitor_performance
from .file_selection_widget import FileSelectionWidget
from .violation_list_widget import ViolationListWidget
from .reset_time_widget import ResetTimeWidget
from .models import CaseInfo, ViolationData, ViolationStatus


class TimingViolationDialog(NonModalDialog, ErrorHandlerMixin):
    """时序违例确认主对话框类
    
    继承自NonModalDialog，提供时序违例确认的完整用户界面。
    集成文件选择、违例列表、复位时间等组件，实现对话框大小和样式设置。
    集成全面的错误处理和用户体验优化功能。
    """
    
    # 定义信号
    data_loaded = pyqtSignal(list)  # 数据加载完成信号
    export_completed = pyqtSignal(str)  # 导出完成信号
    error_occurred = pyqtSignal(Exception, str)  # 错误发生信号
    
    def __init__(self, parent=None):
        """初始化对话框
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent, "时序违例确认工具")
        ErrorHandlerMixin.__init__(self)
        self.setWindowTitle("时序违例确认工具")
        
        # 设置对话框大小和样式
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)  # 默认大小
        
        # 初始化错误处理和进度管理
        self.progress_handler = create_progress_error_handler(self)
        self.performance_monitor = global_performance_monitor
        
        # 初始化用户体验优化
        self._setup_ux_optimizations()
        
        # 设置窗口图标和样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        # 初始化数据
        self.violations = []
        self.case_info = None
        self.reset_time_fs = 0
        
        # 设置界面
        self.setup_ui()
    
    @monitor_performance("setup_ui", global_performance_monitor)
    def setup_ui(self):
        """设置用户界面
        
        设计整体界面布局和组件排列，集成文件选择、违例列表、复位时间等组件
        """
        try:
            self.progress_handler.start_progress("初始化界面", "正在设置用户界面...")
            
            # 创建主布局
            main_layout = QVBoxLayout()
            main_layout.setSpacing(15)
            main_layout.setContentsMargins(15, 15, 15, 15)
            
            # 添加标题区域
            self.progress_handler.update_progress("设置标题区域")
            self._setup_title_section(main_layout)
            
            # 添加文件信息区域
            self.progress_handler.update_progress("设置文件信息区域")
            self._setup_file_section(main_layout)
            
            # 添加复位时间设置区域
            self.progress_handler.update_progress("设置复位时间区域")
            self._setup_reset_time_section(main_layout)
            
            # 添加违例列表区域
            self.progress_handler.update_progress("设置违例列表区域")
            self._setup_violation_list_section(main_layout)
            
            # 添加操作按钮区域
            self.progress_handler.update_progress("设置按钮区域")
            self._setup_button_section(main_layout)
            
            # 设置主布局
            self.setLayout(main_layout)
            
            # 连接组件信号
            self.progress_handler.update_progress("连接组件信号")
            self._connect_signals()
            
            self.progress_handler.stop_progress()
            
        except Exception as e:
            self.progress_handler.stop_progress()
            self.handle_error(e, "初始化用户界面", show_dialog=True)
            raise
    
    def _setup_title_section(self, main_layout):
        """设置标题区域"""
        # 标题组
        title_group = QGroupBox()
        title_layout = QVBoxLayout()
        
        # 主标题
        title_label = QLabel("时序违例确认工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 18px; 
            font-weight: bold; 
            color: #2c3e50;
            margin: 10px;
        """)
        title_layout.addWidget(title_label)
        
        # 说明文本
        info_label = QLabel("此工具用于解析和确认后仿真时序违例日志文件(vio_summary.log)")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            color: #6c757d; 
            font-size: 12px;
            margin-bottom: 10px;
        """)
        title_layout.addWidget(info_label)
        
        title_group.setLayout(title_layout)
        main_layout.addWidget(title_group)
    
    def _setup_file_section(self, main_layout):
        """设置文件信息区域"""
        # 文件信息组
        file_group = QGroupBox("文件信息")
        file_layout = QVBoxLayout()
        
        # 创建文件选择组件
        self.file_selection_widget = FileSelectionWidget(self)
        file_layout.addWidget(self.file_selection_widget)
        
        file_group.setLayout(file_layout)
        main_layout.addWidget(file_group)
    
    def _setup_reset_time_section(self, main_layout):
        """设置复位时间区域"""
        # 复位时间组
        reset_group = QGroupBox("复位时间设置")
        reset_layout = QVBoxLayout()
        
        # 创建复位时间组件
        self.reset_time_widget = ResetTimeWidget(self)
        reset_layout.addWidget(self.reset_time_widget)
        
        reset_group.setLayout(reset_layout)
        main_layout.addWidget(reset_group)
    
    def _setup_violation_list_section(self, main_layout):
        """设置违例列表区域"""
        # 违例列表组
        violation_group = QGroupBox("时序违例列表")
        violation_layout = QVBoxLayout()
        
        # 创建违例列表组件
        self.violation_list_widget = ViolationListWidget(self)
        violation_layout.addWidget(self.violation_list_widget)
        
        violation_group.setLayout(violation_layout)
        main_layout.addWidget(violation_group)
    
    def _setup_button_section(self, main_layout):
        """设置按钮区域"""
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("color: #dee2e6;")
        main_layout.addWidget(separator)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 导出按钮组
        self._create_export_buttons(button_layout)
        
        # 关闭按钮
        self._create_close_button(button_layout)
        
        main_layout.addLayout(button_layout)
    
    def _create_export_buttons(self, button_layout):
        """创建导出按钮组"""
        # Excel导出按钮
        self.export_excel_button = QPushButton("📊 导出Excel")
        self.export_excel_button.setEnabled(False)
        self.export_excel_button.setMinimumSize(120, 35)
        self.export_excel_button.setToolTip("导出为Excel格式，支持丰富的格式和样式")
        self.export_excel_button.setStyleSheet(self._get_button_style("#28a745"))
        button_layout.addWidget(self.export_excel_button)
        
        # CSV导出按钮
        self.export_csv_button = QPushButton("📄 导出CSV")
        self.export_csv_button.setEnabled(False)
        self.export_csv_button.setMinimumSize(120, 35)
        self.export_csv_button.setToolTip("导出为CSV格式，兼容性好，可用Excel打开")
        self.export_csv_button.setStyleSheet(self._get_button_style("#17a2b8"))
        button_layout.addWidget(self.export_csv_button)
        
        # 快速导出按钮（带下拉菜单）
        self.quick_export_button = QPushButton("⚡ 快速导出")
        self.quick_export_button.setEnabled(False)
        self.quick_export_button.setMinimumSize(120, 35)
        self.quick_export_button.setToolTip("使用默认设置快速导出")
        self.quick_export_button.setStyleSheet(self._get_button_style("#ffc107", "#212529"))
        
        # 创建快速导出菜单
        quick_menu = QMenu(self)
        quick_menu.addAction("Excel (包含所有违例)", lambda: self._quick_export("excel", True))
        quick_menu.addAction("Excel (仅已确认)", lambda: self._quick_export("excel", False))
        quick_menu.addSeparator()
        quick_menu.addAction("CSV (包含所有违例)", lambda: self._quick_export("csv", True))
        quick_menu.addAction("CSV (仅已确认)", lambda: self._quick_export("csv", False))
        self.quick_export_button.setMenu(quick_menu)
        
        button_layout.addWidget(self.quick_export_button)
    
    def _create_close_button(self, button_layout):
        """创建关闭按钮"""
        close_button = QPushButton("关闭")
        close_button.setMinimumSize(100, 35)
        close_button.setStyleSheet(self._get_button_style("#6c757d"))
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)
    
    def _get_button_style(self, bg_color, text_color="white"):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                padding: 8px 15px;
                background-color: {bg_color};
                color: {text_color};
                border: none;
                border-radius: 5px;
                font-weight: bold;
                font-size: 11px;
            }}
            QPushButton:hover:enabled {{
                background-color: {self._darken_color(bg_color)};
                transform: translateY(-1px);
            }}
            QPushButton:pressed:enabled {{
                background-color: {self._darken_color(bg_color, 0.2)};
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #666666;
            }}
        """
    
    def _darken_color(self, color, factor=0.1):
        """使颜色变暗"""
        # 简单的颜色变暗实现
        color_map = {
            "#28a745": "#218838",
            "#17a2b8": "#138496", 
            "#ffc107": "#e0a800",
            "#6c757d": "#5a6268"
        }
        return color_map.get(color, color)
    
    def _connect_signals(self):
        """连接组件信号
        
        连接各组件的信号和槽，实现数据在组件间的传递，
        处理用户操作的响应流程，实现界面状态管理
        """
        try:
            # 连接错误处理信号
            self.error_occurred.connect(self._handle_component_error)
            
            # 连接文件选择组件信号
            if hasattr(self, 'file_selection_widget'):
                self._connect_file_selection_signals()
            
            # 连接复位时间组件信号
            if hasattr(self, 'reset_time_widget'):
                self._connect_reset_time_signals()
            
            # 连接违例列表组件信号
            if hasattr(self, 'violation_list_widget'):
                self._connect_violation_list_signals()
            
            # 连接导出按钮信号
            self._connect_export_button_signals()
            
            # 连接内部数据变化信号
            self.data_loaded.connect(self.on_data_loaded)
            self.export_completed.connect(self.on_export_completed)
            
            # 设置组件间的交互逻辑
            self.setup_component_interactions()
            
            self._error_logger.log_info("组件信号连接完成", "信号连接")
            
        except Exception as e:
            self.handle_error(e, "连接组件信号", show_dialog=True)
    
    def _connect_file_selection_signals(self):
        """连接文件选择组件信号"""
        try:
            self.file_selection_widget.file_selected.connect(self.on_file_selected)
            self.file_selection_widget.case_info_extracted.connect(self.on_case_info_extracted)
            self.file_selection_widget.corner_selection_required.connect(self.on_corner_selection_required)
            self.file_selection_widget.validation_error.connect(self.on_validation_error)
            
            # 连接文件选择组件的数据变化信号到界面状态更新
            self.file_selection_widget.case_info_extracted.connect(self.handle_data_change)
            self.file_selection_widget.validation_error.connect(self.handle_data_change)
            
            # 连接文件选择成功信号到违例列表清空
            self.file_selection_widget.file_selected.connect(self.clear_violation_list)
            
            # 连接文件选择成功信号到组件启用
            self.file_selection_widget.file_selected.connect(self.enable_components_after_file_selection)
            
            # 连接用例信息提取成功信号到窗口标题更新
            self.file_selection_widget.case_info_extracted.connect(self.update_window_title)
            
        except Exception as e:
            self.handle_error(e, "连接文件选择组件信号")
    
    def _connect_reset_time_signals(self):
        """连接复位时间组件信号"""
        try:
            self.reset_time_widget.reset_time_applied.connect(self.on_reset_time_applied)
            self.reset_time_widget.reset_time_cleared.connect(self.on_reset_time_cleared)
            self.reset_time_widget.validation_error.connect(self.on_reset_time_validation_error)
            
            # 连接复位时间变化信号到违例状态更新
            self.reset_time_widget.reset_time_applied.connect(self.refresh_violation_display)
            self.reset_time_widget.reset_time_cleared.connect(self.refresh_violation_display)
            
            # 连接复位时间应用信号到自动确认逻辑
            self.reset_time_widget.reset_time_applied.connect(self.apply_reset_time_auto_confirmation)
            self.reset_time_widget.reset_time_cleared.connect(self.clear_auto_confirmations)
            
        except Exception as e:
            self.handle_error(e, "连接复位时间组件信号")
    
    def _connect_violation_list_signals(self):
        """连接违例列表组件信号"""
        try:
            self.violation_list_widget.violation_selected.connect(self.on_violation_selected)
            self.violation_list_widget.confirm_requested.connect(self.on_confirm_requested)
            self.violation_list_widget.detail_requested.connect(self.on_detail_requested)
            self.violation_list_widget.status_changed.connect(self.on_status_changed)
            
            # 连接违例状态变化信号到界面状态更新
            self.violation_list_widget.status_changed.connect(self.handle_data_change)
            
            # 连接违例状态变化信号到复位时间组件统计更新
            self.violation_list_widget.status_changed.connect(self.update_reset_time_statistics)
            
            # 连接违例状态变化信号到导出按钮状态更新
            self.violation_list_widget.status_changed.connect(self.update_export_button_status)
            
        except Exception as e:
            self.handle_error(e, "连接违例列表组件信号")
    
    def _connect_export_button_signals(self):
        """连接导出按钮信号"""
        try:
            if hasattr(self, 'export_excel_button'):
                self.export_excel_button.clicked.connect(lambda: self.safe_execute(self.export_results, "excel"))
            
            if hasattr(self, 'export_csv_button'):
                self.export_csv_button.clicked.connect(lambda: self.safe_execute(self.export_results, "csv"))
                
        except Exception as e:
            self.handle_error(e, "连接导出按钮信号")
    
    def _handle_component_error(self, error: Exception, context: str):
        """处理组件错误信号
        
        Args:
            error: 异常对象
            context: 错误上下文
        """
        self.handle_error(error, context, show_dialog=True)
    
    # ========== 用户体验优化方法 ==========
    
    def _setup_ux_optimizations(self):
        """设置用户体验优化功能"""
        try:
            # 设置响应式界面
            self._setup_responsive_interface()
            
            # 设置操作确认功能
            self._setup_operation_confirmations()
            
            # 设置内存优化
            self._setup_memory_optimization()
            
            # 设置界面性能优化
            self._setup_performance_optimization()
            
        except Exception as e:
            self.handle_error(e, "设置用户体验优化")
    
    def _setup_responsive_interface(self):
        """设置响应式界面设计"""
        try:
            from PyQt5.QtCore import QTimer
            
            # 创建界面更新定时器
            self.ui_update_timer = QTimer()
            self.ui_update_timer.timeout.connect(self._update_responsive_elements)
            self.ui_update_timer.setSingleShot(True)
            
            # 设置最小更新间隔
            self.min_update_interval = 100  # 100ms
            self.last_update_time = 0
            
            # 启用自动调整大小
            self.setMinimumSize(800, 600)
            self.setSizeGripEnabled(True)
            
        except Exception as e:
            self.handle_error(e, "设置响应式界面")
    
    def _update_responsive_elements(self):
        """更新响应式界面元素"""
        try:
            import time
            current_time = time.time() * 1000  # 转换为毫秒
            
            # 检查更新间隔
            if current_time - self.last_update_time < self.min_update_interval:
                return
            
            # 更新界面元素
            self._adjust_layout_for_size()
            self._update_button_states()
            self._optimize_table_columns()
            
            self.last_update_time = current_time
            
        except Exception as e:
            self.handle_error(e, "更新响应式界面元素")
    
    def _adjust_layout_for_size(self):
        """根据窗口大小调整布局"""
        try:
            window_width = self.width()
            window_height = self.height()
            
            # 根据窗口大小调整组件布局
            if hasattr(self, 'violation_list_widget'):
                # 调整表格列宽
                if window_width < 1000:
                    # 小窗口模式：隐藏部分列或调整列宽
                    self.violation_list_widget.setColumnHidden(3, True)  # 隐藏详细信息列
                else:
                    # 正常模式：显示所有列
                    self.violation_list_widget.setColumnHidden(3, False)
            
            # 调整按钮布局
            if hasattr(self, 'export_excel_button') and hasattr(self, 'export_csv_button'):
                if window_width < 800:
                    # 小窗口模式：使用更紧凑的按钮
                    self.export_excel_button.setText("Excel")
                    self.export_csv_button.setText("CSV")
                else:
                    # 正常模式：使用完整按钮文本
                    self.export_excel_button.setText("📊 导出Excel")
                    self.export_csv_button.setText("📄 导出CSV")
            
        except Exception as e:
            self.handle_error(e, "调整布局大小")
    
    def _update_button_states(self):
        """更新按钮状态"""
        try:
            has_violations = bool(self.violations)
            has_case_info = bool(self.case_info)
            
            # 更新导出按钮状态
            if hasattr(self, 'export_excel_button'):
                self.export_excel_button.setEnabled(has_violations)
            
            if hasattr(self, 'export_csv_button'):
                self.export_csv_button.setEnabled(has_violations)
            
            if hasattr(self, 'quick_export_button'):
                self.quick_export_button.setEnabled(has_violations)
            
        except Exception as e:
            self.handle_error(e, "更新按钮状态")
    
    def _optimize_table_columns(self):
        """优化表格列宽"""
        try:
            if hasattr(self, 'violation_list_widget'):
                # 自动调整列宽
                self.violation_list_widget.resizeColumnsToContents()
                
                # 设置最小列宽
                header = self.violation_list_widget.horizontalHeader()
                for i in range(self.violation_list_widget.columnCount()):
                    current_width = header.sectionSize(i)
                    min_width = 80  # 最小列宽
                    if current_width < min_width:
                        header.resizeSection(i, min_width)
            
        except Exception as e:
            self.handle_error(e, "优化表格列宽")
    
    def _setup_operation_confirmations(self):
        """设置操作确认功能"""
        try:
            # 存储需要确认的操作
            self.confirmation_required_operations = {
                'clear_all_confirmations': True,
                'reset_all_data': True,
                'export_large_data': True
            }
            
            # 存储操作历史（用于撤销功能）
            self.operation_history = []
            self.max_history_size = 10
            
        except Exception as e:
            self.handle_error(e, "设置操作确认功能")
    
    def confirm_operation(self, operation_name: str, message: str) -> bool:
        """确认操作
        
        Args:
            operation_name: 操作名称
            message: 确认消息
            
        Returns:
            bool: 用户是否确认
        """
        try:
            if operation_name not in self.confirmation_required_operations:
                return True
            
            if not self.confirmation_required_operations[operation_name]:
                return True
            
            from PyQt5.QtWidgets import QMessageBox
            
            reply = QMessageBox.question(
                self, "操作确认", message,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            return reply == QMessageBox.Yes
            
        except Exception as e:
            self.handle_error(e, f"确认操作 {operation_name}")
            return False
    
    def add_to_operation_history(self, operation: dict):
        """添加操作到历史记录
        
        Args:
            operation: 操作信息字典
        """
        try:
            import time
            
            operation['timestamp'] = time.time()
            self.operation_history.append(operation)
            
            # 限制历史记录大小
            if len(self.operation_history) > self.max_history_size:
                self.operation_history.pop(0)
                
        except Exception as e:
            self.handle_error(e, "添加操作历史")
    
    def undo_last_operation(self):
        """撤销最后一个操作"""
        try:
            if not self.operation_history:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "撤销操作", "没有可撤销的操作")
                return
            
            last_operation = self.operation_history.pop()
            operation_type = last_operation.get('type')
            
            if operation_type == 'confirmation':
                # 撤销确认操作
                violation_id = last_operation.get('violation_id')
                if violation_id and hasattr(self, 'violation_list_widget'):
                    self.violation_list_widget.undo_confirmation(violation_id)
            
            elif operation_type == 'reset_time':
                # 撤销复位时间设置
                if hasattr(self, 'reset_time_widget'):
                    self.reset_time_widget.clear_reset_time()
            
            self._error_logger.log_info(f"撤销操作: {operation_type}", "操作撤销")
            
        except Exception as e:
            self.handle_error(e, "撤销操作")
    
    def _setup_memory_optimization(self):
        """设置内存优化"""
        try:
            from PyQt5.QtCore import QTimer
            import gc
            
            # 创建内存清理定时器
            self.memory_cleanup_timer = QTimer()
            self.memory_cleanup_timer.timeout.connect(self._cleanup_memory)
            self.memory_cleanup_timer.start(30000)  # 每30秒清理一次
            
            # 设置数据缓存限制
            self.max_cached_violations = 10000
            self.max_cached_exports = 5
            
        except Exception as e:
            self.handle_error(e, "设置内存优化")
    
    def _cleanup_memory(self):
        """清理内存"""
        try:
            import gc
            
            # 强制垃圾回收
            gc.collect()
            
            # 清理过期的缓存数据
            self._cleanup_cached_data()
            
            # 记录内存使用情况
            self._log_memory_usage()
            
        except Exception as e:
            self.handle_error(e, "清理内存")
    
    def _cleanup_cached_data(self):
        """清理缓存数据"""
        try:
            # 限制违例数据缓存
            if len(self.violations) > self.max_cached_violations:
                self._error_logger.log_warning(
                    f"违例数据过多 ({len(self.violations)})，建议分批处理",
                    "内存优化"
                )
            
            # 清理操作历史
            if len(self.operation_history) > self.max_history_size:
                self.operation_history = self.operation_history[-self.max_history_size:]
            
        except Exception as e:
            self.handle_error(e, "清理缓存数据")
    
    def _log_memory_usage(self):
        """记录内存使用情况"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            
            self._error_logger.log_debug(
                f"内存使用: RSS={memory_info.rss / 1024 / 1024:.1f}MB, "
                f"VMS={memory_info.vms / 1024 / 1024:.1f}MB",
                "内存监控"
            )
            
        except ImportError:
            # psutil不可用时跳过
            pass
        except Exception as e:
            self.handle_error(e, "记录内存使用")
    
    def _setup_performance_optimization(self):
        """设置界面性能优化"""
        try:
            # 启用双缓冲
            self.setAttribute(Qt.WA_OpaquePaintEvent, True)
            
            # 优化重绘
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.setAlternatingRowColors(True)
                self.violation_list_widget.setShowGrid(False)  # 隐藏网格线提高性能
            
            # 设置更新策略
            self.setUpdatesEnabled(True)
            
        except Exception as e:
            self.handle_error(e, "设置性能优化")
    
    def resizeEvent(self, event):
        """处理窗口大小变化事件"""
        try:
            super().resizeEvent(event)
            
            # 延迟更新响应式元素
            if hasattr(self, 'ui_update_timer'):
                self.ui_update_timer.start(self.min_update_interval)
                
        except Exception as e:
            self.handle_error(e, "处理窗口大小变化")
    
    def closeEvent(self, event):
        """处理关闭事件"""
        try:
            # 确认关闭操作
            if self.violations and any(v.status == ViolationStatus.PENDING for v in self.violations):
                if not self.confirm_operation(
                    'close_with_pending',
                    "还有未确认的违例，确定要关闭吗？"
                ):
                    event.ignore()
                    return
            
            # 停止定时器
            if hasattr(self, 'memory_cleanup_timer'):
                self.memory_cleanup_timer.stop()
            
            if hasattr(self, 'ui_update_timer'):
                self.ui_update_timer.stop()
            
            # 清理资源
            self._cleanup_resources()
            
            # 记录性能统计
            self._log_performance_stats()
            
            event.accept()
            
        except Exception as e:
            self.handle_error(e, "处理关闭事件")
            event.accept()  # 即使出错也要关闭
    
    def _cleanup_resources(self):
        """清理资源"""
        try:
            # 清理进度处理器
            if hasattr(self, 'progress_handler'):
                self.progress_handler.stop_progress()
            
            # 清理缓存数据
            self.violations.clear()
            self.operation_history.clear()
            
            # 强制垃圾回收
            import gc
            gc.collect()
            
        except Exception as e:
            self.handle_error(e, "清理资源")
    
    def _log_performance_stats(self):
        """记录性能统计信息"""
        try:
            if hasattr(self, 'performance_monitor'):
                stats_report = self.performance_monitor.get_performance_report()
                self._error_logger.log_info(stats_report, "性能统计")
                
        except Exception as e:
            self.handle_error(e, "记录性能统计")
    
    def on_data_loaded(self, violations: list):
        """数据加载完成事件处理
        
        Args:
            violations: 加载的违例数据列表
        """
        try:
            # 更新内部数据
            self.violations = violations or []
            
            # 同步数据到各个组件
            self.sync_component_data()
            
            # 更新界面状态
            self.update_interface_state()
            
            print(f"数据加载完成，共 {len(self.violations)} 个违例")
            
        except Exception as e:
            print(f"处理数据加载事件时发生错误: {str(e)}")
    
    def on_export_completed(self, file_path: str):
        """导出完成事件处理
        
        Args:
            file_path: 导出文件路径
        """
        try:
            print(f"导出完成: {file_path}")
            
            # 可以在这里添加导出完成后的处理逻辑
            # 例如：显示成功消息、打开文件位置等
            
        except Exception as e:
            print(f"处理导出完成事件时发生错误: {str(e)}")
    
    def setup_component_interactions(self):
        """设置组件间的交互逻辑
        
        建立组件间的数据流和状态同步机制
        """
        try:
            # 设置文件选择组件与其他组件的交互
            if hasattr(self, 'file_selection_widget'):
                # 当文件选择成功时，启用其他组件
                self.file_selection_widget.file_selected.connect(self.enable_components_after_file_selection)
                
                # 当用例信息提取成功时，更新界面标题
                self.file_selection_widget.case_info_extracted.connect(self.update_window_title)
            
            # 设置复位时间组件与违例列表的双向交互
            if hasattr(self, 'reset_time_widget') and hasattr(self, 'violation_list_widget'):
                # 当违例数据变化时，更新复位时间组件的统计信息
                self.violation_list_widget.status_changed.connect(
                    lambda: self.update_reset_time_statistics()
                )
                
                # 当复位时间变化时，重新计算自动确认状态
                self.reset_time_widget.reset_time_applied.connect(self.apply_reset_time_auto_confirmation)
                self.reset_time_widget.reset_time_cleared.connect(self.clear_auto_confirmations)
            
            # 设置违例列表与确认对话框的交互
            if hasattr(self, 'violation_list_widget'):
                # 当违例确认完成时，更新列表显示
                # 这个连接会在确认对话框创建时动态建立
                pass
            
            # 设置组件间的状态同步
            self.setup_state_synchronization()
            
            # 设置数据流管道
            self.setup_data_flow_pipeline()
            
        except Exception as e:
            print(f"设置组件交互时发生错误: {str(e)}")
    
    def setup_data_flow_pipeline(self):
        """设置数据流管道
        
        建立组件间的数据传递管道，确保数据变化能够正确传播
        """
        try:
            # 创建数据变化事件队列
            from PyQt5.QtCore import QTimer
            
            self.data_change_timer = QTimer()
            self.data_change_timer.timeout.connect(self.process_data_changes)
            self.data_change_timer.setSingleShot(True)
            
            # 创建数据变化标志
            self.pending_data_changes = {
                'violations': False,
                'case_info': False,
                'reset_time': False
            }
            
        except Exception as e:
            print(f"设置数据流管道时发生错误: {str(e)}")
    
    def process_data_changes(self):
        """处理数据变化事件
        
        批量处理数据变化，避免频繁更新
        """
        try:
            # 检查哪些数据发生了变化
            if self.pending_data_changes['violations']:
                self.sync_violation_data()
                self.pending_data_changes['violations'] = False
            
            if self.pending_data_changes['case_info']:
                self.sync_case_info_data()
                self.pending_data_changes['case_info'] = False
            
            if self.pending_data_changes['reset_time']:
                self.sync_reset_time_data()
                self.pending_data_changes['reset_time'] = False
            
            # 最后更新界面状态
            self.update_interface_state()
            
        except Exception as e:
            print(f"处理数据变化时发生错误: {str(e)}")
    
    def trigger_data_change(self, data_type: str):
        """触发数据变化事件
        
        Args:
            data_type: 数据类型 ('violations', 'case_info', 'reset_time')
        """
        try:
            if data_type in self.pending_data_changes:
                self.pending_data_changes[data_type] = True
                
                # 延迟处理数据变化
                if hasattr(self, 'data_change_timer'):
                    self.data_change_timer.start(50)  # 50ms后处理
                    
        except Exception as e:
            print(f"触发数据变化事件时发生错误: {str(e)}")
    
    def sync_violation_data(self):
        """同步违例数据到各个组件"""
        try:
            # 同步到违例列表组件
            if hasattr(self, 'violation_list_widget') and self.violations:
                self.violation_list_widget.load_violations(self.violations)
            
            # 同步到复位时间组件
            if hasattr(self, 'reset_time_widget'):
                if hasattr(self.reset_time_widget, 'update_violations'):
                    self.reset_time_widget.update_violations(self.violations)
            
        except Exception as e:
            print(f"同步违例数据时发生错误: {str(e)}")
    
    def sync_case_info_data(self):
        """同步用例信息数据到各个组件"""
        try:
            # 同步到文件选择组件
            if hasattr(self, 'file_selection_widget') and self.case_info:
                if hasattr(self.file_selection_widget, 'update_case_info'):
                    self.file_selection_widget.update_case_info(self.case_info)
            
            # 更新窗口标题
            self.update_window_title()
            
        except Exception as e:
            print(f"同步用例信息数据时发生错误: {str(e)}")
    
    def sync_reset_time_data(self):
        """同步复位时间数据到各个组件"""
        try:
            # 同步到复位时间组件
            if hasattr(self, 'reset_time_widget') and self.reset_time_fs > 0:
                from .utils import TimeConverter
                try:
                    formatted_time = TimeConverter.format_time(self.reset_time_fs)
                    if hasattr(self.reset_time_widget, 'set_reset_time'):
                        self.reset_time_widget.set_reset_time(formatted_time)
                except Exception as e:
                    print(f"格式化复位时间失败: {str(e)}")
            
        except Exception as e:
            print(f"同步复位时间数据时发生错误: {str(e)}")
    
    def setup_state_synchronization(self):
        """设置状态同步机制
        
        确保各组件的状态保持一致
        """
        try:
            # 创建状态同步定时器（可选）
            from PyQt5.QtCore import QTimer
            
            self.state_sync_timer = QTimer()
            self.state_sync_timer.timeout.connect(self.sync_component_states)
            self.state_sync_timer.setSingleShot(True)  # 单次触发
            
        except Exception as e:
            print(f"设置状态同步时发生错误: {str(e)}")
    
    def sync_component_states(self):
        """同步组件状态
        
        确保所有组件的状态与当前数据保持一致
        """
        try:
            # 同步文件选择组件状态
            if hasattr(self, 'file_selection_widget'):
                self.file_selection_widget.update_state(self.case_info)
            
            # 同步复位时间组件状态
            if hasattr(self, 'reset_time_widget'):
                self.reset_time_widget.update_violations(self.violations)
                self.reset_time_widget.set_reset_time_fs(self.reset_time_fs)
            
            # 同步违例列表组件状态
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.refresh_display()
            
            # 同步导出按钮状态
            self.update_export_button_status()
            
        except Exception as e:
            print(f"同步组件状态时发生错误: {str(e)}")
    
    def trigger_state_sync(self):
        """触发状态同步
        
        延迟触发状态同步，避免频繁更新
        """
        try:
            if hasattr(self, 'state_sync_timer'):
                self.state_sync_timer.start(100)  # 100ms后同步
            else:
                # 如果没有定时器，直接同步
                self.sync_component_states()
                
        except Exception as e:
            print(f"触发状态同步时发生错误: {str(e)}")
    
    def handle_component_error(self, component_name: str, error: Exception):
        """处理组件错误
        
        Args:
            component_name: 组件名称
            error: 错误对象
        """
        try:
            error_msg = f"{component_name} 组件发生错误: {str(error)}"
            print(error_msg)
            
            # 显示错误提示（可选）
            if hasattr(error, 'show_to_user') and error.show_to_user:
                QMessageBox.warning(self, f"{component_name} 错误", str(error))
            
            # 尝试恢复组件状态
            self.recover_component_state(component_name)
            
        except Exception as e:
            print(f"处理组件错误时发生异常: {str(e)}")
    
    def recover_component_state(self, component_name: str):
        """恢复组件状态
        
        Args:
            component_name: 组件名称
        """
        try:
            if component_name == "file_selection_widget" and hasattr(self, 'file_selection_widget'):
                self.file_selection_widget.reset_state()
            elif component_name == "reset_time_widget" and hasattr(self, 'reset_time_widget'):
                self.reset_time_widget.reset_state()
            elif component_name == "violation_list_widget" and hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.reset_state()
                
        except Exception as e:
            print(f"恢复组件状态时发生错误: {str(e)}")
    
    def validate_component_interactions(self) -> bool:
        """验证组件交互是否正常
        
        Returns:
            bool: 交互是否正常
        """
        try:
            # 检查关键组件是否存在
            required_components = [
                'file_selection_widget',
                'reset_time_widget', 
                'violation_list_widget'
            ]
            
            for component in required_components:
                if not hasattr(self, component):
                    print(f"缺少必要组件: {component}")
                    return False
            
            # 检查信号连接是否正常（简单检查）
            if not hasattr(self.file_selection_widget, 'file_selected'):
                print("文件选择组件信号缺失")
                return False
            
            return True
            
        except Exception as e:
            print(f"验证组件交互时发生错误: {str(e)}")
            return False
            
            # 添加导出按钮组
            from PyQt5.QtWidgets import QMenu
            from PyQt5.QtCore import QSize
            
            # Excel导出按钮
            self.export_excel_button = QPushButton("📊 导出Excel")
            self.export_excel_button.setEnabled(False)
            self.export_excel_button.setMinimumWidth(120)
            self.export_excel_button.setMinimumHeight(35)
            self.export_excel_button.clicked.connect(lambda: self.export_results("excel"))
            self.export_excel_button.setToolTip("导出为Excel格式，支持丰富的格式和样式")
            self.export_excel_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #28a745;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 11px;
                }
                QPushButton:hover:enabled {
                    background-color: #218838;
                    transform: translateY(-1px);
                }
                QPushButton:pressed:enabled {
                    background-color: #1e7e34;
                }
                QPushButton:disabled {
                    background-color: #cccccc;
                    color: #666666;
                }
            """)
            button_layout.addWidget(self.export_excel_button)
            
            # CSV导出按钮
            self.export_csv_button = QPushButton("📄 导出CSV")
            self.export_csv_button.setEnabled(False)
            self.export_csv_button.setMinimumWidth(120)
            self.export_csv_button.setMinimumHeight(35)
            self.export_csv_button.clicked.connect(lambda: self.export_results("csv"))
            self.export_csv_button.setToolTip("导出为CSV格式，兼容性好，可用Excel打开")
            self.export_csv_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #17a2b8;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 11px;
                }
                QPushButton:hover:enabled {
                    background-color: #138496;
                    transform: translateY(-1px);
                }
                QPushButton:pressed:enabled {
                    background-color: #117a8b;
                }
                QPushButton:disabled {
                    background-color: #cccccc;
                    color: #666666;
                }
            """)
            button_layout.addWidget(self.export_csv_button)
            
            # 快速导出按钮（带下拉菜单）
            self.quick_export_button = QPushButton("⚡ 快速导出")
            self.quick_export_button.setEnabled(False)
            self.quick_export_button.setMinimumWidth(120)
            self.quick_export_button.setMinimumHeight(35)
            self.quick_export_button.setToolTip("使用默认设置快速导出")
            
            # 创建快速导出菜单
            quick_menu = QMenu(self)
            quick_menu.addAction("Excel (包含所有违例)", lambda: self._quick_export("excel", True))
            quick_menu.addAction("Excel (仅已确认)", lambda: self._quick_export("excel", False))
            quick_menu.addSeparator()
            quick_menu.addAction("CSV (包含所有违例)", lambda: self._quick_export("csv", True))
            quick_menu.addAction("CSV (仅已确认)", lambda: self._quick_export("csv", False))
            
            self.quick_export_button.setMenu(quick_menu)
            self.quick_export_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #ffc107;
                    color: #212529;
                    border: none;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 11px;
                }
                QPushButton:hover:enabled {
                    background-color: #e0a800;
                    transform: translateY(-1px);
                }
                QPushButton:pressed:enabled {
                    background-color: #d39e00;
                }
                QPushButton:disabled {
                    background-color: #cccccc;
                    color: #666666;
                }
                QPushButton::menu-indicator {
                    image: none;
                    width: 0px;
                }
            """)
            button_layout.addWidget(self.quick_export_button)
            
            # 关闭按钮
            close_button = QPushButton("关闭")
            close_button.clicked.connect(self.close)
            close_button.setMinimumWidth(100)
            close_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)
            button_layout.addWidget(close_button)
            
            main_layout.addLayout(button_layout)
            
            # 设置主布局
            self.setLayout(main_layout)
            
        except Exception as e:
            QMessageBox.critical(self, "界面初始化错误", 
                               f"初始化用户界面时发生错误:\n{str(e)}")
    
    def on_file_selected(self, file_path: str):
        """文件选择成功事件处理
        
        Args:
            file_path: 选择的文件路径
        """
        try:
            print(f"文件选择成功: {file_path}")
            
            # 清空当前违例数据
            self.clear_violation_list()
            
            # 开始解析文件
            self.load_violations_from_file(file_path)
            
        except Exception as e:
            error_msg = f"处理文件选择事件时发生错误: {str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "文件处理错误", error_msg)
    
    def on_case_info_extracted(self, case_info: CaseInfo):
        """用例信息提取成功事件处理
        
        Args:
            case_info: 提取的用例信息
        """
        try:
            self.case_info = case_info
            print(f"用例信息提取成功: {case_info.case_name}, corner: {case_info.corner}")
            
            # 触发用例信息数据变化
            self.trigger_data_change('case_info')
            
        except Exception as e:
            print(f"处理用例信息提取事件时发生错误: {str(e)}")
    
    def on_corner_selection_required(self, case_name: str):
        """需要corner选择事件处理
        
        Args:
            case_name: 用例名称
        """
        try:
            print(f"需要为用例 {case_name} 选择corner")
            
            # 这里可以显示corner选择对话框或在界面上显示选择控件
            # 具体实现取决于文件选择组件的设计
            
        except Exception as e:
            print(f"处理corner选择事件时发生错误: {str(e)}")
    
    def on_validation_error(self, error_msg: str):
        """验证错误事件处理
        
        Args:
            error_msg: 错误信息
        """
        try:
            print(f"验证错误: {error_msg}")
            QMessageBox.warning(self, "验证错误", error_msg)
            
            # 重置相关状态
            self.reset_interface_state()
            
        except Exception as e:
            print(f"处理验证错误事件时发生错误: {str(e)}")
    
    def on_reset_time_applied(self, reset_time_fs: int):
        """复位时间应用事件处理
        
        Args:
            reset_time_fs: 复位时间（飞秒）
        """
        try:
            self.reset_time_fs = reset_time_fs
            print(f"复位时间应用成功: {reset_time_fs} FS")
            
            # 触发复位时间数据变化
            self.trigger_data_change('reset_time')
            
        except Exception as e:
            print(f"处理复位时间应用事件时发生错误: {str(e)}")
    
    def on_reset_time_cleared(self):
        """复位时间清除事件处理"""
        try:
            self.reset_time_fs = 0
            print("复位时间已清除")
            
            # 触发复位时间数据变化
            self.trigger_data_change('reset_time')
            
        except Exception as e:
            print(f"处理复位时间清除事件时发生错误: {str(e)}")
    
    def on_reset_time_validation_error(self, error_msg: str):
        """复位时间验证错误事件处理
        
        Args:
            error_msg: 错误信息
        """
        try:
            print(f"复位时间验证错误: {error_msg}")
            QMessageBox.warning(self, "复位时间错误", error_msg)
            
        except Exception as e:
            print(f"处理复位时间验证错误事件时发生错误: {str(e)}")
    
    def on_violation_selected(self, violation: ViolationData):
        """违例选择事件处理
        
        Args:
            violation: 选择的违例数据
        """
        try:
            print(f"违例选择: NUM {violation.num}")
            
            # 可以在这里添加选择后的处理逻辑
            # 例如：显示详细信息、更新状态栏等
            
        except Exception as e:
            print(f"处理违例选择事件时发生错误: {str(e)}")
    
    def on_confirm_requested(self, violation: ViolationData):
        """违例确认请求事件处理
        
        Args:
            violation: 需要确认的违例数据
        """
        try:
            print(f"违例确认请求: NUM {violation.num}")
            
            # 显示确认对话框
            self.show_confirmation_dialog(violation)
            
        except Exception as e:
            print(f"处理违例确认请求事件时发生错误: {str(e)}")
    
    def on_detail_requested(self, violation: ViolationData):
        """违例详情查看请求事件处理
        
        Args:
            violation: 需要查看详情的违例数据
        """
        try:
            print(f"违例详情请求: NUM {violation.num}")
            
            # 显示详情对话框
            self.show_violation_detail(violation)
            
        except Exception as e:
            print(f"处理违例详情请求事件时发生错误: {str(e)}")
    
    def on_status_changed(self, violation: ViolationData):
        """违例状态变化事件处理
        
        Args:
            violation: 状态发生变化的违例数据
        """
        try:
            print(f"违例状态变化: NUM {violation.num}, 新状态: {violation.status}")
            
            # 更新内部数据
            self.update_violation_in_list(violation)
            
            # 触发违例数据变化
            self.trigger_data_change('violations')
            
        except Exception as e:
            print(f"处理违例状态变化事件时发生错误: {str(e)}")
    
    def handle_data_change(self):
        """处理数据变化的通用方法"""
        try:
            # 更新界面状态
            self.update_interface_state()
            
            # 触发状态同步
            self.trigger_state_sync()
            
        except Exception as e:
            print(f"处理数据变化时发生错误: {str(e)}")
    
    def clear_violation_list(self):
        """清空违例列表"""
        try:
            self.violations = []
            
            # 清空违例列表组件
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.clear()
            
            # 更新界面状态
            self.update_interface_state()
            
        except Exception as e:
            print(f"清空违例列表时发生错误: {str(e)}")
    
    def enable_components_after_file_selection(self):
        """文件选择后启用相关组件"""
        try:
            # 启用复位时间组件
            if hasattr(self, 'reset_time_widget'):
                self.reset_time_widget.setEnabled(True)
            
            # 启用违例列表组件
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.setEnabled(True)
            
            print("文件选择后组件已启用")
            
        except Exception as e:
            print(f"启用组件时发生错误: {str(e)}")
    
    def update_window_title(self):
        """更新窗口标题"""
        try:
            if self.case_info:
                title = f"时序违例确认工具 - {self.case_info.case_name}"
                if self.case_info.corner:
                    title += f" ({self.case_info.corner})"
                self.setWindowTitle(title)
            else:
                self.setWindowTitle("时序违例确认工具")
                
        except Exception as e:
            print(f"更新窗口标题时发生错误: {str(e)}")
    
    def refresh_violation_display(self):
        """刷新违例显示"""
        try:
            if hasattr(self, 'violation_list_widget') and self.violations:
                self.violation_list_widget.refresh_display()
                
        except Exception as e:
            print(f"刷新违例显示时发生错误: {str(e)}")
    
    def apply_reset_time_auto_confirmation(self, reset_time_fs: int):
        """应用复位时间自动确认逻辑
        
        Args:
            reset_time_fs: 复位时间（飞秒）
        """
        try:
            if not self.violations:
                return
            
            from .models import ViolationStatus
            
            # 遍历所有违例，标记复位期间的违例
            auto_confirmed_count = 0
            for violation in self.violations:
                if violation.time_fs <= reset_time_fs and violation.status == ViolationStatus.PENDING:
                    violation.status = ViolationStatus.AUTO_CONFIRMED
                    violation.reason = "复位期间时序违例，可以忽略"
                    violation.auto_confirmed = True
                    auto_confirmed_count += 1
            
            print(f"自动确认了 {auto_confirmed_count} 个复位期间的违例")
            
            # 刷新显示
            self.refresh_violation_display()
            
            # 更新统计信息
            self.update_reset_time_statistics()
            
        except Exception as e:
            print(f"应用复位时间自动确认时发生错误: {str(e)}")
    
    def clear_auto_confirmations(self):
        """清除自动确认状态"""
        try:
            if not self.violations:
                return
            
            from .models import ViolationStatus
            
            # 遍历所有违例，清除自动确认状态
            cleared_count = 0
            for violation in self.violations:
                if violation.auto_confirmed:
                    violation.status = ViolationStatus.PENDING
                    violation.reason = ""
                    violation.auto_confirmed = False
                    cleared_count += 1
            
            print(f"清除了 {cleared_count} 个自动确认状态")
            
            # 刷新显示
            self.refresh_violation_display()
            
            # 更新统计信息
            self.update_reset_time_statistics()
            
        except Exception as e:
            print(f"清除自动确认状态时发生错误: {str(e)}")
    
    def update_reset_time_statistics(self):
        """更新复位时间统计信息"""
        try:
            if hasattr(self, 'reset_time_widget') and hasattr(self.reset_time_widget, 'update_statistics'):
                self.reset_time_widget.update_statistics(self.violations)
                
        except Exception as e:
            print(f"更新复位时间统计信息时发生错误: {str(e)}")
    
    def update_export_button_status(self):
        """更新导出按钮状态"""
        try:
            # 检查是否有违例数据
            has_violations = bool(self.violations)
            
            # 更新导出按钮状态
            if hasattr(self, 'export_excel_button'):
                self.export_excel_button.setEnabled(has_violations)
            
            if hasattr(self, 'export_csv_button'):
                self.export_csv_button.setEnabled(has_violations)
            
            if hasattr(self, 'quick_export_button'):
                self.quick_export_button.setEnabled(has_violations)
                
        except Exception as e:
            print(f"更新导出按钮状态时发生错误: {str(e)}")
    
    def sync_component_data(self):
        """同步组件数据"""
        try:
            # 同步违例数据
            self.sync_violation_data()
            
            # 同步用例信息数据
            self.sync_case_info_data()
            
            # 同步复位时间数据
            self.sync_reset_time_data()
            
        except Exception as e:
            print(f"同步组件数据时发生错误: {str(e)}")
    
    def update_interface_state(self):
        """更新界面状态"""
        try:
            # 更新导出按钮状态
            self.update_export_button_status()
            
            # 更新窗口标题
            self.update_window_title()
            
            # 更新复位时间统计
            self.update_reset_time_statistics()
            
        except Exception as e:
            print(f"更新界面状态时发生错误: {str(e)}")
    
    def reset_interface_state(self):
        """重置界面状态"""
        try:
            # 清空数据
            self.violations = []
            self.case_info = None
            self.reset_time_fs = 0
            
            # 重置组件状态
            if hasattr(self, 'file_selection_widget'):
                self.file_selection_widget.reset_state()
            
            if hasattr(self, 'reset_time_widget'):
                self.reset_time_widget.reset_state()
                self.reset_time_widget.setEnabled(False)
            
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.clear()
                self.violation_list_widget.setEnabled(False)
            
            # 更新界面状态
            self.update_interface_state()
            
        except Exception as e:
            print(f"重置界面状态时发生错误: {str(e)}")
    
    def update_violation_in_list(self, updated_violation: ViolationData):
        """更新列表中的违例数据
        
        Args:
            updated_violation: 更新后的违例数据
        """
        try:
            for i, violation in enumerate(self.violations):
                if violation.num == updated_violation.num:
                    self.violations[i] = updated_violation
                    break
                    
        except Exception as e:
            print(f"更新违例数据时发生错误: {str(e)}")
    
    def show_confirmation_dialog(self, violation: ViolationData):
        """显示确认对话框
        
        Args:
            violation: 需要确认的违例数据
        """
        try:
            from .confirmation_dialog import ConfirmationDialog
            
            dialog = ConfirmationDialog(violation, self)
            
            # 连接确认完成信号
            dialog.confirmation_completed.connect(self.on_confirmation_completed)
            
            # 显示对话框
            dialog.exec_()
            
        except Exception as e:
            print(f"显示确认对话框时发生错误: {str(e)}")
            QMessageBox.critical(self, "对话框错误", f"显示确认对话框时发生错误:\n{str(e)}")
    
    def show_violation_detail(self, violation: ViolationData):
        """显示违例详情
        
        Args:
            violation: 需要查看详情的违例数据
        """
        try:
            # 创建详情显示对话框
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton
            
            dialog = QDialog(self)
            dialog.setWindowTitle(f"违例详情 - NUM {violation.num}")
            dialog.setMinimumSize(500, 400)
            
            layout = QVBoxLayout()
            
            # 创建详情文本
            detail_text = f"""
违例编号: {violation.num}
层级路径: {violation.hier}
原始时间: {violation.time_original}
时间(FS): {violation.time_fs:,}
检查信息: {violation.check}
状态: {violation.status.value}
确认人: {violation.confirmer or '未确认'}
确认理由: {violation.reason or '无'}
自动确认: {'是' if violation.auto_confirmed else '否'}
            """.strip()
            
            text_edit = QTextEdit()
            text_edit.setPlainText(detail_text)
            text_edit.setReadOnly(True)
            layout.addWidget(text_edit)
            
            # 关闭按钮
            close_button = QPushButton("关闭")
            close_button.clicked.connect(dialog.close)
            layout.addWidget(close_button)
            
            dialog.setLayout(layout)
            dialog.exec_()
            
        except Exception as e:
            print(f"显示违例详情时发生错误: {str(e)}")
            QMessageBox.critical(self, "详情显示错误", f"显示违例详情时发生错误:\n{str(e)}")
    
    def on_confirmation_completed(self, violation: ViolationData):
        """确认完成事件处理
        
        Args:
            violation: 确认完成的违例数据
        """
        try:
            print(f"违例确认完成: NUM {violation.num}, 状态: {violation.status}")
            
            # 更新违例数据
            self.update_violation_in_list(violation)
            
            # 刷新显示
            self.refresh_violation_display()
            
            # 更新界面状态
            self.update_interface_state()
            
        except Exception as e:
            print(f"处理确认完成事件时发生错误: {str(e)}")
    
    @monitor_performance("load_violations_from_file", global_performance_monitor)
    def load_violations_from_file(self, file_path: str):
        """从文件加载违例数据
        
        Args:
            file_path: 文件路径
        """
        progress_dialog = None
        try:
            from .violation_parser import ViolationParser
            from .exceptions import FileParseError, TimingViolationError
            import os
            
            # 验证文件存在性
            if not os.path.exists(file_path):
                raise FileParseError(file_path, "文件不存在")
            
            # 检查文件大小，如果文件较大则显示进度对话框
            file_size = os.path.getsize(file_path)
            show_progress = file_size > 1024 * 1024  # 1MB以上显示进度
            
            if show_progress:
                progress_dialog = create_progress_dialog("加载数据", "正在解析违例文件...", self)
                progress_dialog.show()
            
            # 创建解析器
            parser = ViolationParser()
            
            # 定义进度回调函数
            def progress_callback(current: int, total: int, message: str = ""):
                if progress_dialog:
                    percentage = int((current / total) * 100) if total > 0 else 0
                    return progress_dialog.update_progress(percentage, message)
                return True
            
            # 解析文件
            if show_progress:
                progress_dialog.update_progress(10, "开始解析文件...")
            
            violations = parser.parse_file(file_path, progress_callback=progress_callback if show_progress else None)
            
            if show_progress:
                progress_dialog.update_progress(90, "处理解析结果...")
            
            # 验证解析结果
            if violations is None:
                raise FileParseError(file_path, "解析结果为空")
            
            # 记录解析统计信息
            self._log_parsing_stats(file_path, violations)
            
            if show_progress:
                progress_dialog.update_progress(100, "加载完成")
            
            # 发送数据加载完成信号
            self.data_loaded.emit(violations)
            
        except FileParseError as e:
            self.handle_error(e, "文件解析失败", show_dialog=True)
        except TimingViolationError as e:
            self.handle_error(e, "时序违例处理失败", show_dialog=True)
        except Exception as e:
            # 包装未知异常
            wrapped_error = TimingViolationError(f"加载违例数据时发生未知错误: {str(e)}")
            self.handle_error(wrapped_error, "数据加载失败", show_dialog=True)
        finally:
            if progress_dialog:
                progress_dialog.close()
    
    def _log_parsing_stats(self, file_path: str, violations: list):
        """记录解析统计信息
        
        Args:
            file_path: 文件路径
            violations: 解析的违例列表
        """
        try:
            import os
            file_size = os.path.getsize(file_path)
            violation_count = len(violations) if violations else 0
            
            self._error_logger.log_info(
                f"文件解析完成: {os.path.basename(file_path)}, "
                f"文件大小: {file_size} bytes, "
                f"违例数量: {violation_count}",
                "文件解析统计"
            )
        except Exception as e:
            self._error_logger.log_warning(f"记录解析统计失败: {str(e)}", "统计记录")
    
    @monitor_performance("export_results", global_performance_monitor)
    def export_results(self, format_type: str):
        """导出结果
        
        Args:
            format_type: 导出格式 ("excel" 或 "csv")
        """
        progress_dialog = None
        try:
            from .export_manager import ExportManager
            from .exceptions import ExportError, ValidationError
            from PyQt5.QtWidgets import QFileDialog
            import os
            
            # 验证输入参数
            if format_type not in ["excel", "csv"]:
                raise ValidationError("导出格式", format_type, "不支持的导出格式")
            
            if not self.violations:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "导出错误", "没有可导出的违例数据")
                return
            
            # 选择保存文件
            if format_type == "excel":
                file_filter = "Excel文件 (*.xlsx)"
                default_name = f"{self.case_info.case_name if self.case_info else 'violations'}.xlsx"
            else:
                file_filter = "CSV文件 (*.csv)"
                default_name = f"{self.case_info.case_name if self.case_info else 'violations'}.csv"
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, f"导出{format_type.upper()}文件", default_name, file_filter
            )
            
            if not file_path:
                return
            
            # 检查文件路径权限
            try:
                # 尝试创建临时文件来检查写权限
                test_file = file_path + ".tmp"
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
            except Exception as e:
                raise ExportError(f"无法写入文件: {str(e)}", file_path, format_type)
            
            # 显示进度对话框（如果数据量较大）
            if len(self.violations) > 100:
                progress_dialog = create_progress_dialog("导出数据", f"正在导出{format_type.upper()}文件...", self)
                progress_dialog.show()
            
            # 创建导出管理器
            export_manager = ExportManager()
            
            # 定义进度回调函数
            def progress_callback(current: int, total: int, message: str = ""):
                if progress_dialog:
                    percentage = int((current / total) * 100) if total > 0 else 0
                    return progress_dialog.update_progress(percentage, message)
                return True
            
            # 执行导出
            if progress_dialog:
                progress_dialog.update_progress(10, "准备导出数据...")
            
            if format_type == "excel":
                export_manager.export_to_excel(
                    self.violations, file_path, self.case_info,
                    progress_callback=progress_callback if progress_dialog else None
                )
            else:
                export_manager.export_to_csv(
                    self.violations, file_path, self.case_info,
                    progress_callback=progress_callback if progress_dialog else None
                )
            
            if progress_dialog:
                progress_dialog.update_progress(100, "导出完成")
            
            # 验证导出文件
            if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
                raise ExportError("导出文件为空或不存在", file_path, format_type)
            
            # 记录导出统计信息
            self._log_export_stats(file_path, format_type, len(self.violations))
            
            # 发送导出完成信号
            self.export_completed.emit(file_path)
            
            # 显示成功消息
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, "导出成功", f"文件已成功导出到:\n{file_path}")
            
        except ExportError as e:
            self.handle_error(e, "文件导出失败", show_dialog=True)
        except ValidationError as e:
            self.handle_error(e, "导出参数验证失败", show_dialog=True)
        except Exception as e:
            # 包装未知异常
            wrapped_error = ExportError(f"导出时发生未知错误: {str(e)}", file_path if 'file_path' in locals() else None, format_type)
            self.handle_error(wrapped_error, "导出失败", show_dialog=True)
        finally:
            if progress_dialog:
                progress_dialog.close()
    
    def _log_export_stats(self, file_path: str, format_type: str, violation_count: int):
        """记录导出统计信息
        
        Args:
            file_path: 导出文件路径
            format_type: 导出格式
            violation_count: 违例数量
        """
        try:
            import os
            file_size = os.path.getsize(file_path)
            
            self._error_logger.log_info(
                f"文件导出完成: {os.path.basename(file_path)}, "
                f"格式: {format_type.upper()}, "
                f"文件大小: {file_size} bytes, "
                f"违例数量: {violation_count}",
                "文件导出统计"
            )
        except Exception as e:
            self._error_logger.log_warning(f"记录导出统计失败: {str(e)}", "统计记录")
    
    def _quick_export(self, format_type: str, include_all: bool):
        """快速导出
        
        Args:
            format_type: 导出格式
            include_all: 是否包含所有违例
        """
        try:
            # 这里可以实现快速导出逻辑
            # 使用默认设置和文件名
            self.export_results(format_type)
            
        except Exception as e:
            print(f"快速导出时发生错误: {str(e)}")
    
    def on_case_info_extracted(self, case_info: CaseInfo):
        """用例信息提取成功事件处理
        
        Args:
            case_info: 提取的用例信息
        """
        self.case_info = case_info
        print(f"用例信息提取成功: {case_info.case_name}, Corner: {case_info.corner}")
        
        # 更新导出按钮状态
        self.update_export_button_status()
    
    def on_corner_selection_required(self, case_name: str):
        """需要corner选择事件处理
        
        Args:
            case_name: 用例名称
        """
        print(f"需要为用例 {case_name} 选择Corner")
        
        # 更新导出按钮状态
        self.update_export_button_status()
    
    def on_validation_error(self, error_message: str):
        """验证错误事件处理
        
        Args:
            error_message: 错误信息
        """
        print(f"文件验证错误: {error_message}")
        
        # 清除用例信息
        self.case_info = None
        
        # 更新导出按钮状态
        self.update_export_button_status()
    
    def on_reset_time_applied(self, reset_time_fs: int):
        """复位时间应用事件处理
        
        Args:
            reset_time_fs: 复位时间（飞秒）
        """
        try:
            self.reset_time_fs = reset_time_fs
            print(f"复位时间已设置: {reset_time_fs} fs")
            
            # 如果有违例数据，重新计算自动确认
            if self.violations:
                self._apply_reset_time_auto_confirmation()
                
        except Exception as e:
            print(f"应用复位时间时发生错误: {str(e)}")
    
    def on_reset_time_cleared(self):
        """复位时间清除事件处理"""
        try:
            self.reset_time_fs = 0
            print("复位时间已清除")
            
            # 清除自动确认状态
            if self.violations:
                self._clear_auto_confirmations()
                
        except Exception as e:
            print(f"清除复位时间时发生错误: {str(e)}")
    
    def on_reset_time_validation_error(self, error_message: str):
        """复位时间验证错误事件处理
        
        Args:
            error_message: 错误信息
        """
        QMessageBox.warning(self, "复位时间验证错误", error_message)
    
    def on_violation_selected(self, violation):
        """违例选择事件处理
        
        Args:
            violation: 选择的违例数据
        """
        try:
            print(f"选择了违例: NUM={violation.num}, Hier={violation.hier}")
            # 可以在这里添加选择后的处理逻辑
            
        except Exception as e:
            print(f"处理违例选择事件时发生错误: {str(e)}")
    
    def on_confirm_requested(self, violation):
        """违例确认请求事件处理
        
        Args:
            violation: 要确认的违例数据
        """
        try:
            self.confirm_violation(violation)
            
        except Exception as e:
            QMessageBox.critical(self, "确认错误", f"处理违例确认时发生错误:\n{str(e)}")
    
    def on_detail_requested(self, violation):
        """违例详情请求事件处理
        
        Args:
            violation: 要查看详情的违例数据
        """
        try:
            self._show_violation_detail(violation)
            
        except Exception as e:
            QMessageBox.critical(self, "详情显示错误", f"显示违例详情时发生错误:\n{str(e)}")
    
    def on_status_changed(self, violation):
        """违例状态变更事件处理
        
        Args:
            violation: 状态变更的违例数据
        """
        try:
            print(f"违例状态变化: NUM={violation.num}, 新状态={violation.get_status_display()}")
            
            # 更新导出按钮状态
            self.update_export_button_status()
            
        except Exception as e:
            print(f"处理违例状态变更时发生错误: {str(e)}")
    
    def _quick_export(self, format_type: str, include_all: bool):
        """快速导出功能
        
        Args:
            format_type: 导出格式 ("excel" 或 "csv")
            include_all: 是否包含所有违例
        """
        try:
            if not self.violations:
                QMessageBox.warning(self, "导出警告", "没有可导出的违例数据")
                return
            
            # 使用默认设置进行导出
            from PyQt5.QtWidgets import QFileDialog
            from .export_manager import ExportManager
            from .models import ExportConfig
            
            # 创建导出管理器
            export_manager = ExportManager()
            
            # 获取用例信息
            case_info = {}
            if self.case_info:
                case_info['case_name'] = self.case_info.case_name
                case_info['corner'] = self.case_info.corner
            
            # 生成默认文件名
            default_filename = export_manager.get_default_filename(case_info, format_type)
            
            # 设置文件对话框过滤器
            if format_type == "excel":
                file_filter = "Excel文件 (*.xlsx);;所有文件 (*)"
                default_extension = ".xlsx"
            else:
                file_filter = "CSV文件 (*.csv);;所有文件 (*)"
                default_extension = ".csv"
            
            # 显示文件保存对话框
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                f"快速导出{format_type.upper()}文件",
                default_filename,
                file_filter
            )
            
            if not file_path:
                return  # 用户取消了保存
            
            # 确保文件有正确的扩展名
            if not file_path.lower().endswith(default_extension.lower()):
                file_path += default_extension
            
            # 创建导出配置
            config = ExportConfig(
                format_type=format_type,
                include_auto_confirmed=include_all,
                only_confirmed=not include_all,
                file_path=file_path
            )
            
            # 执行导出
            result_path = export_manager.export_violations(self.violations, config, case_info)
            
            # 显示成功消息
            QMessageBox.information(
                self, 
                "快速导出成功", 
                f"文件已成功导出到:\n{result_path}"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "快速导出失败", f"快速导出时发生错误:\n{str(e)}")
    
    def _apply_reset_time_auto_confirmation(self):
        """应用复位时间自动确认"""
        try:
            if self.reset_time_fs <= 0:
                return
            
            auto_confirmed_count = 0
            for violation in self.violations:
                if violation.time_fs < self.reset_time_fs:
                    violation.status = "AUTO_CONFIRMED"
                    violation.reason = "复位期间时序违例，可以忽略"
                    violation.auto_confirmed = True
                    auto_confirmed_count += 1
            
            if auto_confirmed_count > 0:
                print(f"自动确认了 {auto_confirmed_count} 个复位期间的违例")
                # 刷新违例列表显示
                if hasattr(self, 'violation_list_widget'):
                    self.violation_list_widget.load_violations(self.violations)
                    
        except Exception as e:
            print(f"应用复位时间自动确认时发生错误: {str(e)}")
    
    def _clear_auto_confirmations(self):
        """清除自动确认状态"""
        try:
            cleared_count = 0
            for violation in self.violations:
                if violation.auto_confirmed:
                    violation.status = "PENDING"
                    violation.reason = ""
                    violation.auto_confirmed = False
                    cleared_count += 1
            
            if cleared_count > 0:
                print(f"清除了 {cleared_count} 个自动确认状态")
                # 刷新违例列表显示
                if hasattr(self, 'violation_list_widget'):
                    self.violation_list_widget.load_violations(self.violations)
                    
        except Exception as e:
            print(f"清除自动确认状态时发生错误: {str(e)}")
    
    def _show_violation_detail(self, violation):
        """显示违例详情
        
        Args:
            violation: 违例数据
        """
        try:
            # 获取状态显示文本
            status_display = violation.status
            if hasattr(violation, 'get_status_display'):
                status_display = violation.get_status_display()
            
            detail_text = f"""违例详细信息:

NUM: {violation.num}
Hier: {violation.hier}
Time: {violation.time_original} ({violation.time_fs} fs)
Check: {violation.check}
状态: {status_display}
确认人: {violation.confirmer or '未确认'}
理由/解决方案: {violation.reason or '无'}
自动确认: {'是' if violation.auto_confirmed else '否'}"""
            
            QMessageBox.information(self, f"违例详情 - NUM {violation.num}", detail_text)
            
        except Exception as e:
            print(f"显示违例详情时发生错误: {str(e)}")
            QMessageBox.warning(self, "详情显示错误", f"显示违例详情时发生错误:\n{str(e)}")
    
    def _on_violation_confirmed(self, violation, confirmation_result):
        """违例确认完成事件处理
        
        Args:
            violation: 确认的违例数据
            confirmation_result: 确认结果
        """
        try:
            # 更新违例状态
            violation.status = confirmation_result.status
            violation.confirmer = confirmation_result.confirmer
            violation.reason = confirmation_result.reason
            
            print(f"违例确认完成: NUM={violation.num}, 状态={violation.status}")
            
            # 刷新违例列表显示
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.load_violations(self.violations)
            
            # 发送状态变化信号
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.status_changed.emit(violation)
            
        except Exception as e:
            print(f"处理违例确认完成事件时发生错误: {str(e)}")
    
    def _on_confirmation_cancelled(self, violation):
        """违例确认取消事件处理
        
        Args:
            violation: 取消确认的违例数据
        """
        try:
            print(f"违例确认取消: NUM={violation.num}")
            # 可以在这里添加取消确认后的处理逻辑
            
        except Exception as e:
            print(f"处理违例确认取消事件时发生错误: {str(e)}")
    
    def load_file(self, file_path: str):
        """加载时序违例文件
        
        Args:
            file_path: 文件路径
            
        Note:
            此方法将在后续任务中实现
        """
        # 使用文件选择组件设置文件路径
        if hasattr(self, 'file_selection_widget'):
            self.file_selection_widget.set_file_path(file_path)
        else:
            # 占位实现
            QMessageBox.information(self, "提示", 
                                  f"文件加载功能将在后续任务中实现\n文件路径: {file_path}")
    
    def set_reset_time(self, reset_time: int):
        """设置复位时间
        
        Args:
            reset_time: 复位时间（飞秒）
        """
        self.reset_time_fs = reset_time
        
        # 更新复位时间组件
        if hasattr(self, 'reset_time_widget'):
            from .utils import TimeConverter
            try:
                formatted_time = TimeConverter.format_time(reset_time)
                self.reset_time_widget.set_reset_time(formatted_time)
            except Exception as e:
                print(f"设置复位时间失败: {str(e)}")
    
    def confirm_violation(self, violation: ViolationData):
        """确认违例
        
        Args:
            violation: 要确认的违例数据
        """
        try:
            # 导入确认对话框
            from .confirmation_dialog import ConfirmationDialog
            
            # 创建确认对话框
            dialog = ConfirmationDialog(violation, self)
            
            # 连接确认完成信号
            dialog.violation_confirmed.connect(self._on_violation_confirmed)
            dialog.confirmation_cancelled.connect(self._on_confirmation_cancelled)
            
            # 显示对话框
            dialog.show()
            
        except Exception as e:
            error_msg = f"创建确认对话框失败: {str(e)}"
            print(f"确认对话框错误: {error_msg}")
            QMessageBox.critical(self, "确认错误", error_msg)
    
    def update_interface_state(self):
        """更新界面状态
        
        根据当前数据状态更新各个组件的启用/禁用状态
        """
        try:
            # 检查是否有违例数据
            has_violations = len(self.violations) > 0
            
            # 检查是否有用例信息
            has_case_info = self.case_info is not None
            
            # 更新导出按钮状态
            export_enabled = has_violations and has_case_info
            self.export_excel_button.setEnabled(export_enabled)
            self.export_csv_button.setEnabled(export_enabled)
            self.quick_export_button.setEnabled(export_enabled)
            
            # 更新复位时间组件状态
            if hasattr(self, 'reset_time_widget'):
                self.reset_time_widget.setEnabled(has_violations)
            
            # 更新违例列表组件状态
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.setEnabled(has_violations)
                
        except Exception as e:
            print(f"更新界面状态时发生错误: {str(e)}")
    
    def sync_component_data(self):
        """同步组件间的数据
        
        确保各个组件之间的数据保持一致
        """
        try:
            # 同步违例列表数据
            if hasattr(self, 'violation_list_widget'):
                if self.violations:
                    self.violation_list_widget.load_violations(self.violations)
                else:
                    # 清空列表
                    self.violation_list_widget.clear()
                    self.violation_list_widget.setRowCount(0)
            
            # 同步用例信息到文件选择组件
            if hasattr(self, 'file_selection_widget') and self.case_info:
                if hasattr(self.file_selection_widget, 'update_case_info'):
                    self.file_selection_widget.update_case_info(self.case_info)
                elif hasattr(self.file_selection_widget, 'set_case_info'):
                    self.file_selection_widget.set_case_info(self.case_info)
            
            # 同步复位时间状态
            if hasattr(self, 'reset_time_widget'):
                if self.reset_time_fs > 0:
                    try:
                        from .utils import TimeConverter
                        formatted_time = TimeConverter.format_time(self.reset_time_fs)
                        if hasattr(self.reset_time_widget, 'set_reset_time'):
                            self.reset_time_widget.set_reset_time(formatted_time)
                    except Exception as e:
                        print(f"同步复位时间时发生错误: {str(e)}")
                
                # 更新违例统计信息
                if hasattr(self.reset_time_widget, 'update_violations'):
                    self.reset_time_widget.update_violations(self.violations)
                
        except Exception as e:
            print(f"同步组件数据时发生错误: {str(e)}")
    
    def handle_data_change(self):
        """处理数据变化事件
        
        当违例数据或用例信息发生变化时调用此方法
        """
        try:
            # 更新界面状态
            self.update_interface_state()
            
            # 同步组件数据
            self.sync_component_data()
            
            # 更新导出按钮状态
            self.update_export_button_status()
            
            # 触发状态同步
            self.trigger_state_sync()
            
        except Exception as e:
            print(f"处理数据变化时发生错误: {str(e)}")
    
    def clear_violation_list(self):
        """清空违例列表
        
        当选择新文件时清空当前违例列表
        """
        try:
            # 清空内部数据
            self.violations = []
            
            # 清空违例列表组件
            if hasattr(self, 'violation_list_widget'):
                if hasattr(self.violation_list_widget, 'clear'):
                    self.violation_list_widget.clear()
                else:
                    self.violation_list_widget.setRowCount(0)
            
            # 重置复位时间
            self.reset_time_fs = 0
            if hasattr(self, 'reset_time_widget'):
                if hasattr(self.reset_time_widget, 'reset_state'):
                    self.reset_time_widget.reset_state()
            
            # 更新界面状态
            self.update_interface_state()
            
        except Exception as e:
            print(f"清空违例列表时发生错误: {str(e)}")
    
    def enable_components_after_file_selection(self, file_path: str):
        """文件选择后启用相关组件
        
        Args:
            file_path: 选择的文件路径
        """
        try:
            # 启用复位时间组件
            if hasattr(self, 'reset_time_widget'):
                self.reset_time_widget.setEnabled(True)
            
            # 启用违例列表组件
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.setEnabled(True)
            
            # 开始加载违例数据
            self.load_violations_from_file(file_path)
            
            print(f"文件选择完成，组件已启用: {file_path}")
            
        except Exception as e:
            print(f"启用组件时发生错误: {str(e)}")
    
    def update_window_title(self):
        """更新窗口标题
        
        根据当前用例信息更新窗口标题
        """
        try:
            base_title = "时序违例确认工具"
            
            if self.case_info:
                title = f"{base_title} - {self.case_info.case_name}"
                if self.case_info.corner:
                    title += f" ({self.case_info.corner})"
                self.setWindowTitle(title)
            else:
                self.setWindowTitle(base_title)
                
        except Exception as e:
            print(f"更新窗口标题时发生错误: {str(e)}")
    
    def update_reset_time_statistics(self):
        """更新复位时间组件的统计信息"""
        try:
            if hasattr(self, 'reset_time_widget') and self.violations:
                if hasattr(self.reset_time_widget, 'update_statistics'):
                    self.reset_time_widget.update_statistics(self.violations)
                    
        except Exception as e:
            print(f"更新复位时间统计信息时发生错误: {str(e)}")
    
    def apply_reset_time_auto_confirmation(self, reset_time_fs: int):
        """应用复位时间自动确认
        
        Args:
            reset_time_fs: 复位时间（飞秒）
        """
        try:
            if reset_time_fs <= 0 or not self.violations:
                return
            
            auto_confirmed_count = 0
            for violation in self.violations:
                if hasattr(violation, 'time_fs') and violation.time_fs < reset_time_fs:
                    # 检查是否已经是手动确认状态
                    if not violation.auto_confirmed and violation.status == "PENDING":
                        violation.status = "AUTO_CONFIRMED"
                        violation.reason = "复位期间时序违例，可以忽略"
                        violation.auto_confirmed = True
                        auto_confirmed_count += 1
            
            if auto_confirmed_count > 0:
                print(f"自动确认了 {auto_confirmed_count} 个复位期间的违例")
                # 刷新违例列表显示
                if hasattr(self, 'violation_list_widget'):
                    self.violation_list_widget.load_violations(self.violations)
                    
        except Exception as e:
            print(f"应用复位时间自动确认时发生错误: {str(e)}")
    
    def clear_auto_confirmations(self):
        """清除自动确认状态"""
        try:
            cleared_count = 0
            for violation in self.violations:
                if violation.auto_confirmed:
                    violation.status = "PENDING"
                    violation.reason = ""
                    violation.auto_confirmed = False
                    cleared_count += 1
            
            if cleared_count > 0:
                print(f"清除了 {cleared_count} 个自动确认状态")
                # 刷新违例列表显示
                if hasattr(self, 'violation_list_widget'):
                    self.violation_list_widget.load_violations(self.violations)
                    
        except Exception as e:
            print(f"清除自动确认状态时发生错误: {str(e)}")
    
    def refresh_violation_display(self):
        """刷新违例显示
        
        当复位时间变化时刷新违例列表显示
        """
        try:
            if hasattr(self, 'violation_list_widget') and self.violations:
                self.violation_list_widget.load_violations(self.violations)
                
        except Exception as e:
            print(f"刷新违例显示时发生错误: {str(e)}")
    
    def update_export_button_status(self):
        """更新导出按钮状态
        
        根据当前数据状态更新导出按钮的启用/禁用状态
        """
        try:
            # 检查是否有违例数据和用例信息
            has_violations = len(self.violations) > 0
            has_case_info = self.case_info is not None
            
            # 导出按钮需要同时有违例数据和用例信息
            export_enabled = has_violations and has_case_info
            
            # 更新导出按钮状态
            if hasattr(self, 'export_excel_button'):
                self.export_excel_button.setEnabled(export_enabled)
            
            if hasattr(self, 'export_csv_button'):
                self.export_csv_button.setEnabled(export_enabled)
            
            if hasattr(self, 'quick_export_button'):
                self.quick_export_button.setEnabled(export_enabled)
                
        except Exception as e:
            print(f"更新导出按钮状态时发生错误: {str(e)}")
    
    def export_results(self, format_type: str):
        """导出结果
        
        Args:
            format_type: 导出格式 ("excel" 或 "csv")
        """
        try:
            if not self.violations:
                QMessageBox.warning(self, "导出警告", "没有可导出的违例数据")
                return
            
            # 导入导出管理器
            from .export_manager import ExportManager
            from .models import ExportConfig
            from PyQt5.QtWidgets import QFileDialog
            
            # 创建导出管理器
            export_manager = ExportManager()
            
            # 获取用例信息
            case_info = {}
            if self.case_info:
                case_info['case_name'] = self.case_info.case_name
                case_info['corner'] = self.case_info.corner
            
            # 生成默认文件名
            default_filename = export_manager.get_default_filename(case_info, format_type)
            
            # 设置文件对话框过滤器
            if format_type == "excel":
                file_filter = "Excel文件 (*.xlsx);;所有文件 (*)"
                default_extension = ".xlsx"
            else:
                file_filter = "CSV文件 (*.csv);;所有文件 (*)"
                default_extension = ".csv"
            
            # 显示文件保存对话框
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                f"导出{format_type.upper()}文件",
                default_filename,
                file_filter
            )
            
            if not file_path:
                return  # 用户取消了保存
            
            # 确保文件有正确的扩展名
            if not file_path.lower().endswith(default_extension.lower()):
                file_path += default_extension
            
            # 创建导出配置
            config = ExportConfig(
                format_type=format_type,
                include_auto_confirmed=True,
                only_confirmed=False,
                file_path=file_path
            )
            
            # 执行导出
            result_path = export_manager.export_violations(self.violations, config, case_info)
            
            # 发送导出完成信号
            self.export_completed.emit(result_path)
            
            # 显示成功消息
            QMessageBox.information(
                self, 
                "导出成功", 
                f"文件已成功导出到:\n{result_path}"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"导出时发生错误:\n{str(e)}")
    
    def _on_violation_confirmed(self, violation: ViolationData):
        """违例确认完成事件处理
        
        Args:
            violation: 确认完成的违例数据
        """
        try:
            # 更新违例列表中对应的违例数据
            for i, v in enumerate(self.violations):
                if v.num == violation.num:
                    self.violations[i] = violation
                    break
            
            # 刷新违例列表显示
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.load_violations(self.violations)
            
            # 更新界面状态
            self.update_interface_state()
            
            print(f"违例确认完成: NUM={violation.num}, 状态={violation.status}")
            
        except Exception as e:
            print(f"处理违例确认完成事件时发生错误: {str(e)}")
    
    def _on_confirmation_cancelled(self):
        """确认取消事件处理"""
        try:
            print("用户取消了违例确认")
            
        except Exception as e:
            print(f"处理确认取消事件时发生错误: {str(e)}")
    
    def load_violations_from_file(self, file_path: str):
        """从文件加载违例数据
        
        Args:
            file_path: 违例日志文件路径
        """
        try:
            print(f"开始从文件加载违例数据: {file_path}")
            
            # 使用ViolationParser解析文件
            from .violation_parser import ViolationParser
            
            parser = ViolationParser()
            violations = parser.parse_file(file_path)
            
            if violations:
                # 更新违例数据
                self.violations = violations
                print(f"成功加载 {len(violations)} 个违例")
                
                # 发送数据加载完成信号
                self.data_loaded.emit(self.violations)
                
                # 如果已设置复位时间，应用自动确认
                if self.reset_time_fs > 0:
                    self.apply_reset_time_auto_confirmation(self.reset_time_fs)
                    
            else:
                print("未找到违例数据")
                QMessageBox.information(self, "信息", "文件中未找到违例数据")
                
        except Exception as e:
            error_msg = f"加载违例数据时发生错误: {str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "加载错误", error_msg)
    
    def get_violation_statistics(self) -> dict:
        """获取违例统计信息
        
        Returns:
            dict: 包含各种统计信息的字典
        """
        try:
            if not self.violations:
                return {
                    'total': 0,
                    'pending': 0,
                    'confirmed_ok': 0,
                    'confirmed_issue': 0,
                    'auto_confirmed': 0
                }
            
            stats = {
                'total': len(self.violations),
                'pending': 0,
                'confirmed_ok': 0,
                'confirmed_issue': 0,
                'auto_confirmed': 0
            }
            
            for violation in self.violations:
                if violation.status == "PENDING":
                    stats['pending'] += 1
                elif violation.status == "CONFIRMED_OK":
                    stats['confirmed_ok'] += 1
                elif violation.status == "CONFIRMED_ISSUE":
                    stats['confirmed_issue'] += 1
                elif violation.status == "AUTO_CONFIRMED":
                    stats['auto_confirmed'] += 1
            
            return stats
            
        except Exception as e:
            print(f"获取违例统计信息时发生错误: {str(e)}")
            return {}
    
    def validate_data_consistency(self) -> bool:
        """验证数据一致性
        
        Returns:
            bool: 数据是否一致
        """
        try:
            # 检查违例数据的完整性
            if self.violations:
                for violation in self.violations:
                    if not hasattr(violation, 'num') or not hasattr(violation, 'status'):
                        print(f"违例数据不完整: {violation}")
                        return False
            
            # 检查用例信息的完整性
            if self.case_info:
                if not hasattr(self.case_info, 'case_name') or not self.case_info.case_name:
                    print("用例信息不完整")
                    return False
            
            return True
            
        except Exception as e:
            print(f"验证数据一致性时发生错误: {str(e)}")
            return False
    
    def reset_dialog_state(self):
        """重置对话框状态
        
        清除所有数据并重置界面到初始状态
        """
        try:
            # 清除数据
            self.violations = []
            self.case_info = None
            self.reset_time_fs = 0
            
            # 重置各个组件
            if hasattr(self, 'file_selection_widget'):
                if hasattr(self.file_selection_widget, 'reset_state'):
                    self.file_selection_widget.reset_state()
            
            if hasattr(self, 'reset_time_widget'):
                if hasattr(self.reset_time_widget, 'reset_state'):
                    self.reset_time_widget.reset_state()
            
            if hasattr(self, 'violation_list_widget'):
                if hasattr(self.violation_list_widget, 'clear'):
                    self.violation_list_widget.clear()
                else:
                    self.violation_list_widget.setRowCount(0)
            
            # 更新界面状态
            self.update_interface_state()
            
            # 重置窗口标题
            self.setWindowTitle("时序违例确认工具")
            
            print("对话框状态已重置")
            
        except Exception as e:
            print(f"重置对话框状态时发生错误: {str(e)}")
    
    def closeEvent(self, event):
        """关闭事件处理
        
        Args:
            event: 关闭事件
        """
        try:
            # 检查是否有未保存的更改
            if self.violations:
                pending_count = sum(1 for v in self.violations if v.status == "PENDING")
                if pending_count > 0:
                    reply = QMessageBox.question(
                        self,
                        "确认关闭",
                        f"还有 {pending_count} 个违例未确认，确定要关闭吗？",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )
                    
                    if reply == QMessageBox.No:
                        event.ignore()
                        return
            
            # 清理资源
            self.cleanup_resources()
            
            # 接受关闭事件
            event.accept()
            
        except Exception as e:
            print(f"处理关闭事件时发生错误: {str(e)}")
            event.accept()  # 即使出错也要关闭
    
    def cleanup_resources(self):
        """清理资源"""
        try:
            # 停止定时器
            if hasattr(self, 'data_change_timer'):
                self.data_change_timer.stop()
            
            if hasattr(self, 'state_sync_timer'):
                self.state_sync_timer.stop()
            
            # 清理组件资源
            if hasattr(self, 'file_selection_widget'):
                if hasattr(self.file_selection_widget, 'cleanup'):
                    self.file_selection_widget.cleanup()
            
            if hasattr(self, 'reset_time_widget'):
                if hasattr(self.reset_time_widget, 'cleanup'):
                    self.reset_time_widget.cleanup()
            
            if hasattr(self, 'violation_list_widget'):
                if hasattr(self.violation_list_widget, 'cleanup'):
                    self.violation_list_widget.cleanup()
            
            print("资源清理完成")
            
        except Exception as e:
            print(f"清理资源时发生错误: {str(e)}")
    
    def clear_auto_confirmations(self):
        """清除自动确认状态"""
        try:
            if not self.violations:
                return
            
            cleared_count = 0
            for violation in self.violations:
                if hasattr(violation, 'auto_confirmed') and violation.auto_confirmed:
                    violation.status = "PENDING"
                    violation.reason = ""
                    violation.auto_confirmed = False
                    cleared_count += 1
            
            if cleared_count > 0:
                print(f"清除了 {cleared_count} 个自动确认状态")
                # 触发违例数据变化
                self.trigger_data_change('violations')
                
        except Exception as e:
            print(f"清除自动确认状态时发生错误: {str(e)}")
    
    def _on_violation_confirmed(self, violation: ViolationData):
        """违例确认完成事件处理
        
        Args:
            violation: 确认完成的违例数据
        """
        try:
            # 更新内部数据中对应的违例
            for i, v in enumerate(self.violations):
                if hasattr(v, 'num') and hasattr(violation, 'num') and v.num == violation.num:
                    self.violations[i] = violation
                    break
            
            # 刷新违例列表显示
            self.refresh_violation_display()
            
            # 更新界面状态
            self.update_interface_state()
            
            print(f"违例确认完成: NUM={violation.num}, 状态={getattr(violation, 'status', 'UNKNOWN')}")
            
        except Exception as e:
            print(f"处理违例确认完成事件时发生错误: {str(e)}")
    
    def _on_confirmation_cancelled(self):
        """确认取消事件处理"""
        try:
            print("用户取消了违例确认")
            # 可以在这里添加取消确认后的处理逻辑
            
        except Exception as e:
            print(f"处理确认取消事件时发生错误: {str(e)}")
    
    def refresh_violation_display(self):
        """刷新违例显示
        
        重新加载和显示违例列表
        """
        try:
            if hasattr(self, 'violation_list_widget') and self.violations:
                # 保存当前选择
                current_selection = None
                if hasattr(self.violation_list_widget, 'get_selected_violation'):
                    current_selection = self.violation_list_widget.get_selected_violation()
                
                # 重新加载数据
                self.violation_list_widget.load_violations(self.violations)
                
                # 恢复选择
                if current_selection and hasattr(self.violation_list_widget, 'select_violation'):
                    self.violation_list_widget.select_violation(current_selection)
                    
        except Exception as e:
            print(f"刷新违例显示时发生错误: {str(e)}")
    
    def validate_component_state(self) -> bool:
        """验证组件状态
        
        Returns:
            bool: 如果所有组件状态有效返回True，否则返回False
        """
        try:
            # 检查必要的组件是否存在
            required_components = [
                'file_selection_widget',
                'violation_list_widget', 
                'reset_time_widget'
            ]
            
            for component_name in required_components:
                if not hasattr(self, component_name):
                    print(f"缺少必要组件: {component_name}")
                    return False
                    
                component = getattr(self, component_name)
                if component is None:
                    print(f"组件未初始化: {component_name}")
                    return False
            
            # 检查数据一致性
            if self.violations and hasattr(self, 'violation_list_widget'):
                widget_count = self.violation_list_widget.rowCount()
                data_count = len(self.violations)
                if widget_count != data_count:
                    print(f"数据不一致: 组件显示{widget_count}条，实际数据{data_count}条")
                    return False
            
            return True
            
        except Exception as e:
            print(f"验证组件状态时发生错误: {str(e)}")
            return False
    
    def export_results(self, format_type: str):
        """导出结果
        
        Args:
            format_type: 导出格式类型 ("excel" 或 "csv")
        """
        try:
            # 增强的数据验证
            is_valid, error_msg, warning_msg = self.validate_export_data()
            if not is_valid:
                self._show_export_warning("导出数据验证失败", error_msg)
                return
            
            # 如果有警告信息，询问用户是否继续
            if warning_msg:
                if not self.show_export_validation_dialog(warning_msg):
                    return  # 用户选择取消导出
            
            # 导入必要的模块
            from PyQt5.QtWidgets import QFileDialog, QProgressDialog
            from PyQt5.QtCore import QTimer
            from .export_manager import ExportManager
            from .models import ExportConfig
            from .exceptions import ExportError
            
            # 创建导出管理器
            export_manager = ExportManager()
            
            # 获取用例信息
            case_info = {}
            if self.case_info:
                case_info['case_name'] = self.case_info.case_name
                case_info['corner'] = self.case_info.corner
            
            # 生成默认文件名
            default_filename = export_manager.get_default_filename(case_info, format_type)
            
            # 设置文件对话框过滤器
            if format_type == "excel":
                file_filter = "Excel文件 (*.xlsx);;所有文件 (*)"
                default_extension = ".xlsx"
                format_display = "Excel"
            else:
                file_filter = "CSV文件 (*.csv);;所有文件 (*)"
                default_extension = ".csv"
                format_display = "CSV"
            
            # 显示文件保存对话框
            file_path, selected_filter = QFileDialog.getSaveFileName(
                self,
                f"导出{format_display}文件",
                default_filename,
                file_filter
            )
            
            if not file_path:
                return  # 用户取消了保存
            
            # 确保文件有正确的扩展名
            if not file_path.lower().endswith(default_extension.lower()):
                file_path += default_extension
            
            # 显示导出选项对话框
            export_options = self._show_export_options_dialog()
            if export_options is None:
                return  # 用户取消了导出选项
            
            # 创建导出配置
            try:
                config = ExportConfig(
                    format_type=format_type,
                    include_auto_confirmed=export_options['include_auto_confirmed'],
                    only_confirmed=export_options.get('only_confirmed', False),
                    file_path=file_path
                )
            except ValueError as e:
                self._show_export_error("配置错误", f"导出配置无效: {str(e)}")
                return
            
            # 验证导出路径
            if not export_manager.validate_export_path(file_path):
                self._show_export_error(
                    "路径验证失败", 
                    f"无法写入文件路径: {file_path}\n\n可能的原因:\n"
                    f"• 目录不存在或无法创建\n"
                    f"• 没有写入权限\n"
                    f"• 文件被其他程序占用\n"
                    f"• 磁盘空间不足\n\n"
                    f"请检查路径和权限后重试"
                )
                return
            
            # 显示进度对话框
            progress = QProgressDialog(f"正在导出{format_display}文件...", "取消", 0, 100, self)
            progress.setWindowTitle("导出进度")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setValue(10)
            
            # 执行导出
            try:
                progress.setValue(30)
                progress.setLabelText(f"正在生成{format_display}文件...")
                
                # 检查是否取消
                if progress.wasCanceled():
                    return
                
                result_path = export_manager.export_violations(self.violations, config, case_info)
                
                progress.setValue(80)
                progress.setLabelText("正在生成导出摘要...")
                
                # 检查是否取消
                if progress.wasCanceled():
                    return
                
                # 获取导出摘要
                summary = export_manager.get_export_summary(self.violations, config)
                
                progress.setValue(100)
                progress.close()
                
                # 显示导出成功信息
                self._show_export_success(result_path, format_display, summary)
                
            except ExportError as e:
                progress.close()
                self.show_enhanced_export_error("导出失败", e, f"{format_display}导出操作")
                
            except Exception as e:
                progress.close()
                self.show_enhanced_export_error("导出异常", e, f"{format_display}导出过程")
                
        except Exception as e:
            self.show_enhanced_export_error("系统错误", e, "导出系统初始化")
    
    def _show_export_warning(self, title: str, message: str):
        """显示导出警告对话框
        
        Args:
            title: 警告标题
            message: 警告信息
        """
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Warning)
        msg_box.setWindowTitle("导出警告")
        msg_box.setText(title)
        msg_box.setDetailedText(message)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()
    
    def _show_export_error(self, title: str, message: str):
        """显示导出错误对话框
        
        Args:
            title: 错误标题
            message: 错误信息
        """
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle("导出错误")
        msg_box.setText(title)
        msg_box.setDetailedText(message)
        msg_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Retry)
        msg_box.setDefaultButton(QMessageBox.Retry)
        
        # 添加帮助按钮
        help_button = msg_box.addButton("帮助", QMessageBox.HelpRole)
        
        result = msg_box.exec_()
        
        if msg_box.clickedButton() == help_button:
            self._show_export_help()
        elif result == QMessageBox.Retry:
            # 延迟重试，给用户时间解决问题
            QTimer.singleShot(1000, lambda: self.export_results(
                "excel" if "Excel" in title else "csv"
            ))
    
    def _show_export_success(self, file_path: str, format_type: str, summary: dict):
        """显示导出成功对话框
        
        Args:
            file_path: 导出文件路径
            format_type: 导出格式类型
            summary: 导出摘要信息
        """
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit
        from PyQt5.QtCore import Qt
        import os
        
        dialog = QDialog(self)
        dialog.setWindowTitle("导出成功")
        dialog.setModal(True)
        dialog.setMinimumSize(500, 400)
        
        layout = QVBoxLayout()
        
        # 成功标题
        title_label = QLabel("✅ 导出成功！")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #28a745; margin: 10px 0;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 文件信息
        file_info = f"""文件路径: {file_path}
导出格式: {format_type}
文件大小: {self._get_file_size(file_path)}
导出时间: {self._get_current_time()}"""
        
        file_info_label = QLabel(file_info)
        file_info_label.setStyleSheet("background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 3px;")
        layout.addWidget(file_info_label)
        
        # 统计信息
        stats_text = f"""导出统计:
• 总违例数: {summary['total_violations']}
• 导出违例数: {summary['exported_violations']}
• 已确认: {summary['confirmed_count']}
• 待确认: {summary['pending_count']}

状态分布:"""
        
        if summary['status_counts']:
            for status, count in summary['status_counts'].items():
                if count > 0:
                    stats_text += f"\n• {status}: {count}"
        
        stats_label = QLabel(stats_text)
        stats_label.setStyleSheet("margin: 10px 0;")
        layout.addWidget(stats_label)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 打开文件按钮
        open_file_button = QPushButton("打开文件")
        open_file_button.clicked.connect(lambda: self._open_file(file_path))
        open_file_button.setStyleSheet("""
            QPushButton {
                padding: 8px 15px;
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        button_layout.addWidget(open_file_button)
        
        # 打开目录按钮
        open_dir_button = QPushButton("打开目录")
        open_dir_button.clicked.connect(lambda: self._open_directory(os.path.dirname(file_path)))
        open_dir_button.setStyleSheet("""
            QPushButton {
                padding: 8px 15px;
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        button_layout.addWidget(open_dir_button)
        
        button_layout.addStretch()
        
        # 关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(dialog.accept)
        close_button.setDefault(True)
        close_button.setStyleSheet("""
            QPushButton {
                padding: 8px 15px;
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        dialog.setLayout(layout)
        dialog.exec_()
    
    def _show_export_help(self):
        """显示导出帮助信息"""
        help_text = """导出功能帮助

常见问题解决方案:

1. 路径验证失败
   • 确保目标目录存在且有写入权限
   • 检查磁盘空间是否充足
   • 关闭可能占用文件的程序(如Excel)

2. Excel导出失败
   • 确保已安装openpyxl库: pip install openpyxl
   • 检查Excel文件是否被其他程序打开

3. CSV导出失败
   • 检查文件编码设置
   • 确保文件路径不包含特殊字符

4. 权限问题
   • 以管理员身份运行程序
   • 选择用户目录下的路径

5. 文件占用
   • 关闭Excel或其他可能打开目标文件的程序
   • 选择不同的文件名

如果问题仍然存在，请联系技术支持。"""
        
        QMessageBox.information(self, "导出帮助", help_text)
    
    def _get_error_details(self, error: Exception) -> str:
        """获取错误详细信息
        
        Args:
            error: 异常对象
            
        Returns:
            str: 格式化的错误信息
        """
        import traceback
        
        error_type = type(error).__name__
        error_message = str(error)
        
        # 提供用户友好的错误信息
        if "Permission denied" in error_message or "PermissionError" in error_type:
            return f"权限错误: 没有足够的权限访问文件或目录\n原始错误: {error_message}"
        elif "FileNotFoundError" in error_type:
            return f"文件未找到: 指定的文件或目录不存在\n原始错误: {error_message}"
        elif "OSError" in error_type:
            return f"系统错误: 操作系统级别的错误\n原始错误: {error_message}"
        elif "openpyxl" in error_message.lower():
            return f"Excel库错误: 请确保已安装openpyxl库\n原始错误: {error_message}"
        else:
            return f"{error_type}: {error_message}"
    
    def _get_file_size(self, file_path: str) -> str:
        """获取文件大小的友好显示
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 格式化的文件大小
        """
        try:
            import os
            size = os.path.getsize(file_path)
            
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            else:
                return f"{size / (1024 * 1024):.1f} MB"
        except:
            return "未知"
    
    def _get_current_time(self) -> str:
        """获取当前时间的格式化字符串
        
        Returns:
            str: 格式化的当前时间
        """
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def _open_file(self, file_path: str):
        """打开文件
        
        Args:
            file_path: 文件路径
        """
        try:
            import os
            import subprocess
            import platform
            
            if platform.system() == "Windows":
                os.startfile(file_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", file_path])
            else:  # Linux
                subprocess.run(["xdg-open", file_path])
                
        except Exception as e:
            QMessageBox.warning(self, "打开文件失败", f"无法打开文件: {str(e)}")
    
    def _open_directory(self, directory_path: str):
        """打开目录
        
        Args:
            directory_path: 目录路径
        """
        try:
            import os
            import subprocess
            import platform
            
            if platform.system() == "Windows":
                subprocess.run(["explorer", directory_path])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", directory_path])
            else:  # Linux
                subprocess.run(["xdg-open", directory_path])
                
        except Exception as e:
            QMessageBox.warning(self, "打开目录失败", f"无法打开目录: {str(e)}")
    
    def _show_export_options_dialog(self) -> dict:
        """显示导出选项对话框
        
        Returns:
            dict: 导出选项，如果用户取消则返回None
        """
        try:
            from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, 
                                       QCheckBox, QLabel, QPushButton, QGroupBox,
                                       QRadioButton, QButtonGroup, QFrame)
            from PyQt5.QtCore import Qt
            
            dialog = QDialog(self)
            dialog.setWindowTitle("导出选项")
            dialog.setModal(True)
            dialog.setMinimumWidth(400)
            dialog.setMinimumHeight(300)
            
            layout = QVBoxLayout()
            layout.setSpacing(15)
            
            # 标题
            title_label = QLabel("📊 导出选项配置")
            title_label.setStyleSheet("font-size: 14px; font-weight: bold; margin-bottom: 10px;")
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)
            
            # 导出内容选项组
            content_group = QGroupBox("导出内容")
            content_layout = QVBoxLayout()
            
            # 包含自动确认选项
            include_auto_checkbox = QCheckBox("包含自动确认的违例")
            include_auto_checkbox.setChecked(True)
            include_auto_checkbox.setToolTip("是否在导出文件中包含复位期间自动确认的违例")
            include_auto_checkbox.setStyleSheet("margin: 5px 0;")
            content_layout.addWidget(include_auto_checkbox)
            
            # 仅导出已确认选项
            only_confirmed_checkbox = QCheckBox("仅导出已确认的违例")
            only_confirmed_checkbox.setChecked(False)
            only_confirmed_checkbox.setToolTip("只导出已经确认的违例，忽略待确认的违例")
            only_confirmed_checkbox.setStyleSheet("margin: 5px 0;")
            content_layout.addWidget(only_confirmed_checkbox)
            
            content_group.setLayout(content_layout)
            layout.addWidget(content_group)
            
            # 分隔线
            line = QFrame()
            line.setFrameShape(QFrame.HLine)
            line.setFrameShadow(QFrame.Sunken)
            layout.addWidget(line)
            
            # 统计信息组
            stats_group = QGroupBox("当前数据统计")
            stats_layout = QVBoxLayout()
            
            stats = self.get_confirmation_statistics()
            
            # 创建统计信息表格样式
            stats_html = f"""
            <table style="width: 100%; border-collapse: collapse;">
                <tr style="background-color: #f8f9fa;">
                    <td style="padding: 5px; border: 1px solid #dee2e6;"><b>状态</b></td>
                    <td style="padding: 5px; border: 1px solid #dee2e6; text-align: center;"><b>数量</b></td>
                </tr>
                <tr>
                    <td style="padding: 5px; border: 1px solid #dee2e6;">总违例数</td>
                    <td style="padding: 5px; border: 1px solid #dee2e6; text-align: center;">{stats['total']}</td>
                </tr>
                <tr style="background-color: #fff3cd;">
                    <td style="padding: 5px; border: 1px solid #dee2e6;">待确认</td>
                    <td style="padding: 5px; border: 1px solid #dee2e6; text-align: center;">{stats['pending']}</td>
                </tr>
                <tr style="background-color: #d4edda;">
                    <td style="padding: 5px; border: 1px solid #dee2e6;">已确认(无问题)</td>
                    <td style="padding: 5px; border: 1px solid #dee2e6; text-align: center;">{stats['confirmed_ok']}</td>
                </tr>
                <tr style="background-color: #f8d7da;">
                    <td style="padding: 5px; border: 1px solid #dee2e6;">已确认(有问题)</td>
                    <td style="padding: 5px; border: 1px solid #dee2e6; text-align: center;">{stats['confirmed_issue']}</td>
                </tr>
                <tr style="background-color: #e2e3e5;">
                    <td style="padding: 5px; border: 1px solid #dee2e6;">自动确认</td>
                    <td style="padding: 5px; border: 1px solid #dee2e6; text-align: center;">{stats['auto_confirmed']}</td>
                </tr>
            </table>
            """
            
            stats_label = QLabel(stats_html)
            stats_label.setStyleSheet("margin: 10px 0;")
            stats_layout.addWidget(stats_label)
            
            stats_group.setLayout(stats_layout)
            layout.addWidget(stats_group)
            
            # 按钮布局
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            
            # 取消按钮
            cancel_button = QPushButton("取消")
            cancel_button.clicked.connect(dialog.reject)
            cancel_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)
            button_layout.addWidget(cancel_button)
            
            # 确定按钮
            ok_button = QPushButton("确定导出")
            ok_button.clicked.connect(dialog.accept)
            ok_button.setDefault(True)
            ok_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #007bff;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #0056b3;
                }
            """)
            button_layout.addWidget(ok_button)
            
            layout.addLayout(button_layout)
            dialog.setLayout(layout)
            
            # 显示对话框并获取结果
            if dialog.exec_() == QDialog.Accepted:
                return {
                    'include_auto_confirmed': include_auto_checkbox.isChecked(),
                    'only_confirmed': only_confirmed_checkbox.isChecked()
                }
            else:
                return None
                
        except Exception as e:
            QMessageBox.critical(self, "导出选项错误", f"显示导出选项对话框时发生错误:\n{str(e)}")
            return None
    
    def _quick_export(self, format_type: str, include_all: bool):
        """快速导出功能
        
        Args:
            format_type: 导出格式类型
            include_all: 是否包含所有违例
        """
        try:
            # 检查导出要求
            is_valid, error_msg = self.validate_export_requirements()
            if not is_valid:
                self._show_export_warning("快速导出失败", error_msg)
                return
            
            # 导入必要的模块
            from .export_manager import ExportManager
            from .models import ExportConfig
            from .exceptions import ExportError
            
            # 创建导出管理器
            export_manager = ExportManager()
            
            # 获取用例信息
            case_info = {}
            if self.case_info:
                case_info['case_name'] = self.case_info.case_name
                case_info['corner'] = self.case_info.corner
            
            # 生成默认文件名和路径
            default_filename = export_manager.get_default_filename(case_info, format_type)
            
            # 使用默认路径（当前目录或用户文档目录）
            import os
            from pathlib import Path
            
            # 尝试使用用户文档目录，如果失败则使用当前目录
            try:
                documents_path = Path.home() / "Documents"
                if documents_path.exists():
                    export_path = documents_path / default_filename
                else:
                    export_path = Path.cwd() / default_filename
            except:
                export_path = Path.cwd() / default_filename
            
            file_path = str(export_path)
            
            # 创建导出配置
            config = ExportConfig(
                format_type=format_type,
                include_auto_confirmed=include_all,
                only_confirmed=False,  # 快速导出包含所有状态
                file_path=file_path
            )
            
            # 验证导出路径
            if not export_manager.validate_export_path(file_path):
                # 如果默认路径失败，尝试当前目录
                fallback_path = str(Path.cwd() / default_filename)
                if export_manager.validate_export_path(fallback_path):
                    config.file_path = fallback_path
                    file_path = fallback_path
                else:
                    self._show_export_error(
                        "快速导出失败", 
                        f"无法写入默认路径，请使用常规导出功能选择其他位置"
                    )
                    return
            
            # 显示简化的进度提示
            from PyQt5.QtWidgets import QProgressDialog
            from PyQt5.QtCore import QTimer
            
            progress = QProgressDialog(f"正在快速导出{format_type.upper()}文件...", None, 0, 100, self)
            progress.setWindowTitle("快速导出")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setCancelButton(None)  # 快速导出不允许取消
            progress.setValue(20)
            
            # 执行导出
            try:
                progress.setValue(50)
                result_path = export_manager.export_violations(self.violations, config, case_info)
                
                progress.setValue(80)
                summary = export_manager.get_export_summary(self.violations, config)
                
                progress.setValue(100)
                progress.close()
                
                # 显示简化的成功消息
                self._show_quick_export_success(result_path, format_type, summary)
                
            except ExportError as e:
                progress.close()
                self.show_enhanced_export_error("快速导出失败", e, f"快速{format_type.upper()}导出")
                
            except Exception as e:
                progress.close()
                self.show_enhanced_export_error("快速导出异常", e, f"快速{format_type.upper()}导出过程")
                
        except Exception as e:
            self.show_enhanced_export_error("快速导出系统错误", e, "快速导出系统")
    
    def _show_quick_export_success(self, file_path: str, format_type: str, summary: dict, include_all: bool = True):
        """显示快速导出成功消息
        
        Args:
            file_path: 导出文件路径
            format_type: 导出格式类型
            summary: 导出摘要信息
            include_all: 是否包含所有违例（用于显示额外信息）
        """
        try:
            import os
            
            # 创建简化的成功消息
            message = f"""快速导出成功！

文件路径: {file_path}
导出格式: {format_type.upper()}
文件大小: {self._get_file_size(file_path)}

导出统计:
• 总违例数: {summary['total_violations']}
• 导出违例数: {summary['exported_violations']}
• 已确认: {summary['confirmed_count']}
• 待确认: {summary['pending_count']}"""
            
            # 创建消息框
            msg_box = QMessageBox(self)
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setWindowTitle("快速导出成功")
            msg_box.setText("✅ 快速导出完成！")
            msg_box.setDetailedText(message)
            msg_box.setStandardButtons(QMessageBox.Ok)
            
            # 添加打开文件和打开目录按钮
            open_file_button = msg_box.addButton("打开文件", QMessageBox.ActionRole)
            open_dir_button = msg_box.addButton("打开目录", QMessageBox.ActionRole)
            
            result = msg_box.exec_()
            
            # 处理按钮点击
            if msg_box.clickedButton() == open_file_button:
                self._open_file(file_path)
            elif msg_box.clickedButton() == open_dir_button:
                self._open_directory(os.path.dirname(file_path))
                
        except Exception as e:
            # 如果显示成功消息失败，至少显示基本信息
            QMessageBox.information(self, "快速导出成功", f"文件已导出到: {file_path}")
    
    def get_confirmation_statistics(self) -> dict:
        """获取确认统计信息
        
        Returns:
            dict: 统计信息字典
        """
        try:
            if not self.violations:
                return {
                    'total': 0,
                    'pending': 0,
                    'confirmed_ok': 0,
                    'confirmed_issue': 0,
                    'auto_confirmed': 0
                }
            
            from .models import ViolationStatus
            
            stats = {
                'total': len(self.violations),
                'pending': 0,
                'confirmed_ok': 0,
                'confirmed_issue': 0,
                'auto_confirmed': 0
            }
            
            for violation in self.violations:
                if violation.status == ViolationStatus.PENDING:
                    stats['pending'] += 1
                elif violation.status == ViolationStatus.CONFIRMED_OK:
                    stats['confirmed_ok'] += 1
                elif violation.status == ViolationStatus.CONFIRMED_ISSUE:
                    stats['confirmed_issue'] += 1
                elif violation.status == ViolationStatus.AUTO_CONFIRMED:
                    stats['auto_confirmed'] += 1
            
            return stats
            
        except Exception as e:
            print(f"获取确认统计信息时发生错误: {str(e)}")
            return {
                'total': 0,
                'pending': 0,
                'confirmed_ok': 0,
                'confirmed_issue': 0,
                'auto_confirmed': 0
            }
    
    def has_pending_violations(self) -> bool:
        """检查是否有待确认的违例
        
        Returns:
            bool: 是否有待确认的违例
        """
        try:
            if not self.violations:
                return False
            
            from .models import ViolationStatus
            return any(v.status == ViolationStatus.PENDING for v in self.violations)
            
        except Exception as e:
            print(f"检查待确认违例时发生错误: {str(e)}")
            return False
    
    def get_pending_violation_count(self) -> int:
        """获取待确认违例数量
        
        Returns:
            int: 待确认违例数量
        """
        try:
            if not self.violations:
                return 0
            
            from .models import ViolationStatus
            return sum(1 for v in self.violations if v.status == ViolationStatus.PENDING)
            
        except Exception as e:
            print(f"获取待确认违例数量时发生错误: {str(e)}")
            return 0
            
            # 预览导出数量
            def update_preview():
                will_export = stats['total']
                if not include_auto_checkbox.isChecked():
                    will_export -= stats['auto_confirmed']
                if only_confirmed_checkbox.isChecked():
                    will_export = stats['confirmed_ok'] + stats['confirmed_issue']
                    if include_auto_checkbox.isChecked():
                        will_export += stats['auto_confirmed']
                
                preview_label.setText(f"📋 预计导出: {will_export} 条违例记录")
                preview_label.setStyleSheet(
                    "color: #007bff; font-weight: bold; "
                    "background-color: #e7f3ff; padding: 8px; "
                    "border: 1px solid #b3d9ff; border-radius: 3px;"
                )
            
            preview_label = QLabel()
            update_preview()
            stats_layout.addWidget(preview_label)
            
            # 连接信号以实时更新预览
            include_auto_checkbox.toggled.connect(update_preview)
            only_confirmed_checkbox.toggled.connect(update_preview)
            
            stats_group.setLayout(stats_layout)
            layout.addWidget(stats_group)
            
            # 按钮布局
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            
            # 重置按钮
            reset_button = QPushButton("重置")
            reset_button.clicked.connect(lambda: (
                include_auto_checkbox.setChecked(True),
                only_confirmed_checkbox.setChecked(False)
            ))
            reset_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)
            button_layout.addWidget(reset_button)
            
            # 确定按钮
            ok_button = QPushButton("确定导出")
            ok_button.setDefault(True)
            ok_button.clicked.connect(dialog.accept)
            ok_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #28a745;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
            button_layout.addWidget(ok_button)
            
            # 取消按钮
            cancel_button = QPushButton("取消")
            cancel_button.clicked.connect(dialog.reject)
            cancel_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            button_layout.addWidget(cancel_button)
            
            layout.addLayout(button_layout)
            dialog.setLayout(layout)
            
            # 显示对话框
            if dialog.exec_() == QDialog.Accepted:
                return {
                    'include_auto_confirmed': include_auto_checkbox.isChecked(),
                    'only_confirmed': only_confirmed_checkbox.isChecked()
                }
            else:
                return None
                
        except Exception as e:
            print(f"显示导出选项对话框时发生错误: {str(e)}")
            # 显示错误并返回默认选项
            QMessageBox.warning(self, "选项对话框错误", f"显示导出选项时发生错误: {str(e)}")
            return {'include_auto_confirmed': True, 'only_confirmed': False}
    
    def get_current_file_path(self) -> str:
        """获取当前选择的文件路径
        
        Returns:
            str: 当前文件路径
        """
        if hasattr(self, 'file_selection_widget'):
            return self.file_selection_widget.get_current_file_path()
        return ""
    
    def get_current_case_info(self) -> CaseInfo:
        """获取当前用例信息
        
        Returns:
            CaseInfo: 当前用例信息
        """
        if hasattr(self, 'file_selection_widget'):
            return self.file_selection_widget.get_current_case_info()
        return self.case_info
    
    def is_ready_for_processing(self) -> bool:
        """检查是否准备好进行处理
        
        Returns:
            bool: 是否准备好
        """
        if hasattr(self, 'file_selection_widget'):
            return self.file_selection_widget.is_case_info_complete()
        return False
    
    def on_violation_selected(self, violation: ViolationData):
        """违例选择事件处理
        
        Args:
            violation: 选择的违例数据
        """
        print(f"选择了违例: NUM={violation.num}, Hier={violation.hier}")
    
    def on_confirm_requested(self, violation: ViolationData):
        """确认请求事件处理
        
        Args:
            violation: 需要确认的违例数据
        """
        print(f"请求确认违例: NUM={violation.num}")
        
        try:
            # 导入确认对话框
            from .confirmation_dialog import ConfirmationDialog
            
            # 创建确认对话框
            dialog = ConfirmationDialog(violation, self)
            
            # 连接确认完成信号
            dialog.violation_confirmed.connect(self._on_violation_confirmed)
            dialog.confirmation_cancelled.connect(self._on_confirmation_cancelled)
            
            # 显示对话框
            dialog.exec_()
            
        except Exception as e:
            error_msg = f"打开确认对话框时发生错误: {str(e)}"
            QMessageBox.critical(self, "错误", error_msg)
            print(f"确认对话框错误: {error_msg}")
    
    def on_detail_requested(self, violation: ViolationData):
        """详情查看请求事件处理
        
        Args:
            violation: 需要查看详情的违例数据
        """
        print(f"请求查看违例详情: NUM={violation.num}")
        
        # 显示简单的详情信息
        detail_text = f"""违例详细信息:

NUM: {violation.num}
Hier: {violation.hier}
Time: {violation.time_original}
Check: {violation.check}
状态: {violation.get_status_display()}
确认人: {violation.confirmer if violation.confirmer else '未确认'}
"""
        QMessageBox.information(self, f"违例详情 - NUM {violation.num}", detail_text)
    
    def on_status_changed(self, violation: ViolationData):
        """状态变化事件处理
        
        Args:
            violation: 状态发生变化的违例数据
        """
        print(f"违例状态变化: NUM={violation.num}, 新状态={violation.get_status_display()}")
    
    def on_reset_time_applied(self, reset_time_fs: int):
        """复位时间应用事件处理
        
        Args:
            reset_time_fs: 应用的复位时间（飞秒）
        """
        try:
            self.reset_time_fs = reset_time_fs
            
            # 如果有违例数据，应用自动确认逻辑
            if self.violations:
                auto_confirmed_count = self.apply_auto_confirmation_logic(reset_time_fs)
                
                # 更新违例列表显示
                if hasattr(self, 'violation_list_widget'):
                    self.violation_list_widget.refresh_display()
                
                # 显示自动确认结果
                from .utils import TimeConverter
                formatted_time = TimeConverter.format_time(reset_time_fs)
                
                if auto_confirmed_count > 0:
                    QMessageBox.information(
                        self, 
                        "自动确认完成", 
                        f"复位时间已设置为: {formatted_time}\n"
                        f"自动确认了 {auto_confirmed_count} 个复位期间的时序违例"
                    )
                else:
                    QMessageBox.information(
                        self, 
                        "复位时间已设置", 
                        f"复位时间已设置为: {formatted_time}\n"
                        f"没有发现复位期间的时序违例"
                    )
            
            print(f"复位时间应用成功: {reset_time_fs} FS")
            
        except Exception as e:
            error_msg = f"应用复位时间时发生错误: {str(e)}"
            QMessageBox.critical(self, "错误", error_msg)
            print(f"复位时间应用错误: {error_msg}")
    
    def on_reset_time_cleared(self):
        """复位时间清除事件处理"""
        try:
            self.reset_time_fs = 0
            
            # 如果有违例数据，清除自动确认状态
            if self.violations:
                cleared_count = self.clear_auto_confirmation_logic()
                
                # 更新违例列表显示
                if hasattr(self, 'violation_list_widget'):
                    self.violation_list_widget.refresh_display()
                
                # 显示清除结果
                if cleared_count > 0:
                    QMessageBox.information(
                        self, 
                        "自动确认已清除", 
                        f"复位时间已清除\n"
                        f"恢复了 {cleared_count} 个违例的待确认状态"
                    )
                else:
                    QMessageBox.information(
                        self, 
                        "复位时间已清除", 
                        "复位时间已清除"
                    )
            
            print("复位时间清除成功")
            
        except Exception as e:
            error_msg = f"清除复位时间时发生错误: {str(e)}"
            QMessageBox.critical(self, "错误", error_msg)
            print(f"复位时间清除错误: {error_msg}")
    
    def on_reset_time_validation_error(self, error_message: str):
        """复位时间验证错误事件处理
        
        Args:
            error_message: 错误信息
        """
        print(f"复位时间验证错误: {error_message}")
        
        # 显示错误提示
        QMessageBox.warning(self, "复位时间验证错误", error_message)
    
    def apply_auto_confirmation_logic(self, reset_time_fs: int) -> int:
        """应用自动确认逻辑
        
        Args:
            reset_time_fs: 复位时间（飞秒）
            
        Returns:
            int: 自动确认的违例数量
        """
        if not self.violations or reset_time_fs <= 0:
            return 0
        
        from .utils import TimeConverter
        from .models import ViolationStatus
        
        auto_confirmed_count = 0
        
        for violation in self.violations:
            # 检查是否为复位期间违例且未手动确认
            if (not violation.is_confirmed() and 
                TimeConverter.is_reset_period_violation(violation.time_fs, reset_time_fs)):
                
                # 设置自动确认状态
                violation.status = ViolationStatus.AUTO_CONFIRMED
                violation.auto_confirmed = True
                violation.confirmer = "系统自动"
                violation.reason = "复位期间时序违例，可以忽略"
                violation.confirmation_result = "无问题"
                
                auto_confirmed_count += 1
        
        return auto_confirmed_count
    
    def clear_auto_confirmation_logic(self) -> int:
        """清除自动确认逻辑
        
        Returns:
            int: 清除的自动确认违例数量
        """
        if not self.violations:
            return 0
        
        from .models import ViolationStatus
        
        cleared_count = 0
        
        for violation in self.violations:
            # 如果是自动确认的违例，恢复为待确认状态
            if violation.auto_confirmed and violation.status == ViolationStatus.AUTO_CONFIRMED:
                violation.status = ViolationStatus.PENDING
                violation.auto_confirmed = False
                violation.confirmer = ""
                violation.reason = ""
                violation.confirmation_result = None
                
                cleared_count += 1
        
        return cleared_count
    
    def _on_violation_confirmed(self, violation: ViolationData):
        """违例确认完成事件处理
        
        Args:
            violation: 确认完成的违例数据
        """
        try:
            # 更新违例列表显示
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.update_violation_status(violation)
            
            # 更新导出按钮状态
            self.update_export_button_status()
            
            print(f"违例确认完成: NUM={violation.num}, 状态={violation.get_status_display()}")
            
        except Exception as e:
            print(f"处理违例确认完成事件时发生错误: {str(e)}")
    
    def _on_confirmation_cancelled(self, violation: ViolationData):
        """违例确认取消事件处理
        
        Args:
            violation: 取消确认的违例数据
        """
        print(f"违例确认取消: NUM={violation.num}")
    
    def update_export_button_status_legacy(self):
        """更新导出按钮状态（旧版本，保留用于兼容性）"""
        try:
            # 检查是否有违例数据和用例信息
            has_data = bool(self.violations)
            has_case_info = bool(self.case_info)
            
            # 启用/禁用导出按钮
            if hasattr(self, 'export_excel_button'):
                self.export_excel_button.setEnabled(has_data and has_case_info)
            
            if hasattr(self, 'export_csv_button'):
                self.export_csv_button.setEnabled(has_data and has_case_info)
            
            if hasattr(self, 'quick_export_button'):
                self.quick_export_button.setEnabled(has_data and has_case_info)
                
        except Exception as e:
            print(f"更新导出按钮状态时发生错误: {str(e)}")
    
    def clear_auto_confirmed_violations(self) -> int:
        """清除自动确认的违例数量
        
        Returns:
            int: 清除的违例数量
        """
        if not self.violations:
            return 0
        
        from .models import ViolationStatus
        
        cleared_count = 0
        
        for violation in self.violations:
            # 只清除自动确认的违例，保留手动确认的违例
            if violation.is_auto_confirmed():
                violation.status = ViolationStatus.PENDING
                violation.auto_confirmed = False
                violation.confirmer = ""
                violation.reason = ""
                violation.confirmation_result = None
                
                cleared_count += 1
        
        return cleared_count
    
    def recalculate_auto_confirmation(self, new_reset_time_fs: int):
        """重新计算自动确认状态（当复位时间更改时）
        
        Args:
            new_reset_time_fs: 新的复位时间（飞秒）
        """
        if not self.violations:
            return
        
        from .utils import TimeConverter
        from .models import ViolationStatus
        
        # 首先清除所有自动确认状态
        for violation in self.violations:
            if violation.is_auto_confirmed():
                violation.status = ViolationStatus.PENDING
                violation.auto_confirmed = False
                violation.confirmer = ""
                violation.reason = ""
                violation.confirmation_result = None
        
        # 如果新的复位时间有效，重新应用自动确认
        if new_reset_time_fs > 0:
            self.apply_auto_confirmation_logic(new_reset_time_fs)
        
        # 更新复位时间组件的违例数据
        if hasattr(self, 'reset_time_widget'):
            self.reset_time_widget.update_violation_status(self.violations)
    
    def load_violations_to_list(self, violations: list):
        """加载违例数据到列表
        
        Args:
            violations: 违例数据列表
        """
        # 更新内部违例数据
        self.violations = violations or []
        
        # 同步到违例列表组件
        if hasattr(self, 'violation_list_widget'):
            self.violation_list_widget.load_violations(violations)
        
        # 同步到复位时间组件
        if hasattr(self, 'reset_time_widget'):
            self.reset_time_widget.set_violations(violations)
            
            # 如果已设置复位时间，重新应用自动确认逻辑
            if self.reset_time_widget.has_reset_time():
                self.recalculate_auto_confirmation(self.reset_time_widget.get_reset_time_fs())
        
        # 更新导出按钮状态
        self.update_export_button_status()
    
    def get_violation_list_widget(self) -> ViolationListWidget:
        """获取违例列表组件
        
        Returns:
            ViolationListWidget: 违例列表组件
        """
        return getattr(self, 'violation_list_widget', None)
    
    def validate_export_data(self) -> tuple:
        """验证导出数据的完整性
        
        Returns:
            tuple: (is_valid, error_msg, warning_msg)
        """
        try:
            # 检查是否有违例数据
            if not self.violations:
                return False, "没有可导出的违例数据，请先加载时序违例文件", None
            
            # 检查用例信息
            if not self.case_info:
                return False, "缺少用例信息，请确保文件路径格式正确或选择Corner", None
            
            # 检查用例信息完整性
            if not self.case_info.case_name:
                return False, "用例名称为空，请检查文件路径格式", None
            
            if not self.case_info.corner:
                return False, "Corner信息缺失，请选择正确的Corner", None
            
            # 检查是否有待确认的违例
            pending_count = self.get_pending_violation_count()
            warning_msg = None
            
            if pending_count > 0:
                warning_msg = f"当前还有 {pending_count} 个违例未确认，是否继续导出？"
            
            return True, None, warning_msg
            
        except Exception as e:
            return False, f"验证导出数据时发生错误: {str(e)}", None
    
    def validate_export_requirements(self) -> tuple:
        """验证导出基本要求
        
        Returns:
            tuple: (is_valid, error_msg)
        """
        try:
            # 检查是否有违例数据
            if not self.violations:
                return False, "没有可导出的违例数据"
            
            # 检查用例信息
            if not self.case_info or not self.case_info.case_name:
                return False, "缺少用例信息"
            
            return True, None
            
        except Exception as e:
            return False, f"验证导出要求时发生错误: {str(e)}"
    
    def show_export_validation_dialog(self, warning_msg: str) -> bool:
        """显示导出验证对话框
        
        Args:
            warning_msg: 警告信息
            
        Returns:
            bool: 用户是否选择继续导出
        """
        try:
            from PyQt5.QtWidgets import QMessageBox
            
            msg_box = QMessageBox(self)
            msg_box.setIcon(QMessageBox.Warning)
            msg_box.setWindowTitle("导出确认")
            msg_box.setText("导出前确认")
            msg_box.setInformativeText(warning_msg)
            msg_box.setDetailedText(
                "建议在导出前完成所有违例的确认工作，以确保导出结果的完整性。\n\n"
                "如果选择继续导出，未确认的违例将在导出文件中标记为'待确认'状态。"
            )
            
            # 添加自定义按钮
            continue_button = msg_box.addButton("继续导出", QMessageBox.AcceptRole)
            cancel_button = msg_box.addButton("取消", QMessageBox.RejectRole)
            
            # 设置默认按钮
            msg_box.setDefaultButton(cancel_button)
            
            # 显示对话框
            result = msg_box.exec_()
            
            return msg_box.clickedButton() == continue_button
            
        except Exception as e:
            print(f"显示导出验证对话框时发生错误: {str(e)}")
            # 如果对话框显示失败，默认允许继续
            return True
    
    def show_enhanced_export_error(self, title: str, error: Exception, operation: str):
        """显示增强的导出错误对话框
        
        Args:
            title: 错误标题
            error: 异常对象
            operation: 操作描述
        """
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit, QTabWidget, QWidget
            from PyQt5.QtCore import Qt
            import traceback
            
            dialog = QDialog(self)
            dialog.setWindowTitle("导出错误详情")
            dialog.setModal(True)
            dialog.setMinimumSize(600, 500)
            
            layout = QVBoxLayout()
            
            # 错误标题
            title_label = QLabel(f"❌ {title}")
            title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #dc3545; margin: 10px 0;")
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)
            
            # 操作描述
            operation_label = QLabel(f"操作: {operation}")
            operation_label.setStyleSheet("font-size: 12px; color: #6c757d; margin-bottom: 15px;")
            operation_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(operation_label)
            
            # 创建选项卡
            tab_widget = QTabWidget()
            
            # 错误信息选项卡
            error_tab = QWidget()
            error_layout = QVBoxLayout()
            
            # 用户友好的错误信息
            user_error = self._get_user_friendly_error(error)
            user_error_label = QLabel(user_error)
            user_error_label.setStyleSheet(
                "background-color: #f8d7da; color: #721c24; "
                "padding: 15px; border: 1px solid #f5c6cb; "
                "border-radius: 5px; margin: 10px 0;"
            )
            user_error_label.setWordWrap(True)
            error_layout.addWidget(user_error_label)
            
            # 原始错误信息
            original_error_label = QLabel("原始错误信息:")
            original_error_label.setStyleSheet("font-weight: bold; margin-top: 15px;")
            error_layout.addWidget(original_error_label)
            
            original_error_text = QTextEdit()
            original_error_text.setPlainText(str(error))
            original_error_text.setMaximumHeight(100)
            original_error_text.setReadOnly(True)
            original_error_text.setStyleSheet("background-color: #f8f9fa; border: 1px solid #dee2e6;")
            error_layout.addWidget(original_error_text)
            
            error_tab.setLayout(error_layout)
            tab_widget.addTab(error_tab, "错误信息")
            
            # 解决方案选项卡
            solution_tab = QWidget()
            solution_layout = QVBoxLayout()
            
            solutions = self._get_error_solutions(error)
            solutions_label = QLabel("建议的解决方案:")
            solutions_label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
            solution_layout.addWidget(solutions_label)
            
            solutions_text = QTextEdit()
            solutions_text.setPlainText(solutions)
            solutions_text.setReadOnly(True)
            solutions_text.setStyleSheet("background-color: #d1ecf1; border: 1px solid #bee5eb;")
            solution_layout.addWidget(solutions_text)
            
            solution_tab.setLayout(solution_layout)
            tab_widget.addTab(solution_tab, "解决方案")
            
            # 技术详情选项卡
            tech_tab = QWidget()
            tech_layout = QVBoxLayout()
            
            tech_label = QLabel("技术详情 (用于技术支持):")
            tech_label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
            tech_layout.addWidget(tech_label)
            
            tech_text = QTextEdit()
            tech_details = f"错误类型: {type(error).__name__}\n"
            tech_details += f"错误信息: {str(error)}\n"
            tech_details += f"操作: {operation}\n\n"
            tech_details += "堆栈跟踪:\n"
            tech_details += traceback.format_exc()
            
            tech_text.setPlainText(tech_details)
            tech_text.setReadOnly(True)
            tech_text.setStyleSheet("font-family: monospace; background-color: #f8f9fa; border: 1px solid #dee2e6;")
            tech_layout.addWidget(tech_text)
            
            tech_tab.setLayout(tech_layout)
            tab_widget.addTab(tech_tab, "技术详情")
            
            layout.addWidget(tab_widget)
            
            # 按钮布局
            button_layout = QHBoxLayout()
            
            # 复制错误信息按钮
            copy_button = QPushButton("复制错误信息")
            copy_button.clicked.connect(lambda: self._copy_error_to_clipboard(error, operation))
            copy_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #17a2b8;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #138496;
                }
            """)
            button_layout.addWidget(copy_button)
            
            # 重试按钮
            retry_button = QPushButton("重试")
            retry_button.clicked.connect(lambda: (dialog.done(1)))  # 返回1表示重试
            retry_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #ffc107;
                    color: #212529;
                    border: none;
                    border-radius: 3px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #e0a800;
                }
            """)
            button_layout.addWidget(retry_button)
            
            button_layout.addStretch()
            
            # 关闭按钮
            close_button = QPushButton("关闭")
            close_button.clicked.connect(dialog.accept)
            close_button.setDefault(True)
            close_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)
            button_layout.addWidget(close_button)
            
            layout.addLayout(button_layout)
            dialog.setLayout(layout)
            
            # 显示对话框
            result = dialog.exec_()
            
            # 如果用户选择重试
            if result == 1:
                from PyQt5.QtCore import QTimer
                # 延迟重试，给用户时间解决问题
                QTimer.singleShot(1000, lambda: self._retry_export_operation(operation))
                
        except Exception as e:
            # 如果增强错误对话框失败，显示简单错误信息
            error_msg = f"{title}\n\n操作: {operation}\n错误: {str(error)}"
            QMessageBox.critical(self, "导出错误", error_msg)
    
    def _get_user_friendly_error(self, error: Exception) -> str:
        """获取用户友好的错误信息
        
        Args:
            error: 异常对象
            
        Returns:
            str: 用户友好的错误信息
        """
        error_type = type(error).__name__
        error_message = str(error).lower()
        
        if "permission" in error_message or "access" in error_message:
            return "权限不足：无法访问指定的文件或目录。请检查文件权限或选择其他位置。"
        elif "filenotfound" in error_type.lower() or "no such file" in error_message:
            return "文件未找到：指定的文件或目录不存在。请检查路径是否正确。"
        elif "disk" in error_message or "space" in error_message:
            return "磁盘空间不足：无法创建导出文件。请清理磁盘空间或选择其他位置。"
        elif "openpyxl" in error_message:
            return "Excel库缺失：系统缺少Excel处理库。请安装openpyxl库或选择CSV格式导出。"
        elif "encoding" in error_message or "codec" in error_message:
            return "编码错误：文件编码处理失败。请检查文件内容或尝试其他导出格式。"
        elif "timeout" in error_message:
            return "操作超时：导出操作耗时过长。请尝试减少导出数据量或检查系统性能。"
        elif "memory" in error_message:
            return "内存不足：系统内存不足以完成导出操作。请关闭其他程序或减少导出数据量。"
        else:
            return f"导出过程中发生未知错误：{str(error)}"
    
    def _get_error_solutions(self, error: Exception) -> str:
        """获取错误解决方案
        
        Args:
            error: 异常对象
            
        Returns:
            str: 解决方案文本
        """
        error_type = type(error).__name__
        error_message = str(error).lower()
        
        solutions = []
        
        if "permission" in error_message or "access" in error_message:
            solutions.extend([
                "1. 以管理员身份运行程序",
                "2. 选择用户目录下的位置（如桌面或文档文件夹）",
                "3. 检查目标文件是否被其他程序占用",
                "4. 确保目标目录存在且可写"
            ])
        elif "filenotfound" in error_type.lower():
            solutions.extend([
                "1. 检查文件路径是否正确",
                "2. 确保目标目录存在",
                "3. 手动创建目标目录",
                "4. 选择其他可用的位置"
            ])
        elif "openpyxl" in error_message:
            solutions.extend([
                "1. 安装Excel处理库：pip install openpyxl",
                "2. 使用CSV格式导出作为替代方案",
                "3. 检查Python环境是否正确配置",
                "4. 重新安装相关依赖库"
            ])
        elif "disk" in error_message or "space" in error_message:
            solutions.extend([
                "1. 清理磁盘空间",
                "2. 选择其他磁盘位置",
                "3. 删除不必要的临时文件",
                "4. 减少导出数据量"
            ])
        elif "encoding" in error_message:
            solutions.extend([
                "1. 尝试使用CSV格式导出",
                "2. 检查数据中是否包含特殊字符",
                "3. 更改系统区域设置",
                "4. 联系技术支持"
            ])
        else:
            solutions.extend([
                "1. 重试导出操作",
                "2. 重启程序后再次尝试",
                "3. 选择不同的导出格式",
                "4. 检查系统资源使用情况",
                "5. 联系技术支持并提供错误详情"
            ])
        
        return "\n".join(solutions)
    
    def _copy_error_to_clipboard(self, error: Exception, operation: str):
        """复制错误信息到剪贴板
        
        Args:
            error: 异常对象
            operation: 操作描述
        """
        try:
            from PyQt5.QtWidgets import QApplication
            import traceback
            
            error_info = f"""时序违例确认工具 - 导出错误报告
            
时间: {self._get_current_time()}
操作: {operation}
错误类型: {type(error).__name__}
错误信息: {str(error)}

堆栈跟踪:
{traceback.format_exc()}

系统信息:
- 操作系统: {self._get_system_info()}
- Python版本: {self._get_python_version()}
"""
            
            clipboard = QApplication.clipboard()
            clipboard.setText(error_info)
            
            QMessageBox.information(self, "复制成功", "错误信息已复制到剪贴板")
            
        except Exception as e:
            QMessageBox.warning(self, "复制失败", f"无法复制错误信息: {str(e)}")
    
    def _get_system_info(self) -> str:
        """获取系统信息
        
        Returns:
            str: 系统信息
        """
        try:
            import platform
            return f"{platform.system()} {platform.release()}"
        except:
            return "未知"
    
    def _get_python_version(self) -> str:
        """获取Python版本
        
        Returns:
            str: Python版本
        """
        try:
            import sys
            return f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        except:
            return "未知"
    
    def _retry_export_operation(self, operation: str):
        """重试导出操作
        
        Args:
            operation: 操作描述
        """
        try:
            # 根据操作类型重试相应的导出
            if "excel" in operation.lower():
                self.export_results("excel")
            elif "csv" in operation.lower():
                self.export_results("csv")
            else:
                # 默认重试Excel导出
                self.export_results("excel")
                
        except Exception as e:
            QMessageBox.critical(self, "重试失败", f"重试导出操作失败: {str(e)}")
    
    def update_export_button_status(self):
        """更新导出按钮状态"""
        try:
            # 检查是否有违例数据
            has_violations = bool(self.violations)
            
            # 检查用例信息是否完整
            has_complete_case_info = (
                self.case_info and 
                self.case_info.case_name and 
                self.case_info.corner
            )
            
            # 计算按钮启用状态
            enable_export = has_violations and has_complete_case_info
            
            # 更新导出按钮状态
            if hasattr(self, 'export_excel_button'):
                self.export_excel_button.setEnabled(enable_export)
                
                # 更新工具提示
                if enable_export:
                    self.export_excel_button.setToolTip("导出为Excel格式，支持丰富的格式和样式")
                else:
                    tooltip = "导出Excel文件\n"
                    if not has_violations:
                        tooltip += "• 需要先加载违例数据\n"
                    if not has_complete_case_info:
                        tooltip += "• 需要完整的用例信息（用例名称和Corner）"
                    self.export_excel_button.setToolTip(tooltip)
            
            if hasattr(self, 'export_csv_button'):
                self.export_csv_button.setEnabled(enable_export)
                
                # 更新工具提示
                if enable_export:
                    self.export_csv_button.setToolTip("导出为CSV格式，兼容性好，可用Excel打开")
                else:
                    tooltip = "导出CSV文件\n"
                    if not has_violations:
                        tooltip += "• 需要先加载违例数据\n"
                    if not has_complete_case_info:
                        tooltip += "• 需要完整的用例信息（用例名称和Corner）"
                    self.export_csv_button.setToolTip(tooltip)
            
            if hasattr(self, 'quick_export_button'):
                self.quick_export_button.setEnabled(enable_export)
                
                # 更新工具提示
                if enable_export:
                    self.quick_export_button.setToolTip("使用默认设置快速导出")
                else:
                    tooltip = "快速导出文件\n"
                    if not has_violations:
                        tooltip += "• 需要先加载违例数据\n"
                    if not has_complete_case_info:
                        tooltip += "• 需要完整的用例信息（用例名称和Corner）"
                    self.quick_export_button.setToolTip(tooltip)
            
            # 更新按钮样式以反映状态
            self._update_export_button_styles(enable_export)
            
        except Exception as e:
            print(f"更新导出按钮状态时发生错误: {str(e)}")
    
    def _update_export_button_styles(self, enabled: bool):
        """更新导出按钮样式
        
        Args:
            enabled: 是否启用
        """
        try:
            # 根据启用状态调整按钮样式
            if enabled:
                # 启用状态的样式已在setup_ui中定义
                pass
            else:
                # 可以在这里添加禁用状态的特殊样式
                pass
                
        except Exception as e:
            print(f"更新导出按钮样式时发生错误: {str(e)}")
    
    def _on_violation_confirmed(self, confirmed_violation: ViolationData):
        """违例确认完成事件处理
        
        Args:
            confirmed_violation: 确认后的违例数据
        """
        try:
            print(f"违例确认完成: NUM={confirmed_violation.num}, 状态={confirmed_violation.get_status_display()}")
            
            # 更新内部违例数据列表
            self._update_violation_in_list(confirmed_violation)
            
            # 更新违例列表显示
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.update_violation_status(confirmed_violation)
            
            # 显示确认成功提示
            confirmer = confirmed_violation.confirmer
            from .models import ConfirmationResult
            result_text = "无问题" if confirmed_violation.confirmation_result == ConfirmationResult.NO_ISSUE else "有问题"
            
            QMessageBox.information(
                self,
                "确认完成",
                f"违例 NUM {confirmed_violation.num} 确认完成\n"
                f"确认人: {confirmer}\n"
                f"确认结果: {result_text}"
            )
            
        except Exception as e:
            error_msg = f"处理违例确认时发生错误: {str(e)}"
            QMessageBox.critical(self, "错误", error_msg)
            print(f"违例确认处理错误: {error_msg}")
    
    def _on_confirmation_cancelled(self):
        """确认取消事件处理"""
        print("用户取消了违例确认")
        # 不需要特殊处理，对话框会自动关闭
    
    def _update_violation_in_list(self, updated_violation: ViolationData):
        """更新违例列表中的违例数据
        
        Args:
            updated_violation: 更新后的违例数据
        """
        # 在内部违例列表中查找并更新对应的违例
        for i, violation in enumerate(self.violations):
            if violation.num == updated_violation.num:
                self.violations[i] = updated_violation
                break
    
    def _validate_confirmation_data(self, violation: ViolationData) -> bool:
        """验证确认数据完整性
        
        Args:
            violation: 违例数据
            
        Returns:
            bool: 数据是否完整有效
        """
        # 检查确认人
        if not violation.confirmer or not violation.confirmer.strip():
            return False
        
        # 检查确认结果
        if not violation.confirmation_result:
            return False
        
        # 检查确认理由
        if not violation.reason or not violation.reason.strip():
            return False
        
        return True
    
    def get_confirmation_statistics(self) -> dict:
        """获取确认统计信息
        
        Returns:
            dict: 确认统计信息
        """
        from .models import ViolationStatus
        
        stats = {
            'total': len(self.violations),
            'pending': 0,
            'confirmed_ok': 0,
            'confirmed_issue': 0,
            'auto_confirmed': 0,
            'manual_confirmed': 0
        }
        
        for violation in self.violations:
            if violation.status == ViolationStatus.PENDING:
                stats['pending'] += 1
            elif violation.status == ViolationStatus.CONFIRMED_OK:
                stats['confirmed_ok'] += 1
            elif violation.status == ViolationStatus.CONFIRMED_ISSUE:
                stats['confirmed_issue'] += 1
            elif violation.status == ViolationStatus.AUTO_CONFIRMED:
                stats['auto_confirmed'] += 1
            
            # 统计手动确认数量
            if violation.is_confirmed() and not violation.is_auto_confirmed():
                stats['manual_confirmed'] += 1
        
        return stats
    
    def has_pending_violations(self) -> bool:
        """检查是否还有待确认的违例
        
        Returns:
            bool: 是否有待确认的违例
        """
        from .models import ViolationStatus
        return any(v.status == ViolationStatus.PENDING for v in self.violations)
    
    def get_pending_violation_count(self) -> int:
        """获取待确认违例数量
        
        Returns:
            int: 待确认违例数量
        """
        from .models import ViolationStatus
        return sum(1 for v in self.violations if v.status == ViolationStatus.PENDING)
    
    def confirm_all_pending_violations(self, confirmer: str, result_type: str, reason: str):
        """批量确认所有待确认的违例
        
        Args:
            confirmer: 确认人
            result_type: 确认结果类型 ("无问题" 或 "有问题")
            reason: 确认理由
        """
        try:
            from .models import ViolationStatus, ConfirmationResult
            
            # 确定确认结果
            if result_type == "无问题":
                result = ConfirmationResult.NO_ISSUE
            else:
                result = ConfirmationResult.HAS_ISSUE
            
            # 批量确认待确认的违例
            confirmed_count = 0
            for violation in self.violations:
                if violation.status == ViolationStatus.PENDING:
                    violation.set_manual_confirmation(confirmer, result, reason)
                    confirmed_count += 1
            
            # 更新违例列表显示
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.refresh_display()
            
            # 显示批量确认结果
            if confirmed_count > 0:
                QMessageBox.information(
                    self,
                    "批量确认完成",
                    f"成功确认了 {confirmed_count} 个违例\n"
                    f"确认人: {confirmer}\n"
                    f"确认结果: {result_type}"
                )
            else:
                QMessageBox.information(
                    self,
                    "批量确认",
                    "没有找到待确认的违例"
                )
            
        except Exception as e:
            error_msg = f"批量确认时发生错误: {str(e)}"
            QMessageBox.critical(self, "错误", error_msg)
            print(f"批量确认错误: {error_msg}")
    
    def _quick_export(self, format_type: str, include_all: bool):
        """快速导出功能
        
        Args:
            format_type: 导出格式类型 ("excel" 或 "csv")
            include_all: 是否包含所有违例（True）或仅已确认（False）
        """
        try:
            # 检查是否有违例数据
            if not self.violations:
                self._show_export_warning("没有可导出的违例数据", "请先加载时序违例文件")
                return
            
            # 检查用例信息完整性
            if not self.case_info or not self.case_info.case_name:
                self._show_export_warning("用例信息不完整", "请确保已正确加载文件并提取用例信息")
                return
            
            # 导入必要的模块
            from .export_manager import ExportManager
            from .models import ExportConfig
            from .exceptions import ExportError
            import os
            
            # 创建导出管理器
            export_manager = ExportManager()
            
            # 获取用例信息
            case_info = {}
            if self.case_info:
                case_info['case_name'] = self.case_info.case_name
                case_info['corner'] = self.case_info.corner
            
            # 生成默认文件名和路径
            default_filename = export_manager.get_default_filename(case_info, format_type)
            
            # 使用当前工作目录作为默认保存位置
            default_path = os.path.join(os.getcwd(), default_filename)
            
            # 创建导出配置
            try:
                config = ExportConfig(
                    format_type=format_type,
                    include_auto_confirmed=include_all,
                    only_confirmed=not include_all,
                    file_path=default_path
                )
            except ValueError as e:
                self._show_export_error("配置错误", f"导出配置无效: {str(e)}")
                return
            
            # 验证导出路径
            if not export_manager.validate_export_path(default_path):
                # 如果默认路径不可用，尝试用户文档目录
                import os
                user_docs = os.path.expanduser("~/Documents")
                if os.path.exists(user_docs):
                    default_path = os.path.join(user_docs, default_filename)
                    config.file_path = default_path
                    
                    if not export_manager.validate_export_path(default_path):
                        self._show_export_error(
                            "路径验证失败", 
                            f"无法写入默认路径，请使用标准导出功能选择其他位置"
                        )
                        return
                else:
                    self._show_export_error(
                        "路径验证失败", 
                        f"无法写入默认路径，请使用标准导出功能选择其他位置"
                    )
                    return
            
            # 显示进度对话框
            from PyQt5.QtWidgets import QProgressDialog
            from PyQt5.QtCore import Qt
            
            format_display = "Excel" if format_type == "excel" else "CSV"
            progress = QProgressDialog(f"正在快速导出{format_display}文件...", "取消", 0, 100, self)
            progress.setWindowTitle("快速导出")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setValue(20)
            
            # 执行导出
            try:
                progress.setValue(50)
                progress.setLabelText(f"正在生成{format_display}文件...")
                
                # 检查是否取消
                if progress.wasCanceled():
                    return
                
                result_path = export_manager.export_violations(self.violations, config, case_info)
                
                progress.setValue(90)
                progress.setLabelText("正在生成导出摘要...")
                
                # 检查是否取消
                if progress.wasCanceled():
                    return
                
                # 获取导出摘要
                summary = export_manager.get_export_summary(self.violations, config)
                
                progress.setValue(100)
                progress.close()
                
                # 显示快速导出成功信息
                self._show_quick_export_success(result_path, format_display, summary, include_all)
                
            except ExportError as e:
                progress.close()
                self._show_export_error("快速导出失败", str(e))
                
            except Exception as e:
                progress.close()
                error_details = self._get_error_details(e)
                self._show_export_error("快速导出异常", f"快速导出过程中发生未预期的错误:\n{error_details}")
                
        except Exception as e:
            error_details = self._get_error_details(e)
            self._show_export_error("快速导出系统错误", f"快速导出系统发生错误:\n{error_details}")

    
    def _enable_export_buttons(self, enabled: bool):
        """启用或禁用导出按钮
        
        Args:
            enabled: 是否启用按钮
        """
        try:
            if hasattr(self, 'export_excel_button'):
                self.export_excel_button.setEnabled(enabled)
            
            if hasattr(self, 'export_csv_button'):
                self.export_csv_button.setEnabled(enabled)
            
            if hasattr(self, 'quick_export_button'):
                self.quick_export_button.setEnabled(enabled)
                
        except Exception as e:
            print(f"启用/禁用导出按钮时发生错误: {str(e)}")
    
    def update_export_button_status(self):
        """更新导出按钮状态
        
        根据当前数据状态决定是否启用导出按钮
        """
        try:
            # 检查是否有违例数据和完整的用例信息
            has_data = bool(self.violations)
            has_case_info = bool(self.case_info and self.case_info.case_name)
            has_corner = bool(self.case_info and self.case_info.has_corner()) if self.case_info else False
            
            # 只有当有数据、用例信息和corner信息时才启用导出按钮
            should_enable = has_data and has_case_info and has_corner
            
            self._enable_export_buttons(should_enable)
            
            # 更新按钮提示信息
            if not should_enable:
                tooltip_reasons = []
                if not has_data:
                    tooltip_reasons.append("没有违例数据")
                if not has_case_info:
                    tooltip_reasons.append("缺少用例信息")
                if not has_corner:
                    tooltip_reasons.append("缺少Corner信息")
                
                tooltip = f"导出功能不可用: {', '.join(tooltip_reasons)}"
                
                if hasattr(self, 'export_excel_button'):
                    self.export_excel_button.setToolTip(tooltip)
                if hasattr(self, 'export_csv_button'):
                    self.export_csv_button.setToolTip(tooltip)
                if hasattr(self, 'quick_export_button'):
                    self.quick_export_button.setToolTip(tooltip)
            else:
                # 恢复原始提示信息
                if hasattr(self, 'export_excel_button'):
                    self.export_excel_button.setToolTip("导出为Excel格式，支持丰富的格式和样式")
                if hasattr(self, 'export_csv_button'):
                    self.export_csv_button.setToolTip("导出为CSV格式，兼容性好，可用Excel打开")
                if hasattr(self, 'quick_export_button'):
                    self.quick_export_button.setToolTip("使用默认设置快速导出")
                    
        except Exception as e:
            print(f"更新导出按钮状态时发生错误: {str(e)}")
    
    def validate_export_requirements(self) -> tuple:
        """验证导出要求
        
        Returns:
            tuple: (is_valid, error_message)
        """
        try:
            # 检查违例数据
            if not self.violations:
                return False, "没有可导出的违例数据，请先加载时序违例文件"
            
            # 检查用例信息
            if not self.case_info:
                return False, "缺少用例信息，请确保已正确加载文件"
            
            if not self.case_info.case_name:
                return False, "缺少用例名称信息"
            
            if not self.case_info.has_corner():
                return False, "缺少Corner信息，请选择或确认Corner"
            
            # 检查是否有可导出的数据
            exportable_count = len([v for v in self.violations if hasattr(v, 'is_exportable') and v.is_exportable()])
            if exportable_count == 0:
                return False, "没有可导出的违例数据"
            
            return True, ""
            
        except Exception as e:
            return False, f"验证导出要求时发生错误: {str(e)}"
    
    def get_export_preview_info(self) -> dict:
        """获取导出预览信息
        
        Returns:
            dict: 导出预览信息
        """
        try:
            stats = self.get_confirmation_statistics()
            
            preview_info = {
                'case_name': self.case_info.case_name if self.case_info else "未知",
                'corner': self.case_info.corner if self.case_info else "未知",
                'total_violations': stats['total'],
                'pending_violations': stats['pending'],
                'confirmed_violations': stats['confirmed_ok'] + stats['confirmed_issue'],
                'auto_confirmed_violations': stats['auto_confirmed'],
                'exportable_violations': len([v for v in self.violations if hasattr(v, 'is_exportable') and v.is_exportable()]),
                'has_pending': stats['pending'] > 0,
                'completion_rate': round((stats['confirmed_ok'] + stats['confirmed_issue'] + stats['auto_confirmed']) / max(stats['total'], 1) * 100, 1)
            }
            
            return preview_info
            
        except Exception as e:
            print(f"获取导出预览信息时发生错误: {str(e)}")
            return {}
    
    def closeEvent(self, event):
        """关闭事件处理"""
        try:
            # 检查是否还有待确认的违例
            if self.has_pending_violations():
                pending_count = self.get_pending_violation_count()
                reply = QMessageBox.question(
                    self,
                    "关闭确认",
                    f"还有 {pending_count} 个违例未确认，确定要关闭吗？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply != QMessageBox.Yes:
                    event.ignore()
                    return
            
            # 清理资源
            self.violations.clear()
            self.case_info = None
            
            # 调用父类关闭事件
            super().closeEvent(event)
            
        except Exception as e:
            print(f"关闭对话框时发生错误: {str(e)}")
            event.accept()  # 确保窗口能够关闭
    
    def on_reset_time_applied(self, reset_time_fs: int):
        """复位时间应用事件处理
        
        Args:
            reset_time_fs: 复位时间（飞秒）
        """
        try:
            self.reset_time_fs = reset_time_fs
            print(f"复位时间已设置: {reset_time_fs} fs")
            
            # 更新违例列表中的自动确认状态
            if hasattr(self, 'violation_list_widget') and self.violations:
                self.violation_list_widget.apply_reset_time_filter(reset_time_fs)
                
        except Exception as e:
            print(f"应用复位时间时发生错误: {str(e)}")
    
    def on_reset_time_cleared(self):
        """复位时间清除事件处理"""
        try:
            self.reset_time_fs = 0
            print("复位时间已清除")
            
            # 清除违例列表中的自动确认状态
            if hasattr(self, 'violation_list_widget') and self.violations:
                self.violation_list_widget.clear_reset_time_filter()
                
        except Exception as e:
            print(f"清除复位时间时发生错误: {str(e)}")
    
    def on_reset_time_validation_error(self, error_message: str):
        """复位时间验证错误事件处理
        
        Args:
            error_message: 错误信息
        """
        print(f"复位时间验证错误: {error_message}")
        QMessageBox.warning(self, "复位时间验证错误", error_message)
    
    def on_violation_selected(self, violation):
        """违例选择事件处理
        
        Args:
            violation: 选择的违例数据
        """
        try:
            print(f"违例已选择: NUM={violation.num}")
            # 可以在这里添加选择后的处理逻辑，比如显示详细信息
            
        except Exception as e:
            print(f"处理违例选择事件时发生错误: {str(e)}")
    
    def on_confirm_requested(self, violation):
        """违例确认请求事件处理
        
        Args:
            violation: 需要确认的违例数据
        """
        try:
            print(f"请求确认违例: NUM={violation.num}")
            
            # 导入确认对话框
            from .confirmation_dialog import ConfirmationDialog
            
            # 创建并显示确认对话框
            dialog = ConfirmationDialog(violation, self)
            if dialog.exec_() == dialog.Accepted:
                # 获取确认结果并更新违例数据
                confirmation_data = dialog.get_confirmation_data()
                
                # 更新违例状态
                violation.set_manual_confirmation(
                    confirmation_data['confirmer'],
                    confirmation_data['result'],
                    confirmation_data['reason']
                )
                
                # 刷新违例列表显示
                if hasattr(self, 'violation_list_widget'):
                    self.violation_list_widget.refresh_violation_display(violation)
                
                print(f"违例确认完成: NUM={violation.num}, 结果={confirmation_data['result'].value}")
                
        except Exception as e:
            print(f"处理违例确认请求时发生错误: {str(e)}")
            QMessageBox.critical(self, "确认错误", f"处理违例确认时发生错误:\n{str(e)}")
    
    def on_detail_requested(self, violation):
        """违例详情请求事件处理
        
        Args:
            violation: 需要查看详情的违例数据
        """
        try:
            print(f"请求查看违例详情: NUM={violation.num}")
            
            # 创建详情显示对话框
            self._show_violation_detail(violation)
            
        except Exception as e:
            print(f"处理违例详情请求时发生错误: {str(e)}")
            QMessageBox.critical(self, "详情显示错误", f"显示违例详情时发生错误:\n{str(e)}")
    
    def on_status_changed(self, violation):
        """违例状态变更事件处理
        
        Args:
            violation: 状态发生变更的违例数据
        """
        try:
            print(f"违例状态已变更: NUM={violation.num}, 新状态={violation.status.value}")
            
            # 更新导出按钮状态
            self.update_export_button_status()
            
        except Exception as e:
            print(f"处理违例状态变更事件时发生错误: {str(e)}")
    
    def _show_violation_detail(self, violation):
        """显示违例详情对话框
        
        Args:
            violation: 违例数据
        """
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit
            from PyQt5.QtCore import Qt
            
            dialog = QDialog(self)
            dialog.setWindowTitle(f"违例详情 - NUM {violation.num}")
            dialog.setModal(True)
            dialog.setMinimumSize(600, 400)
            
            layout = QVBoxLayout()
            
            # 标题
            title_label = QLabel(f"时序违例详细信息 (NUM: {violation.num})")
            title_label.setStyleSheet("font-size: 14px; font-weight: bold; margin-bottom: 15px;")
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)
            
            # 基本信息
            info_html = f"""
            <table style="width: 100%; border-collapse: collapse;">
                <tr style="background-color: #f8f9fa;">
                    <td style="padding: 8px; border: 1px solid #dee2e6; font-weight: bold;">字段</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6; font-weight: bold;">值</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">序号</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">{violation.num}</td>
                </tr>
                <tr style="background-color: #f8f9fa;">
                    <td style="padding: 8px; border: 1px solid #dee2e6;">层级路径</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">{violation.hier}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">原始时间</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">{violation.time_original}</td>
                </tr>
                <tr style="background-color: #f8f9fa;">
                    <td style="padding: 8px; border: 1px solid #dee2e6;">时间(飞秒)</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">{violation.time_fs:,}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">检查信息</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">{violation.check}</td>
                </tr>
                <tr style="background-color: #f8f9fa;">
                    <td style="padding: 8px; border: 1px solid #dee2e6;">状态</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">{violation.get_status_display()}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">确认人</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">{violation.confirmer or '未确认'}</td>
                </tr>
                <tr style="background-color: #f8f9fa;">
                    <td style="padding: 8px; border: 1px solid #dee2e6;">确认结果</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">{violation.confirmation_result.value if violation.confirmation_result else '未确认'}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">自动确认</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">{'是' if violation.auto_confirmed else '否'}</td>
                </tr>
            </table>
            """
            
            info_label = QLabel(info_html)
            info_label.setStyleSheet("margin: 10px 0;")
            layout.addWidget(info_label)
            
            # 确认理由
            if violation.reason:
                reason_label = QLabel("确认理由/解决方案:")
                reason_label.setStyleSheet("font-weight: bold; margin-top: 15px;")
                layout.addWidget(reason_label)
                
                reason_text = QTextEdit()
                reason_text.setPlainText(violation.reason)
                reason_text.setReadOnly(True)
                reason_text.setMaximumHeight(100)
                reason_text.setStyleSheet("background-color: #f8f9fa; border: 1px solid #dee2e6;")
                layout.addWidget(reason_text)
            
            # 按钮
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            
            close_button = QPushButton("关闭")
            close_button.clicked.connect(dialog.accept)
            close_button.setDefault(True)
            close_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)
            button_layout.addWidget(close_button)
            
            layout.addLayout(button_layout)
            dialog.setLayout(layout)
            dialog.exec_()
            
        except Exception as e:
            QMessageBox.critical(self, "详情显示错误", f"显示违例详情时发生错误:\n{str(e)}")    

    def validate_export_data(self) -> tuple:
        """验证导出数据的完整性和有效性
        
        Returns:
            tuple: (is_valid, error_message, warning_message)
        """
        try:
            # 基本验证
            is_valid, error_msg = self.validate_export_requirements()
            if not is_valid:
                return False, error_msg, ""
            
            # 数据质量检查
            warning_messages = []
            
            # 检查是否有大量待确认的违例
            stats = self.get_confirmation_statistics()
            if stats['pending'] > 0:
                pending_ratio = stats['pending'] / stats['total'] * 100
                if pending_ratio > 50:
                    warning_messages.append(f"有 {stats['pending']} 个违例({pending_ratio:.1f}%)尚未确认")
            
            # 检查是否有问题违例未解决
            if stats['confirmed_issue'] > 0:
                warning_messages.append(f"有 {stats['confirmed_issue']} 个违例标记为有问题，请确认是否已解决")
            
            # 检查数据完整性
            incomplete_violations = []
            for violation in self.violations:
                if violation.is_confirmed() and not violation.reason.strip():
                    incomplete_violations.append(violation.num)
            
            if incomplete_violations:
                if len(incomplete_violations) <= 5:
                    nums_str = ", ".join(map(str, incomplete_violations))
                    warning_messages.append(f"违例 {nums_str} 已确认但缺少确认理由")
                else:
                    warning_messages.append(f"有 {len(incomplete_violations)} 个已确认违例缺少确认理由")
            
            warning_message = "\n".join(warning_messages) if warning_messages else ""
            
            return True, "", warning_message
            
        except Exception as e:
            return False, f"验证导出数据时发生错误: {str(e)}", ""
    
    def show_export_validation_dialog(self, warning_message: str) -> bool:
        """显示导出验证对话框
        
        Args:
            warning_message: 警告信息
            
        Returns:
            bool: 用户是否选择继续导出
        """
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit
            from PyQt5.QtCore import Qt
            
            dialog = QDialog(self)
            dialog.setWindowTitle("导出数据验证")
            dialog.setModal(True)
            dialog.setMinimumSize(500, 300)
            
            layout = QVBoxLayout()
            
            # 警告图标和标题
            title_label = QLabel("⚠️ 导出数据验证警告")
            title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #856404; margin-bottom: 15px;")
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)
            
            # 警告信息
            info_label = QLabel("检测到以下问题，建议在导出前处理:")
            info_label.setStyleSheet("margin-bottom: 10px;")
            layout.addWidget(info_label)
            
            warning_text = QTextEdit()
            warning_text.setPlainText(warning_message)
            warning_text.setReadOnly(True)
            warning_text.setMaximumHeight(150)
            warning_text.setStyleSheet("""
                QTextEdit {
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 3px;
                    padding: 10px;
                    color: #856404;
                }
            """)
            layout.addWidget(warning_text)
            
            # 询问信息
            question_label = QLabel("是否继续导出？")
            question_label.setStyleSheet("font-weight: bold; margin-top: 15px;")
            layout.addWidget(question_label)
            
            # 按钮布局
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            
            # 取消按钮
            cancel_button = QPushButton("取消导出")
            cancel_button.clicked.connect(dialog.reject)
            cancel_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)
            button_layout.addWidget(cancel_button)
            
            # 继续按钮
            continue_button = QPushButton("继续导出")
            continue_button.clicked.connect(dialog.accept)
            continue_button.setDefault(True)
            continue_button.setStyleSheet("""
                QPushButton {
                    padding: 8px 15px;
                    background-color: #ffc107;
                    color: #212529;
                    border: none;
                    border-radius: 3px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #e0a800;
                }
            """)
            button_layout.addWidget(continue_button)
            
            layout.addLayout(button_layout)
            dialog.setLayout(layout)
            
            return dialog.exec_() == QDialog.Accepted
            
        except Exception as e:
            print(f"显示导出验证对话框时发生错误: {str(e)}")
            # 如果对话框显示失败，默认允许继续
            return True
    
    def enhance_export_error_handling(self, error: Exception, operation: str) -> str:
        """增强导出错误处理
        
        Args:
            error: 异常对象
            operation: 操作描述
            
        Returns:
            str: 增强的错误信息
        """
        try:
            error_type = type(error).__name__
            error_message = str(error)
            
            # 根据错误类型提供具体的解决建议
            if "Permission" in error_type or "permission" in error_message.lower():
                return f"""权限错误 - {operation}

错误详情: {error_message}

可能的解决方案:
1. 以管理员身份运行程序
2. 选择用户有写入权限的目录(如文档文件夹)
3. 检查目标文件是否被其他程序占用
4. 确保磁盘空间充足

建议操作:
• 关闭可能打开目标文件的程序(如Excel)
• 选择不同的文件名或路径
• 检查文件夹权限设置"""
            
            elif "FileNotFound" in error_type or "not found" in error_message.lower():
                return f"""文件未找到错误 - {operation}

错误详情: {error_message}

可能的解决方案:
1. 确保目标目录存在
2. 检查文件路径是否正确
3. 验证磁盘是否可访问

建议操作:
• 手动创建目标目录
• 选择已存在的目录
• 检查网络驱动器连接"""
            
            elif "openpyxl" in error_message.lower() or "excel" in error_message.lower():
                return f"""Excel库错误 - {operation}

错误详情: {error_message}

可能的解决方案:
1. 安装或更新openpyxl库: pip install openpyxl
2. 检查Python环境配置
3. 尝试使用CSV格式导出

建议操作:
• 在命令行运行: pip install --upgrade openpyxl
• 重启程序后重试
• 使用CSV格式作为替代方案"""
            
            elif "Memory" in error_type or "memory" in error_message.lower():
                return f"""内存错误 - {operation}

错误详情: {error_message}

可能的解决方案:
1. 关闭其他占用内存的程序
2. 分批导出数据
3. 重启程序释放内存

建议操作:
• 仅导出已确认的违例
• 使用CSV格式(内存占用更少)
• 重启程序后重试"""
            
            elif "Disk" in error_type or "space" in error_message.lower():
                return f"""磁盘空间错误 - {operation}

错误详情: {error_message}

可能的解决方案:
1. 清理磁盘空间
2. 选择其他磁盘位置
3. 压缩或删除不需要的文件

建议操作:
• 检查目标磁盘剩余空间
• 选择空间充足的磁盘
• 清理临时文件"""
            
            else:
                return f"""未知错误 - {operation}

错误类型: {error_type}
错误详情: {error_message}

通用解决方案:
1. 重试操作
2. 重启程序
3. 检查系统资源
4. 联系技术支持

建议操作:
• 记录错误信息
• 尝试不同的导出设置
• 检查系统日志"""
                
        except Exception as e:
            return f"处理错误信息时发生异常: {str(e)}\n原始错误: {str(error)}"
    
    def show_enhanced_export_error(self, title: str, error: Exception, operation: str):
        """显示增强的导出错误对话框
        
        Args:
            title: 错误标题
            error: 异常对象
            operation: 操作描述
        """
        try:
            enhanced_message = self.enhance_export_error_handling(error, operation)
            
            msg_box = QMessageBox(self)
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle("导出错误")
            msg_box.setText(title)
            msg_box.setDetailedText(enhanced_message)
            
            # 添加自定义按钮
            retry_button = msg_box.addButton("重试", QMessageBox.ActionRole)
            help_button = msg_box.addButton("帮助", QMessageBox.HelpRole)
            close_button = msg_box.addButton("关闭", QMessageBox.RejectRole)
            
            msg_box.setDefaultButton(retry_button)
            
            result = msg_box.exec_()
            
            # 处理按钮点击
            if msg_box.clickedButton() == retry_button:
                # 延迟重试，给用户时间解决问题
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(2000, lambda: self.export_results(
                    "excel" if "excel" in operation.lower() else "csv"
                ))
            elif msg_box.clickedButton() == help_button:
                self._show_export_help()
                
        except Exception as e:
            # 如果增强错误处理失败，显示基本错误信息
            QMessageBox.critical(self, title, f"导出失败:\n{str(error)}")
    
    def _retry_export_operation(self, operation: str):
        """重试导出操作
        
        Args:
            operation: 操作描述
        """
        try:
            # 根据操作类型重试相应的导出
            if "excel" in operation.lower():
                self.export_results("excel")
            elif "csv" in operation.lower():
                self.export_results("csv")
            else:
                QMessageBox.information(self, "重试", "请手动重新执行导出操作")
                
        except Exception as e:
            QMessageBox.critical(self, "重试失败", f"重试导出操作失败: {str(e)}")
    
    def _copy_error_to_clipboard(self, error: Exception, operation: str):
        """复制错误信息到剪贴板
        
        Args:
            error: 异常对象
            operation: 操作描述
        """
        try:
            from PyQt5.QtWidgets import QApplication
            import traceback
            
            error_text = f"时序违例确认工具 - 导出错误报告\n"
            error_text += f"时间: {self._get_current_time()}\n"
            error_text += f"操作: {operation}\n"
            error_text += f"错误类型: {type(error).__name__}\n"
            error_text += f"错误信息: {str(error)}\n\n"
            error_text += "堆栈跟踪:\n"
            error_text += traceback.format_exc()
            
            clipboard = QApplication.clipboard()
            clipboard.setText(error_text)
            
            QMessageBox.information(self, "复制成功", "错误信息已复制到剪贴板")
            
        except Exception as e:
            QMessageBox.warning(self, "复制失败", f"复制错误信息失败: {str(e)}")
    
    def closeEvent(self, event):
        """窗口关闭事件处理"""
        try:
            # 检查是否有未保存的更改
            if self.has_pending_violations():
                reply = QMessageBox.question(
                    self, 
                    "确认关闭", 
                    f"当前还有 {self.get_pending_violation_count()} 个违例未确认，确定要关闭吗？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply == QMessageBox.No:
                    event.ignore()
                    return
            
            # 清理资源
            self.cleanup_resources()
            
            # 接受关闭事件
            event.accept()
            
        except Exception as e:
            print(f"关闭窗口时发生错误: {str(e)}")
            event.accept()  # 即使出错也要关闭窗口
    
    def cleanup_resources(self):
        """清理资源"""
        try:
            # 清理违例数据
            self.violations = []
            self.case_info = None
            self.reset_time_fs = 0
            
            # 清理组件
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.clear()
            
            if hasattr(self, 'file_selection_widget'):
                self.file_selection_widget.clear()
            
            if hasattr(self, 'reset_time_widget'):
                self.reset_time_widget.clear()
                
        except Exception as e:
            print(f"清理资源时发生错误: {str(e)}")
    
    def get_dialog_state(self) -> dict:
        """获取对话框状态
        
        Returns:
            dict: 对话框状态信息
        """
        try:
            return {
                'has_violations': bool(self.violations),
                'violation_count': len(self.violations) if self.violations else 0,
                'has_case_info': bool(self.case_info),
                'case_name': self.case_info.case_name if self.case_info else None,
                'corner': self.case_info.corner if self.case_info else None,
                'reset_time_fs': self.reset_time_fs,
                'pending_count': self.get_pending_violation_count(),
                'confirmed_count': self.get_confirmation_statistics().get('confirmed_ok', 0) + 
                                 self.get_confirmation_statistics().get('confirmed_issue', 0),
                'auto_confirmed_count': self.get_confirmation_statistics().get('auto_confirmed', 0)
            }
        except Exception as e:
            print(f"获取对话框状态时发生错误: {str(e)}")
            return {} 
   
    def closeEvent(self, event):
        """窗口关闭事件处理"""
        try:
            # 清理资源
            self.cleanup_resources()
            
            # 调用父类的关闭事件
            super().closeEvent(event)
            
        except Exception as e:
            print(f"关闭窗口时发生错误: {str(e)}")
            event.accept()  # 强制关闭
    
    def cleanup_resources(self):
        """清理资源"""
        try:
            # 停止定时器
            if hasattr(self, 'data_change_timer'):
                self.data_change_timer.stop()
            
            if hasattr(self, 'state_sync_timer'):
                self.state_sync_timer.stop()
            
            # 断开信号连接
            self.disconnect_all_signals()
            
            print("资源清理完成")
            
        except Exception as e:
            print(f"清理资源时发生错误: {str(e)}")
    
    def disconnect_all_signals(self):
        """断开所有信号连接"""
        try:
            # 断开文件选择组件信号
            if hasattr(self, 'file_selection_widget'):
                self.file_selection_widget.file_selected.disconnect()
                self.file_selection_widget.case_info_extracted.disconnect()
                self.file_selection_widget.corner_selection_required.disconnect()
                self.file_selection_widget.validation_error.disconnect()
            
            # 断开复位时间组件信号
            if hasattr(self, 'reset_time_widget'):
                self.reset_time_widget.reset_time_applied.disconnect()
                self.reset_time_widget.reset_time_cleared.disconnect()
                self.reset_time_widget.validation_error.disconnect()
            
            # 断开违例列表组件信号
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.violation_selected.disconnect()
                self.violation_list_widget.confirm_requested.disconnect()
                self.violation_list_widget.detail_requested.disconnect()
                self.violation_list_widget.status_changed.disconnect()
            
            print("信号连接已断开")
            
        except Exception as e:
            print(f"断开信号连接时发生错误: {str(e)}")
    
    def get_component_status(self) -> dict:
        """获取组件状态信息
        
        Returns:
            dict: 组件状态信息
        """
        try:
            status = {
                'file_selected': bool(self.case_info),
                'violations_loaded': bool(self.violations),
                'reset_time_set': self.reset_time_fs > 0,
                'components_enabled': True
            }
            
            # 检查组件是否存在和启用
            if hasattr(self, 'file_selection_widget'):
                status['file_selection_enabled'] = self.file_selection_widget.isEnabled()
            
            if hasattr(self, 'reset_time_widget'):
                status['reset_time_enabled'] = self.reset_time_widget.isEnabled()
            
            if hasattr(self, 'violation_list_widget'):
                status['violation_list_enabled'] = self.violation_list_widget.isEnabled()
            
            return status
            
        except Exception as e:
            print(f"获取组件状态时发生错误: {str(e)}")
            return {}
    
    def validate_data_consistency(self) -> bool:
        """验证数据一致性
        
        Returns:
            bool: 数据是否一致
        """
        try:
            # 检查违例数据与组件显示是否一致
            if hasattr(self, 'violation_list_widget') and self.violations:
                widget_count = self.violation_list_widget.rowCount()
                data_count = len(self.violations)
                
                if widget_count != data_count:
                    print(f"数据不一致: 组件显示 {widget_count} 条，数据有 {data_count} 条")
                    return False
            
            # 检查用例信息是否一致
            if self.case_info and hasattr(self, 'file_selection_widget'):
                # 这里可以添加更多的一致性检查
                pass
            
            return True
            
        except Exception as e:
            print(f"验证数据一致性时发生错误: {str(e)}")
            return False
    
    def force_refresh_all_components(self):
        """强制刷新所有组件"""
        try:
            print("强制刷新所有组件...")
            
            # 刷新文件选择组件
            if hasattr(self, 'file_selection_widget') and hasattr(self.file_selection_widget, 'refresh'):
                self.file_selection_widget.refresh()
            
            # 刷新复位时间组件
            if hasattr(self, 'reset_time_widget') and hasattr(self.reset_time_widget, 'refresh'):
                self.reset_time_widget.refresh()
            
            # 刷新违例列表组件
            if hasattr(self, 'violation_list_widget'):
                self.violation_list_widget.refresh_display()
            
            # 更新界面状态
            self.update_interface_state()
            
            print("所有组件刷新完成")
            
        except Exception as e:
            print(f"强制刷新组件时发生错误: {str(e)}")
    
    def debug_component_interactions(self):
        """调试组件交互（开发用）"""
        try:
            print("=== 组件交互调试信息 ===")
            
            # 打印组件状态
            status = self.get_component_status()
            for key, value in status.items():
                print(f"{key}: {value}")
            
            # 打印数据状态
            print(f"违例数据数量: {len(self.violations)}")
            print(f"用例信息: {self.case_info}")
            print(f"复位时间: {self.reset_time_fs} FS")
            
            # 验证数据一致性
            consistent = self.validate_data_consistency()
            print(f"数据一致性: {consistent}")
            
            # 验证组件交互
            interactions_ok = self.validate_component_interactions()
            print(f"组件交互正常: {interactions_ok}")
            
            print("=== 调试信息结束 ===")
            
        except Exception as e:
            print(f"调试组件交互时发生错误: {str(e)}")