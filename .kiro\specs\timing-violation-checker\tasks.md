# Implementation Plan

- [x] 1. 创建基础项目结构和数据模型






  - 创建插件主文件和基础类结构
  - 实现ViolationData数据类和相关枚举
  - 创建异常类定义
  - _Requirements: 2.5, 8.2_

- [x] 2. 实现时间转换工具






  - [x] 2.1 创建TimeConverter类




    - 实现时间单位识别和转换逻辑(FS/PS/NS)
    - 实现to_femtoseconds静态方法
    - 实现format_time静态方法用于显示
    - 编写单元测试验证转换准确性
    - _Requirements: 2.3, 4.3_

  - [x] 2.2 实现时间比较功能


    - 实现复位时间比较逻辑
    - 处理时间转换异常情况
    - 添加时间格式验证
    - _Requirements: 4.3, 4.4_

- [x] 3. 实现日志文件解析器






  - [x] 3.1 创建ViolationParser类




    - 实现parse_file方法读取vio_summary.log
    - 实现按"----"分隔符解析违例块
    - 实现_parse_violation_block方法解析单个违例
    - 实现_extract_field方法提取键值对
    - _Requirements: 2.1, 2.2, 2.4_



  - [x] 3.2 添加解析错误处理







    - 处理文件不存在或无法读取的情况
    - 处理格式错误的违例条目
    - 记录解析错误并继续处理其他条目
    - 实现解析结果验证
    - _Requirements: 2.4, 1.3, 1.4_

- [-] 4. 实现用例信息提取器



  - [x] 4.1 创建CaseInfoExtractor类






    - 实现从文件路径提取case_name和corner
    - 处理{case_name}_{corner}格式的目录名
    - 处理仅有{case_name}的情况
    - 验证corner的合法性
    - _Requirements: 3.1, 3.2, 3.4_

  - [x] 4.2 实现corner选择逻辑









    - 提供合法corner列表(npg_f1_ssg到npg_f3_tt)
    - 实现corner下拉选择功能
    - 验证用户选择的corner
    - _Requirements: 3.3, 3.5_

- [x] 5. 创建主插件类





  - [x] 5.1 实现TimingViolationCheckerPlugin基础结构


    - 继承PluginBase并实现必要属性
    - 实现initialize方法集成到主窗口
    - 创建插件菜单项和响应事件
    - 实现cleanup方法清理资源
    - _Requirements: 8.1, 8.2_

  - [x] 5.2 集成插件到现有系统


    - 确保与现有插件管理系统兼容
    - 遵循现有GUI设计风格
    - 实现插件配置保存和加载
    - _Requirements: 8.2, 8.3_

- [x] 6. 实现文件选择组件





  - [x] 6.1 创建文件选择界面


    - 实现文件选择对话框
    - 验证选择的文件路径格式
    - 显示文件选择错误提示
    - 提取并显示用例信息
    - _Requirements: 1.1, 1.2, 1.3, 1.4_

  - [x] 6.2 集成用例信息显示


    - 显示提取的case_name
    - 实现corner下拉选择或显示
    - 处理corner选择验证
    - _Requirements: 3.1, 3.2, 3.3, 3.5_
-

- [x] 7. 实现违例列表显示组件




  - [x] 7.1 创建ViolationListWidget类


    - 继承QTableWidget实现表格显示
    - 设置表格列标题和格式
    - 实现load_violations方法加载数据
    - 添加状态颜色标识
    - _Requirements: 6.1, 6.4_

  - [x] 7.2 实现交互功能


    - 实现违例条目选择和点击事件
    - 添加确认按钮和响应逻辑
    - 实现状态更新和显示刷新
    - 添加详情查看功能
    - _Requirements: 6.2, 5.1, 6.5_

- [x] 8. 实现复位时间处理功能







  - [x] 8.1 创建复位时间输入界面






    - 实现复位时间输入框和验证
    - 添加应用按钮和响应逻辑
    - 显示时间格式提示
    - _Requirements: 4.1, 4.2_

  - [x] 8.2 实现自动确认逻辑


    - 比较违例时间与复位时间
    - 自动标记复位期间的违例
    - 设置自动确认的备注信息
    - 实现复位时间更改时的重新计算
    - _Requirements: 4.3, 4.4, 4.5_

- [x] 9. 实现违例确认对话框




  - [x] 9.1 创建ConfirmationDialog类


    - 设计确认对话框界面布局
    - 显示违例详细信息
    - 实现确认人输入和验证
    - 添加确认结果选择(无问题/有问题)
    - _Requirements: 5.2, 5.3, 5.4, 5.5_

  - [x] 9.2 实现确认逻辑处理


    - 验证确认信息完整性
    - 保存确认结果到数据模型
    - 更新违例列表显示状态
    - 处理确认取消操作
    - _Requirements: 5.6, 5.7_

- [-] 10. 实现导出功能




  - [x] 10.1 创建ExportManager类




    - 实现Excel格式导出
    - 实现CSV格式导出
    - 设置导出文件格式和列标题
    - 包含所有必要的违例信息
    - _Requirements: 7.1, 7.2, 7.3_

  - [x] 10.2 实现导出界面和错误处理



























    - 添加导出按钮和格式选择
    - 实现文件保存对话框
    - 显示导出成功提示和文件位置
    - 处理导出失败的错误提示
    - _Requirements: 7.4, 7.5_

- [-] 11. 创建主对话框界面









  - [x] 11.1 实现TimingViolationDialog类








    - 继承NonModalDialog创建主对话框
    - 设计整体界面布局和组件排列
    - 集成文件选择、违例列表、复位时间等组件
    - 实现对话框大小和样式设置
    - _Requirements: 8.1, 8.3_

  - [x] 11.2 实现组件间交互逻辑
















    - 连接各组件的信号和槽
    - 实现数据在组件间的传递
    - 处理用户操作的响应流程
    - 实现界面状态管理
    - _Requirements: 6.3, 8.4_

- [-] 12. 实现错误处理和用户体验优化






  - [x] 12.1 添加全面的错误处理




    - 实现自定义异常类的使用
    - 添加用户友好的错误提示对话框
    - 处理文件IO和解析异常
    - 实现错误日志记录
    - _Requirements: 1.3, 1.4, 2.4_

  - [ ] 12.2 优化用户体验


    - 添加进度指示器处理大文件
    - 实现响应式界面设计
    - 添加操作确认和撤销功能
    - 优化界面性能和内存使用
    - _Requirements: 8.3, 8.4, 8.5_

- [ ] 13. 编写测试用例
  - [ ] 13.1 创建单元测试
    - 测试ViolationParser的各种输入情况
    - 测试TimeConverter的时间转换准确性
    - 测试CaseInfoExtractor的路径解析
    - 测试数据模型的验证逻辑
    - _Requirements: 2.1, 2.2, 2.3, 3.1_

  - [ ] 13.2 创建集成测试
    - 测试插件加载和初始化
    - 测试完整的用户操作流程
    - 测试导出功能的正确性
    - 测试错误处理的有效性
    - _Requirements: 8.1, 8.2, 7.1, 7.2_

- [ ] 14. 完善文档和部署准备
  - [ ] 14.1 编写用户文档
    - 创建插件使用说明文档
    - 编写故障排除指南
    - 添加示例文件和使用场景
    - _Requirements: 8.1_

  - [ ] 14.2 最终测试和优化
    - 进行完整的功能测试
    - 性能测试和优化
    - 用户验收测试
    - 修复发现的问题
    - _Requirements: 8.3, 8.4, 8.5_