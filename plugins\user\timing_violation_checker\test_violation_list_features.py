"""
ViolationListWidget功能验证测试

验证违例列表组件的所有交互功能。
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch

# 添加插件路径到sys.path
plugin_path = os.path.dirname(os.path.abspath(__file__))
if plugin_path not in sys.path:
    sys.path.insert(0, plugin_path)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtTest import QTest

from models import ViolationData, ViolationStatus, ConfirmationResult
from violation_list_widget import ViolationListWidget


class TestViolationListWidget(unittest.TestCase):
    """ViolationListWidget功能测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置测试"""
        self.widget = ViolationListWidget()
        self.widget.show()  # 显示widget以确保功能正常
        self.test_violations = self._create_test_data()
        
        # 创建信号接收器
        self.violation_selected_received = []
        self.confirm_requested_received = []
        self.detail_requested_received = []
        self.status_changed_received = []
        
        # 连接信号
        self.widget.violation_selected.connect(self.violation_selected_received.append)
        self.widget.confirm_requested.connect(self.confirm_requested_received.append)
        self.widget.detail_requested.connect(self.detail_requested_received.append)
        self.widget.status_changed.connect(self.status_changed_received.append)
    
    def tearDown(self):
        """清理测试"""
        self.widget.close()
        self.widget = None
    
    def _create_test_data(self):
        """创建测试数据"""
        violations = []
        
        # 创建不同状态的测试违例
        configs = [
            (1, "tb_top.cpu.core0", "1500000FS", 1500000, "setup(posedge clk, data)", ViolationStatus.PENDING),
            (2, "tb_top.memory.ctrl", "2000000FS", 2000000, "hold(posedge clk, addr)", ViolationStatus.CONFIRMED_OK),
            (3, "tb_top.bus.arbiter", "500000FS", 500000, "setup(negedge rst_n, enable)", ViolationStatus.AUTO_CONFIRMED),
            (4, "tb_top.io.uart", "3000000FS", 3000000, "hold(posedge clk, req)", ViolationStatus.CONFIRMED_ISSUE),
            (5, "tb_top.cpu.cache", "1200000FS", 1200000, "setup(posedge clk, tag)", ViolationStatus.PENDING),
        ]
        
        for num, hier, time_orig, time_fs, check, status in configs:
            violation = ViolationData(
                num=num,
                hier=hier,
                time_original=time_orig,
                time_fs=time_fs,
                check=check,
                status=status
            )
            
            # 为已确认的违例设置确认信息
            if status == ViolationStatus.CONFIRMED_OK:
                violation.set_manual_confirmation(
                    confirmer="测试用户",
                    result=ConfirmationResult.NO_ISSUE,
                    reason="测试确认 - 无问题"
                )
            elif status == ViolationStatus.CONFIRMED_ISSUE:
                violation.set_manual_confirmation(
                    confirmer="测试用户",
                    result=ConfirmationResult.HAS_ISSUE,
                    reason="测试确认 - 有问题需要修复"
                )
            elif status == ViolationStatus.AUTO_CONFIRMED:
                violation.set_auto_confirmation("复位期间时序违例，可以忽略")
            
            violations.append(violation)
        
        return violations
    
    def test_load_violations(self):
        """测试加载违例数据"""
        # 加载数据
        self.widget.load_violations(self.test_violations)
        
        # 验证行数
        self.assertEqual(self.widget.rowCount(), len(self.test_violations))
        
        # 验证数据存储
        self.assertEqual(len(self.widget.get_all_violations()), len(self.test_violations))
        
        # 验证第一行数据
        first_violation = self.test_violations[0]
        num_item = self.widget.item(0, self.widget.COLUMN_NUM)
        self.assertEqual(int(num_item.text()), first_violation.num)
        
        hier_item = self.widget.item(0, self.widget.COLUMN_HIER)
        self.assertEqual(hier_item.text(), first_violation.hier)
    
    def test_status_colors(self):
        """测试状态颜色显示"""
        self.widget.load_violations(self.test_violations)
        
        # 检查不同状态的颜色
        for row in range(self.widget.rowCount()):
            status_item = self.widget.item(row, self.widget.COLUMN_STATUS)
            violation = self.test_violations[row]
            
            # 验证状态文本
            self.assertEqual(status_item.text(), violation.get_status_display())
            
            # 验证背景颜色已设置
            background_color = status_item.background()
            self.assertIsNotNone(background_color)
    
    def test_get_selected_violation(self):
        """测试获取选择的违例"""
        self.widget.load_violations(self.test_violations)
        
        # 初始状态没有选择
        self.assertIsNone(self.widget.get_selected_violation())
        
        # 选择第一行
        self.widget.selectRow(0)
        selected = self.widget.get_selected_violation()
        self.assertIsNotNone(selected)
        self.assertEqual(selected.num, self.test_violations[0].num)
    
    def test_violation_selection_signal(self):
        """测试违例选择信号"""
        self.widget.load_violations(self.test_violations)
        
        # 选择第一行
        self.widget.selectRow(0)
        
        # 处理事件
        QApplication.processEvents()
        
        # 验证信号发送
        self.assertTrue(len(self.violation_selected_received) > 0)
        selected_violation = self.violation_selected_received[-1]
        self.assertEqual(selected_violation.num, self.test_violations[0].num)
    
    def test_update_violation_status(self):
        """测试更新违例状态"""
        self.widget.load_violations(self.test_violations)
        
        # 获取第一个待确认的违例
        pending_violation = None
        for v in self.test_violations:
            if v.status == ViolationStatus.PENDING:
                pending_violation = v
                break
        
        self.assertIsNotNone(pending_violation)
        
        # 更新状态
        pending_violation.set_manual_confirmation(
            confirmer="测试更新",
            result=ConfirmationResult.NO_ISSUE,
            reason="状态更新测试"
        )
        
        # 更新显示
        self.widget.update_violation_status(pending_violation)
        
        # 验证状态变化信号
        QApplication.processEvents()
        self.assertTrue(len(self.status_changed_received) > 0)
        
        # 验证显示更新
        row = self.widget._find_violation_row(pending_violation)
        self.assertNotEqual(row, -1)
        
        status_item = self.widget.item(row, self.widget.COLUMN_STATUS)
        self.assertEqual(status_item.text(), "已确认-无问题")
        
        confirmer_item = self.widget.item(row, self.widget.COLUMN_CONFIRMER)
        self.assertEqual(confirmer_item.text(), "测试更新")
    
    def test_filter_by_status(self):
        """测试按状态过滤"""
        self.widget.load_violations(self.test_violations)
        
        # 过滤显示待确认的违例
        self.widget.filter_by_status(ViolationStatus.PENDING)
        
        # 计算应该显示的行数
        expected_count = sum(1 for v in self.test_violations if v.status == ViolationStatus.PENDING)
        visible_count = self.widget.get_visible_violation_count()
        
        self.assertEqual(visible_count, expected_count)
        
        # 显示所有违例
        self.widget.show_all_violations()
        visible_count = self.widget.get_visible_violation_count()
        self.assertEqual(visible_count, len(self.test_violations))
    
    def test_get_violations_by_status(self):
        """测试按状态获取违例"""
        self.widget.load_violations(self.test_violations)
        
        # 获取待确认的违例
        pending_violations = self.widget.get_pending_violations()
        expected_pending = [v for v in self.test_violations if v.status == ViolationStatus.PENDING]
        self.assertEqual(len(pending_violations), len(expected_pending))
        
        # 获取已确认的违例
        confirmed_violations = self.widget.get_confirmed_violations()
        expected_confirmed = [v for v in self.test_violations if v.is_confirmed()]
        self.assertEqual(len(confirmed_violations), len(expected_confirmed))
    
    def test_get_summary_info(self):
        """测试获取汇总信息"""
        self.widget.load_violations(self.test_violations)
        
        summary = self.widget.get_summary_info()
        
        # 验证总数
        self.assertEqual(summary['total_count'], len(self.test_violations))
        self.assertEqual(summary['visible_count'], len(self.test_violations))
        
        # 验证状态统计
        status_counts = {}
        for violation in self.test_violations:
            status_counts[violation.status] = status_counts.get(violation.status, 0) + 1
        
        self.assertEqual(summary['pending_count'], status_counts.get(ViolationStatus.PENDING, 0))
        self.assertEqual(summary['confirmed_ok_count'], status_counts.get(ViolationStatus.CONFIRMED_OK, 0))
        self.assertEqual(summary['confirmed_issue_count'], status_counts.get(ViolationStatus.CONFIRMED_ISSUE, 0))
        self.assertEqual(summary['auto_confirmed_count'], status_counts.get(ViolationStatus.AUTO_CONFIRMED, 0))
    
    def test_select_violation_by_num(self):
        """测试根据NUM选择违例"""
        self.widget.load_violations(self.test_violations)
        
        # 选择存在的违例
        target_num = self.test_violations[2].num
        result = self.widget.select_violation_by_num(target_num)
        self.assertTrue(result)
        
        # 验证选择结果
        selected = self.widget.get_selected_violation()
        self.assertIsNotNone(selected)
        self.assertEqual(selected.num, target_num)
        
        # 选择不存在的违例
        result = self.widget.select_violation_by_num(999)
        self.assertFalse(result)
    
    def test_export_visible_data(self):
        """测试导出可见数据"""
        self.widget.load_violations(self.test_violations)
        
        # 导出所有数据
        exported_data = self.widget.export_visible_data()
        self.assertEqual(len(exported_data), len(self.test_violations))
        
        # 验证导出数据格式
        first_export = exported_data[0]
        first_violation = self.test_violations[0]
        
        self.assertEqual(first_export['num'], first_violation.num)
        self.assertEqual(first_export['hier'], first_violation.hier)
        self.assertEqual(first_export['time_original'], first_violation.time_original)
        self.assertEqual(first_export['status'], first_violation.get_status_display())
        
        # 过滤后导出
        self.widget.filter_by_status(ViolationStatus.PENDING)
        filtered_data = self.widget.export_visible_data()
        expected_count = sum(1 for v in self.test_violations if v.status == ViolationStatus.PENDING)
        self.assertEqual(len(filtered_data), expected_count)
    
    def test_clear_table(self):
        """测试清空表格"""
        self.widget.load_violations(self.test_violations)
        
        # 验证数据已加载
        self.assertEqual(self.widget.rowCount(), len(self.test_violations))
        
        # 清空表格
        self.widget.clear_table()
        
        # 验证表格已清空
        self.assertEqual(self.widget.rowCount(), 0)
        self.assertEqual(len(self.widget.get_all_violations()), 0)
    
    def test_refresh_display(self):
        """测试刷新显示"""
        self.widget.load_violations(self.test_violations)
        
        # 修改数据
        original_count = len(self.test_violations)
        self.test_violations.append(ViolationData(
            num=99,
            hier="test.refresh",
            time_original="1000000FS",
            time_fs=1000000,
            check="test refresh"
        ))
        
        # 刷新显示（这里只是测试方法调用，实际刷新需要重新加载数据）
        self.widget.refresh_display()
        
        # 验证原始数据仍然存在
        self.assertEqual(self.widget.rowCount(), original_count)


def run_interactive_test():
    """运行交互式测试"""
    print("开始交互式功能测试...")
    
    app = QApplication(sys.argv)
    
    # 创建测试组件
    widget = ViolationListWidget()
    widget.setWindowTitle("ViolationListWidget 交互测试")
    widget.resize(1000, 600)
    
    # 创建测试数据
    test_violations = []
    configs = [
        (1, "tb_top.cpu.core0", "1500000FS", 1500000, "setup(posedge clk, data)", ViolationStatus.PENDING),
        (2, "tb_top.memory.ctrl", "2000000FS", 2000000, "hold(posedge clk, addr)", ViolationStatus.CONFIRMED_OK),
        (3, "tb_top.bus.arbiter", "500000FS", 500000, "setup(negedge rst_n, enable)", ViolationStatus.AUTO_CONFIRMED),
        (4, "tb_top.io.uart", "3000000FS", 3000000, "hold(posedge clk, req)", ViolationStatus.CONFIRMED_ISSUE),
    ]
    
    for num, hier, time_orig, time_fs, check, status in configs:
        violation = ViolationData(
            num=num,
            hier=hier,
            time_original=time_orig,
            time_fs=time_fs,
            check=check,
            status=status
        )
        
        if status == ViolationStatus.CONFIRMED_OK:
            violation.set_manual_confirmation("测试用户", ConfirmationResult.NO_ISSUE, "测试确认")
        elif status == ViolationStatus.CONFIRMED_ISSUE:
            violation.set_manual_confirmation("测试用户", ConfirmationResult.HAS_ISSUE, "测试问题")
        elif status == ViolationStatus.AUTO_CONFIRMED:
            violation.set_auto_confirmation()
        
        test_violations.append(violation)
    
    # 连接信号进行测试
    def on_violation_selected(violation):
        print(f"选择违例: NUM={violation.num}")
    
    def on_confirm_requested(violation):
        print(f"请求确认: NUM={violation.num}")
        # 模拟确认
        violation.set_manual_confirmation("交互测试", ConfirmationResult.NO_ISSUE, "交互测试确认")
        widget.update_violation_status(violation)
    
    def on_detail_requested(violation):
        print(f"查看详情: NUM={violation.num}")
    
    def on_status_changed(violation):
        print(f"状态变化: NUM={violation.num} -> {violation.get_status_display()}")
    
    widget.violation_selected.connect(on_violation_selected)
    widget.confirm_requested.connect(on_confirm_requested)
    widget.detail_requested.connect(on_detail_requested)
    widget.status_changed.connect(on_status_changed)
    
    # 加载数据
    widget.load_violations(test_violations)
    
    # 显示组件
    widget.show()
    
    print("交互测试窗口已打开，可以进行以下测试:")
    print("1. 点击违例行进行选择")
    print("2. 点击确认按钮进行确认")
    print("3. 双击行查看详情")
    print("4. 右键点击显示上下文菜单")
    
    # 运行应用
    app.exec_()


if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'interactive':
        run_interactive_test()
    else:
        unittest.main()