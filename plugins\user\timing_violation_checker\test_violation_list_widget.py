"""
ViolationListWidget测试文件

测试违例列表显示组件的功能和交互。
"""

import sys
import os
from typing import List

# 添加插件路径到sys.path
plugin_path = os.path.dirname(os.path.abspath(__file__))
if plugin_path not in sys.path:
    sys.path.insert(0, plugin_path)

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                             QHBoxLayout, QWidget, QPushButton, QLabel,
                             QMessageBox, QGroupBox, QCheckBox, QComboBox)
from PyQt5.QtCore import Qt

from models import ViolationData, ViolationStatus, ConfirmationResult
from violation_list_widget import ViolationListWidget


class ViolationListTestWindow(QMainWindow):
    """违例列表测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("ViolationListWidget 功能测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建测试数据
        self.test_violations = self._create_test_data()
        
        # 设置界面
        self.setup_ui()
        
        # 加载测试数据
        self.violation_list.load_violations(self.test_violations)
    
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        
        # 添加标题
        title_label = QLabel("ViolationListWidget 功能测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # 创建违例列表组件
        self.violation_list = ViolationListWidget()
        main_layout.addWidget(self.violation_list)
        
        # 创建控制面板
        control_panel = self._create_control_panel()
        main_layout.addWidget(control_panel)
        
        # 连接信号
        self._connect_signals()
        
        # 创建状态显示
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("padding: 5px; background-color: #f8f9fa; border: 1px solid #dee2e6;")
        main_layout.addWidget(self.status_label)
    
    def _create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QGroupBox("测试控制")
        layout = QHBoxLayout(panel)
        
        # 状态过滤
        filter_label = QLabel("状态过滤:")
        layout.addWidget(filter_label)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems([
            "显示全部",
            "待确认",
            "已确认-无问题", 
            "已确认-有问题",
            "自动确认"
        ])
        self.status_filter.currentTextChanged.connect(self._on_filter_changed)
        layout.addWidget(self.status_filter)
        
        layout.addStretch()
        
        # 测试按钮
        test_confirm_btn = QPushButton("测试确认功能")
        test_confirm_btn.clicked.connect(self._test_confirm_function)
        layout.addWidget(test_confirm_btn)
        
        test_update_btn = QPushButton("测试状态更新")
        test_update_btn.clicked.connect(self._test_status_update)
        layout.addWidget(test_update_btn)
        
        refresh_btn = QPushButton("刷新显示")
        refresh_btn.clicked.connect(self.violation_list.refresh_display)
        layout.addWidget(refresh_btn)
        
        summary_btn = QPushButton("显示汇总")
        summary_btn.clicked.connect(self._show_summary)
        layout.addWidget(summary_btn)
        
        return panel
    
    def _create_test_data(self) -> List[ViolationData]:
        """创建测试数据"""
        test_data = []
        
        # 创建不同状态的测试违例
        violations_config = [
            (1, "tb_top.cpu.core0.reg_file", "1523423FS", 1523423, "setup(posedge clk, data)", ViolationStatus.PENDING),
            (2, "tb_top.cpu.core1.alu", "2000000FS", 2000000, "hold(posedge clk, addr)", ViolationStatus.CONFIRMED_OK),
            (3, "tb_top.memory.ctrl", "500000FS", 500000, "setup(negedge rst_n, enable)", ViolationStatus.AUTO_CONFIRMED),
            (4, "tb_top.bus.arbiter", "3500000FS", 3500000, "hold(posedge clk, req)", ViolationStatus.CONFIRMED_ISSUE),
            (5, "tb_top.cpu.cache.tag", "1200000FS", 1200000, "setup(posedge clk, tag_valid)", ViolationStatus.PENDING),
            (6, "tb_top.io.uart.tx", "800000FS", 800000, "hold(negedge clk, tx_data)", ViolationStatus.PENDING),
            (7, "tb_top.cpu.fpu.add", "4000000FS", 4000000, "setup(posedge clk, operand_a)", ViolationStatus.CONFIRMED_OK),
            (8, "tb_top.memory.ddr.ctrl", "300000FS", 300000, "hold(posedge clk, cas_n)", ViolationStatus.AUTO_CONFIRMED),
        ]
        
        for num, hier, time_orig, time_fs, check, status in violations_config:
            violation = ViolationData(
                num=num,
                hier=hier,
                time_original=time_orig,
                time_fs=time_fs,
                check=check,
                status=status
            )
            
            # 为已确认的违例设置确认信息
            if status == ViolationStatus.CONFIRMED_OK:
                violation.set_manual_confirmation(
                    confirmer="测试用户",
                    result=ConfirmationResult.NO_ISSUE,
                    reason="经分析确认为正常时序，无需处理"
                )
            elif status == ViolationStatus.CONFIRMED_ISSUE:
                violation.set_manual_confirmation(
                    confirmer="测试用户",
                    result=ConfirmationResult.HAS_ISSUE,
                    reason="需要调整时钟约束，已提交修复方案"
                )
            elif status == ViolationStatus.AUTO_CONFIRMED:
                violation.set_auto_confirmation("复位期间时序违例，可以忽略")
            
            test_data.append(violation)
        
        return test_data
    
    def _connect_signals(self):
        """连接信号"""
        # 连接违例列表信号
        self.violation_list.violation_selected.connect(self._on_violation_selected)
        self.violation_list.confirm_requested.connect(self._on_confirm_requested)
        self.violation_list.detail_requested.connect(self._on_detail_requested)
        self.violation_list.status_changed.connect(self._on_status_changed)
    
    def _on_violation_selected(self, violation: ViolationData):
        """处理违例选择事件"""
        self.status_label.setText(f"选择了违例 NUM={violation.num}: {violation.hier}")
        print(f"违例选择: NUM={violation.num}, Hier={violation.hier}")
    
    def _on_confirm_requested(self, violation: ViolationData):
        """处理确认请求事件"""
        self.status_label.setText(f"请求确认违例 NUM={violation.num}")
        
        # 模拟确认对话框
        reply = QMessageBox.question(
            self, 
            "确认违例",
            f"是否确认违例 NUM={violation.num}?\n"
            f"Hier: {violation.hier}\n"
            f"Time: {violation.time_original}\n"
            f"Check: {violation.check}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if reply == QMessageBox.Yes:
            # 模拟确认操作
            violation.set_manual_confirmation(
                confirmer="测试用户",
                result=ConfirmationResult.NO_ISSUE,
                reason="测试确认 - 无问题"
            )
            
            # 更新显示
            self.violation_list.update_violation_status(violation)
            
            self.status_label.setText(f"违例 NUM={violation.num} 已确认")
            print(f"违例确认: NUM={violation.num} 已确认为无问题")
    
    def _on_detail_requested(self, violation: ViolationData):
        """处理详情查看请求事件"""
        self.status_label.setText(f"查看违例 NUM={violation.num} 详情")
        
        # 显示详情对话框
        detail_text = f"""违例详细信息:
        
NUM: {violation.num}
Hier: {violation.hier}
Time (原始): {violation.time_original}
Time (飞秒): {violation.time_fs}
Check: {violation.check}
状态: {violation.get_status_display()}
确认人: {violation.confirmer if violation.confirmer else '未确认'}
理由: {violation.reason if violation.reason else '无'}
自动确认: {'是' if violation.auto_confirmed else '否'}
"""
        
        QMessageBox.information(self, f"违例详情 - NUM {violation.num}", detail_text)
        print(f"查看详情: NUM={violation.num}")
    
    def _on_status_changed(self, violation: ViolationData):
        """处理状态变化事件"""
        self.status_label.setText(f"违例 NUM={violation.num} 状态已更新为: {violation.get_status_display()}")
        print(f"状态变化: NUM={violation.num}, 新状态={violation.get_status_display()}")
    
    def _on_filter_changed(self, filter_text: str):
        """处理过滤变化事件"""
        if filter_text == "显示全部":
            self.violation_list.show_all_violations()
        elif filter_text == "待确认":
            self.violation_list.filter_by_status(ViolationStatus.PENDING)
        elif filter_text == "已确认-无问题":
            self.violation_list.filter_by_status(ViolationStatus.CONFIRMED_OK)
        elif filter_text == "已确认-有问题":
            self.violation_list.filter_by_status(ViolationStatus.CONFIRMED_ISSUE)
        elif filter_text == "自动确认":
            self.violation_list.filter_by_status(ViolationStatus.AUTO_CONFIRMED)
        
        visible_count = self.violation_list.get_visible_violation_count()
        self.status_label.setText(f"过滤器: {filter_text}, 显示 {visible_count} 条违例")
        print(f"过滤变化: {filter_text}, 可见违例数: {visible_count}")
    
    def _test_confirm_function(self):
        """测试确认功能"""
        # 获取第一个待确认的违例
        pending_violations = self.violation_list.get_pending_violations()
        if not pending_violations:
            QMessageBox.information(self, "测试", "没有待确认的违例")
            return
        
        violation = pending_violations[0]
        
        # 模拟确认
        violation.set_manual_confirmation(
            confirmer="自动测试",
            result=ConfirmationResult.NO_ISSUE,
            reason="自动测试确认 - 功能正常"
        )
        
        # 更新显示
        self.violation_list.update_violation_status(violation)
        
        QMessageBox.information(self, "测试", f"已确认违例 NUM={violation.num}")
        print(f"测试确认: NUM={violation.num}")
    
    def _test_status_update(self):
        """测试状态更新功能"""
        # 获取所有违例
        all_violations = self.violation_list.get_all_violations()
        if not all_violations:
            QMessageBox.information(self, "测试", "没有违例数据")
            return
        
        # 随机选择一个违例进行状态更新
        import random
        violation = random.choice(all_violations)
        
        # 切换状态
        if violation.status == ViolationStatus.PENDING:
            violation.set_manual_confirmation(
                confirmer="状态测试",
                result=ConfirmationResult.NO_ISSUE,
                reason="状态更新测试"
            )
        elif violation.status == ViolationStatus.CONFIRMED_OK:
            violation.set_manual_confirmation(
                confirmer="状态测试",
                result=ConfirmationResult.HAS_ISSUE,
                reason="状态更新测试 - 发现问题"
            )
        else:
            # 重置为待确认状态
            violation.status = ViolationStatus.PENDING
            violation.confirmer = ""
            violation.reason = ""
            violation.confirmation_result = None
            violation.auto_confirmed = False
        
        # 更新显示
        self.violation_list.update_violation_status(violation)
        
        QMessageBox.information(self, "测试", 
                              f"违例 NUM={violation.num} 状态已更新为: {violation.get_status_display()}")
        print(f"状态更新测试: NUM={violation.num}, 新状态={violation.get_status_display()}")
    
    def _show_summary(self):
        """显示汇总信息"""
        summary = self.violation_list.get_summary_info()
        
        summary_text = f"""违例汇总信息:

总违例数: {summary['total_count']}
可见违例数: {summary['visible_count']}

状态统计:
- 待确认: {summary['pending_count']}
- 已确认-无问题: {summary['confirmed_ok_count']}
- 已确认-有问题: {summary['confirmed_issue_count']}
- 自动确认: {summary['auto_confirmed_count']}
"""
        
        QMessageBox.information(self, "违例汇总", summary_text)
        print("汇总信息:", summary)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = ViolationListTestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()